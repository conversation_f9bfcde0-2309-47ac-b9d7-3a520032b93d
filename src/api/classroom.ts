import API from "@/lib/axios";
import { AxiosError } from "axios";

// Types for classroom information
export interface Room {
  id: number;
  name: string;
  status: string;
  school_id: number;
  floor_id: number;
  floor_info?: {
    id: number;
    name: string;
  };
  building_info?: {
    id: number;
    name: string;
  };
}

export interface Class {
  id: number;
  name: string;
  status: string;
  room_id?: number;
}

export interface FloorInfo {
  id: number;
  name: string;
}

export interface BuildingInfo {
  id: number;
  name: string;
}

export interface RoomDetail {
  room_info: {
    id: number;
    name: string;
    status: string;
    school_id: number;
    floor_info: FloorInfo;
    building_info: BuildingInfo;
  };
  classes: Class[];
}

export interface RoomFormData {
  name: string;
  floor_id: number;
  school_id: number;
}

export interface AddRoomData {
  id?: number;
  name: string;
  floor_id: number;
  school_id: number;
}

export interface ListRoomParams {
  token: string;
  name?: string;
  floor_id?: number;
  building_id?: number;
  limit?: number;
  page?: number;
}

export interface PaginationMeta {
  page: number;
  limit: number;
  totalData: number;
  totalPage: number;
}

export interface PaginationMetaOptional {
  page?: number;
  limit?: number;
  totalData?: number;
  totalPage?: number;
}

interface RoomListApiResponse {
  data: Room[];
  code: number;
  mess: string;
  meta?: PaginationMeta;
}

interface RoomDetailApiResponse {
  data: RoomDetail;
  code: number;
  mess: string;
}

interface AddRoomApiResponse {
  data: Room;
  code: number;
  mess: string;
}

interface DeleteRoomApiResponse {
  code: number;
  mess: string;
}

/**
 * List rooms with optional search filters and pagination
 */
export const listRoomAPI = async (params: ListRoomParams): Promise<RoomListApiResponse> => {
  try {
    const response = await API.post<RoomListApiResponse>("/listRoomApi", params);
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi lấy danh sách phòng học");
  }
};

/**
 * Get room details by ID including floor info, building info, and classes
 */
export const detailRoomAPI = async (token: string, id: number): Promise<RoomDetailApiResponse> => {
  try {
    const response = await API.post<RoomDetailApiResponse>("/detailRoomApi", {
      token,
      id
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi lấy chi tiết phòng học");
  }
};

/**
 * Add or update room
 */
export const addRoomAPI = async (
  token: string, 
  roomData: AddRoomData
): Promise<AddRoomApiResponse> => {
  try {
    const response = await API.post<AddRoomApiResponse>("/addRoomApi", {
      token,
      ...roomData
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi lưu phòng học");
  }
};

/**
 * Delete room
 */
export const deleteRoomAPI = async (
  token: string, 
  id: number
): Promise<DeleteRoomApiResponse> => {
  try {
    const response = await API.post<DeleteRoomApiResponse>("/deleteRoomApi", {
      token,
      id
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi xóa phòng học");
  }
};
