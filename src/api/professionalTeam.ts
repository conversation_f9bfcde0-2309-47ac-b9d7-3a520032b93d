import API from "@/lib/axios";
import { AxiosError } from "axios";

// Types for professional team information
export interface ProfessionalTeam {
  id: number;
  name: string;
  school_id: number;
}

export interface Teacher {
  id: number;
  name: string;
  email: string;
  phone: string;
  status: string;
  avatar?: string;
}

export interface ProfessionalTeamDetail {
  professional_team_info: ProfessionalTeam;
  teachers: Teacher[];
}

export interface ProfessionalTeamFormData {
  name: string;
}

export interface AddProfessionalTeamData {
  id?: number;
  name: string;
}

// Teacher assignment interfaces
export interface TeacherAssignment {
  teacher_id: number;
  teacher_name: string;
  professional_team_id: number;
  professional_team_name: string;
}

export interface AddTeacherToProfessionalTeamRequest {
  token: string;
  id_professional_team: number;
  id_teacher: number;
}

export interface RemoveTeacherFromProfessionalTeamRequest {
  token: string;
  id_teacher: number;
}

export interface ListProfessionalTeamParams {
  token: string;
  id?: number;
  name?: string;
  limit?: number;
  page?: number;
}

export interface PaginationMeta {
  page: number;
  limit: number;
  totalData: number;
  totalPage: number;
}

export interface PaginationMetaOptional {
  page?: number;
  limit?: number;
  totalData?: number;
  totalPage?: number;
}

interface ProfessionalTeamListApiResponse {
  data: ProfessionalTeam[];
  code: number;
  mess: string;
  meta?: PaginationMeta;
  totalData?: PaginationMeta;
}

interface ProfessionalTeamDetailApiResponse {
  data: ProfessionalTeamDetail;
  code: number;
  mess: string;
}

interface AddProfessionalTeamApiResponse {
  data: ProfessionalTeam;
  code: number;
  mess: string;
}

interface DeleteProfessionalTeamApiResponse {
  code: number;
  mess: string;
}

interface AddTeacherToProfessionalTeamApiResponse {
  data: TeacherAssignment;
  code: number;
  mess: string;
}

interface RemoveTeacherFromProfessionalTeamApiResponse {
  code: number;
  mess: string;
}

/**
 * List professional teams with optional search filters and pagination
 */
export const listProfessionalTeamAPI = async (params: ListProfessionalTeamParams): Promise<ProfessionalTeamListApiResponse> => {
  try {
    const response = await API.post<ProfessionalTeamListApiResponse>("/listProfessional_teamApi", params);
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi lấy danh sách tổ chuyên môn");
  }
};

/**
 * Get professional team details by ID
 */
export const detailProfessionalTeamAPI = async (token: string, id: number): Promise<ProfessionalTeamDetailApiResponse> => {
  try {
    const response = await API.post<ProfessionalTeamDetailApiResponse>("/detailProfessionalTeamApi", {
      token,
      id
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi lấy chi tiết tổ chuyên môn");
  }
};

/**
 * Add or update professional team
 */
export const addProfessionalTeamAPI = async (
  token: string, 
  teamData: AddProfessionalTeamData
): Promise<AddProfessionalTeamApiResponse> => {
  try {
    const response = await API.post<AddProfessionalTeamApiResponse>("/addProfessionalTeamApi", {
      token,
      ...teamData
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi lưu tổ chuyên môn");
  }
};

/**
 * Delete professional team
 */
export const deleteProfessionalTeamAPI = async (
  token: string, 
  id: number
): Promise<DeleteProfessionalTeamApiResponse> => {
  try {
    const response = await API.post<DeleteProfessionalTeamApiResponse>("/deleteProfessionalTeamApi", {
      token,
      id
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi xóa tổ chuyên môn");
  }
};

/**
 * Add teacher to professional team
 */
export const addTeacherToProfessionalTeamAPI = async (
  request: AddTeacherToProfessionalTeamRequest
): Promise<AddTeacherToProfessionalTeamApiResponse> => {
  try {
    const response = await API.post<AddTeacherToProfessionalTeamApiResponse>("/addTeacherToProfessionalTeamApi", request);
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi thêm giáo viên vào tổ chuyên môn");
  }
};

/**
 * Remove teacher from professional team
 */
export const removeTeacherFromProfessionalTeamAPI = async (
  request: RemoveTeacherFromProfessionalTeamRequest
): Promise<RemoveTeacherFromProfessionalTeamApiResponse> => {
  try {
    const response = await API.post<RemoveTeacherFromProfessionalTeamApiResponse>("/deleteTeacherFromProfessionalTeamApi", request);
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi xóa giáo viên khỏi tổ chuyên môn");
  }
};
