import axios, { AxiosError } from "axios";
import API from "@/lib/axios";
import { getAuthToken } from "@/utils/getAuthToken";

const CAMERA_API_TOKEN = getAuthToken();

// Types for nested information
export interface BuildingInfo {
  id: number;
  name: string;
}

export interface FloorInfo {
  id: number;
  name: string;
}

export interface RoomInfo {
  id: number;
  name: string;
}

// Types for camera information
export interface Camera {
  id: number;
  name: string;
  mac_address: string;
  ip_address: string | null;
  status: "active" | "lock" | "offline" | "maintenance" | "error" | string;
  room_id: number;
  school_id: number;
  building_id: number | null;
  floor_id?: number;
  stream_url: string | null;
  building_info?: BuildingInfo;
  floor_info?: FloorInfo;
  room_info?: RoomInfo;
}

export interface CameraFormData {
  name: string;
  room_id: number;
  mac_address: string;
  ip_address: string;
}

export interface AddCameraData {
  id?: number;
  name: string;
  room_id: number;
  mac_address: string;
  ip_address: string;
}

export interface ListCameraParams {
  token: string;
  id?: number;
  name?: string;
  mac_address?: string;
  ip_address?: string;
  search?: string;
  building_id?: number;
  floor_id?: number;
  room_id?: number;
  status?: string;
  limit?: number;
  page?: number;
}

export interface PaginationMeta {
  page: number;
  limit: number;
  totalData: number;
  totalPage: number;
}

export interface CameraPaginationData {
  page: number;
  limit: number;
  totalData: number;
  totalPage: number;
}

interface CameraListApiResponse {
  data: Camera[];
  code: number;
  mess: string;
  meta: any[];
  totalData: CameraPaginationData;
}

interface CameraDetailApiResponse {
  data: Camera;
  code: number;
  mess: string;
  meta: any[];
}

interface AddCameraApiResponse {
  data: Camera;
  code: number;
  mess: string;
  meta: any[];
  totalData: number;
}

interface DeleteCameraApiResponse {
  code: number;
  mess: string;
  meta: any[];
}

/**
 * Validate MAC address format (XX:XX:XX:XX:XX:XX or XX-XX-XX-XX-XX-XX)
 */
export const validateMacAddress = (macAddress: string): boolean => {
  const macRegex = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;
  return macRegex.test(macAddress);
};

/**
 * Validate IPv4 address format
 */
export const validateIpAddress = (ipAddress: string): boolean => {
  const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  return ipRegex.test(ipAddress);
};

/**
 * List cameras with optional search filters and pagination
 */
export const listCameraAPI = async (params: Partial<ListCameraParams>): Promise<CameraListApiResponse> => {
  try {
    const requestData = {
      token: CAMERA_API_TOKEN,
      ...params,
      limit: params.limit || 20,
      page: params.page || 1,
    };

    const response = await API.post<CameraListApiResponse>(
      "/listCameraApi",
      requestData
    );

    // Handle API response codes
    if (response.data.code !== 1) {
      throw new Error(response.data.mess || "Lỗi từ API camera");
    }

    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi lấy danh sách camera");
  }
};

/**
 * Get camera details by ID
 */
export const detailCameraAPI = async (id: number): Promise<CameraDetailApiResponse> => {
  try {
    const response = await API.post<CameraDetailApiResponse>(
      "/detailCameraApi",
      {
        token: CAMERA_API_TOKEN,
        id
      }
    );

    if (response.data.code !== 1) {
      throw new Error(response.data.mess || "Lỗi từ API camera");
    }

    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi lấy chi tiết camera");
  }
};

/**
 * Add or update camera
 */
export const addCameraAPI = async (
  cameraData: AddCameraData
): Promise<AddCameraApiResponse> => {
  try {
    const response = await API.post<AddCameraApiResponse>(
      "/addCameraApi",
      {
        token: CAMERA_API_TOKEN,
        ...cameraData
      }
    );

    if (response.data.code !== 1) {
      throw new Error(response.data.mess || "Lỗi từ API camera");
    }

    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi lưu camera");
  }
};

/**
 * Delete camera
 */
export const deleteCameraAPI = async (
  id: number
): Promise<DeleteCameraApiResponse> => {
  try {
    const response = await API.post<DeleteCameraApiResponse>(
      "/deleteCameraApi",
      {
        token: CAMERA_API_TOKEN,
        id
      }
    );

    if (response.data.code !== 1) {
      throw new Error(response.data.mess || "Lỗi từ API camera");
    }

    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi xóa camera");
  }
};
