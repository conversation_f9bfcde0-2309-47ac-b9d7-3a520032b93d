import API from "@/lib/axios";
import { AxiosError } from "axios";

// Types for floor information
export interface Floor {
  id: number;
  name: string;
  building_id: number;
  school_id: number;
  building_info?: {
    id: number;
    name: string;
  };
}

export interface Room {
  id: number;
  name: string;
  status: string;
  school_id: number;
  floor_id: number;
  floor_info: {
    id: number;
    name: string;
  };
}

export interface BuildingInfo {
  id: number;
  name: string;
}

export interface FloorDetail {
  floor_info: Floor;
  building_info: BuildingInfo;
  rooms: Room[];
}

export interface FloorFormData {
  name: string;
  building_id: number;
}

export interface AddFloorData {
  id?: number;
  name: string;
  building_id: number;
}

export interface ListFloorParams {
  token: string;
  name?: string;
  building_id?: number;
  limit?: number;
  page?: number;
}

export interface PaginationMeta {
  page: number;
  limit: number;
  totalData: number;
  totalPage: number;
}

export interface PaginationMetaOptional {
  page?: number;
  limit?: number;
  totalData?: number;
  totalPage?: number;
}

interface FloorListApiResponse {
  data: Floor[];
  code: number;
  mess: string;
  meta?: PaginationMeta;
}

interface FloorDetailApiResponse {
  data: FloorDetail;
  code: number;
  mess: string;
}

interface AddFloorApiResponse {
  data: Floor;
  code: number;
  mess: string;
}

interface DeleteFloorApiResponse {
  code: number;
  mess: string;
}

/**
 * List floors with optional search filters and pagination
 */
export const listFloorAPI = async (params: ListFloorParams): Promise<FloorListApiResponse> => {
  try {
    const response = await API.post<FloorListApiResponse>("/listFloorApi", params);
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi lấy danh sách tầng");
  }
};

/**
 * Get floor details by ID including building info and rooms
 */
export const detailFloorAPI = async (token: string, id: number): Promise<FloorDetailApiResponse> => {
  try {
    const response = await API.post<FloorDetailApiResponse>("/detailFloorApi", {
      token,
      id
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi lấy chi tiết tầng");
  }
};

/**
 * Add or update floor
 */
export const addFloorAPI = async (
  token: string, 
  floorData: AddFloorData
): Promise<AddFloorApiResponse> => {
  try {
    const response = await API.post<AddFloorApiResponse>("/addFloorApi", {
      token,
      ...floorData
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi lưu tầng");
  }
};

/**
 * Delete floor
 */
export const deleteFloorAPI = async (
  token: string, 
  id: number
): Promise<DeleteFloorApiResponse> => {
  try {
    const response = await API.post<DeleteFloorApiResponse>("/deleteFloorApi", {
      token,
      id
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi xóa tầng");
  }
};
