import API from "@/lib/axios";
import { AxiosError } from "axios";

// Types for account information
export interface AccountInfo {
  id: string;
  name: string;
  avatar?: string;
  phone: string;
  email: string;
  status: string;
  school_id: string;
  created_at: string;
  last_login: string;
  grant_permission: string[];
}

export interface UpdateAccountData {
  name: string;
  email: string;
  phone: string;
  password?: string;
}

export interface ChangePasswordData {
  current_password: string;
  new_password: string;
  password_confirmation: string;
}

export interface ContractInfo {
  school_id: string;
  school_name: string;
  contract_expiry_date: string;
  days_remaining: number;
  status: string;
}

interface AccountApiResponse {
  data: AccountInfo;
  code: number;
  mess: string;
}

interface UpdateAccountApiResponse {
  data: AccountInfo;
  code: number;
  mess: string;
}

interface ChangePasswordApiResponse {
  data: AccountInfo;
  code: number;
  mess: string;
}

interface ContractApiResponse {
  data: ContractInfo;
  code: number;
  mess: string;
}

/**
 * Fetch account information
 */
export const getAccountInfoAPI = async (token: string): Promise<AccountApiResponse> => {
  try {
    const response = await API.post<AccountApiResponse>("/getInfoMyAdminAPI", {
      token: token
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi lấy thông tin tài khoản");
  }
};

/**
 * Update account information
 */
export const updateAccountInfoAPI = async (
  token: string, 
  updateData: UpdateAccountData
): Promise<UpdateAccountApiResponse> => {
  try {
    const response = await API.post<UpdateAccountApiResponse>("/saveInfoAdminAPI", {
      token: token,
      name: updateData.name,
      email: updateData.email,
      phone: updateData.phone,
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi cập nhật thông tin tài khoản");
  }
};

/**
 * Change password
 */
export const changePasswordAPI = async (
  token: string, 
  passwordData: ChangePasswordData
): Promise<ChangePasswordApiResponse> => {
  try {
    const response = await API.post<ChangePasswordApiResponse>("/changePasswordForAdminAPI", {
      token: token,
      current_password: passwordData.current_password,
      new_password: passwordData.new_password,
      password_confirmation: passwordData.password_confirmation
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi đổi mật khẩu");
  }
};

/**
 * Check contract expiry information
 */
export const checkContractExpiryAPI = async (token: string): Promise<ContractApiResponse> => {
  try {
    const response = await API.post<ContractApiResponse>("/checkContractExpiryAPI", {
      token: token
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi kiểm tra hợp đồng");
  }
};
