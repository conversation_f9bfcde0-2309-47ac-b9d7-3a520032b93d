import axios, { AxiosError } from "axios";
import API from "@/lib/axios";

// Types for attendance management
export interface AttendanceRecord {
  id: number;
  student_id: number;
  student_name: string;
  student_code: string;
  student_phone: string;
  class_id: number;
  class_name: string;
  class_code: string;
  schedule_id: number;
  schedule_name: string;
  attendance_time: string;
  attendance_type: "auto" | "manual";
  camera_id?: number;
  camera_name?: string;
  location: string;
  note?: string;
  created_at: string;
  updated_at: string;
}

export interface AttendanceStats {
  total_present: number;
  total_absent: number;
  total_late: number;
  attendance_rate: number;
}

export interface AttendancePaginationData {
  page: number;
  limit: number;
  totalData: number;
  totalPage: number;
}

// Manual attendance creation data
export interface ManualAttendanceData {
  student_id: number;
  schedule_id: number;
  location?: string;
  note?: string;
}

// Filter parameters for attendance list
export interface AttendanceFilters {
  start_date?: string;
  end_date?: string;
  class_id?: string;
  schedule_id?: string;
  attendance_type?: "auto" | "manual" | "";
  camera_id?: string;
  location?: string;
  student_search?: string;
  page?: number;
  limit?: number;
}

// Schedule/Session data
export interface Schedule {
  id: number;
  name: string;
  start_time: string;
  end_time: string;
  type: string;
}

// Attendance Schedule Management Types
export interface AttendanceSchedule {
  id: number;
  schedule_name: string;
  start_time: string;
  end_time: string;
  days_of_week: string; // comma-separated: "1,2,3,4,5"
  attendance_type: "check_in" | "check_out" | "both";
  is_active: boolean;
  class_id?: number;
  class_name?: string;
  class_code?: string;
  created_at: string;
  updated_at: string;
}

export interface AttendanceScheduleFormData {
  schedule_name: string;
  start_time: string;
  end_time: string;
  days_of_week: number[]; // Array of day numbers [1,2,3,4,5]
  attendance_type: "check_in" | "check_out" | "both";
  is_active: boolean;
  class_id?: number;
}

export interface AttendanceScheduleFilters {
  class_id?: string;
  is_active?: boolean;
  day_of_week?: number; // 0=Sunday, 1=Monday, etc.
  attendance_type?: "check_in" | "check_out" | "both" | "";
  search?: string; // Search by schedule name
  page?: number;
  limit?: number;
}

export interface AttendanceSchedulePaginationData {
  page: number;
  limit: number;
  totalData: number;
  totalPage: number;
}

// Camera data
export interface Camera {
  id: number;
  name: string;
  location: string;
  status: "active" | "inactive";
}

// API Response types
export interface ManualAttendanceApiResponse {
  data: AttendanceRecord;
  code: number;
  mess: string;
}

export interface AttendanceListApiResponse {
  data: AttendanceRecord[];
  totalData: AttendancePaginationData;
  stats: AttendanceStats;
  code: number;
  mess: string;
}

export interface DeleteAttendanceApiResponse {
  data: {
    deleted_record: AttendanceRecord;
  };
  code: number;
  mess: string;
}

export interface ScheduleListApiResponse {
  data: Schedule[];
  code: number;
  mess: string;
}

export interface CameraListApiResponse {
  data: Camera[];
  code: number;
  mess: string;
}

// Attendance Schedule API Response types
export interface AttendanceScheduleListApiResponse {
  data: AttendanceSchedule[];
  totalData: AttendanceSchedulePaginationData;
  code: number;
  mess: string;
}

export interface AttendanceScheduleApiResponse {
  data: AttendanceSchedule;
  code: number;
  mess: string;
}

export interface DeleteAttendanceScheduleApiResponse {
  data: {
    deleted_schedule: AttendanceSchedule;
  };
  code: number;
  mess: string;
}

/**
 * Create manual attendance record
 */
export const manualAttendanceAPI = async (
  token: string,
  attendanceData: ManualAttendanceData
): Promise<ManualAttendanceApiResponse> => {
  try {
    const response = await API.post<ManualAttendanceApiResponse>("/manualAttendanceAPI", {
      token,
      ...attendanceData
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi tạo điểm danh thủ công");
  }
};

/**
 * Get attendance records with filtering
 */
export const listAttendedStudentsAPI = async (
  token: string,
  filters: AttendanceFilters = {}
): Promise<AttendanceListApiResponse> => {
  try {
    const params = {
      token,
      page: 1,
      limit: 50,
      ...filters
    };

    const response = await API.post<AttendanceListApiResponse>("/listAttendedStudentsAPI", params);
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi tải danh sách điểm danh");
  }
};

/**
 * Delete attendance record
 */
export const deleteAttendanceAPI = async (
  token: string,
  attendanceId: number
): Promise<DeleteAttendanceApiResponse> => {
  try {
    const response = await API.post<DeleteAttendanceApiResponse>("/deleteAttendanceAPI", {
      token,
      attendance_id: attendanceId
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi xóa bản ghi điểm danh");
  }
};

/**
 * Get list of schedules/sessions
 */
export const listSchedulesAPI = async (
  token: string
): Promise<ScheduleListApiResponse> => {
  try {
    const response = await API.post<ScheduleListApiResponse>("/listSchedulesAPI", {
      token
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi tải danh sách lịch học");
  }
};

/**
 * Get list of cameras
 */
export const listCamerasAPI = async (
  token: string
): Promise<CameraListApiResponse> => {
  try {
    const response = await API.post<CameraListApiResponse>("/listCamerasAPI", {
      token
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi tải danh sách camera");
  }
};

/**
 * Get list of attendance schedules with filtering
 */
export const listAttendanceSchedulesAPI = async (
  token: string,
  filters: AttendanceScheduleFilters = {}
): Promise<AttendanceScheduleListApiResponse> => {
  try {
    const params = {
      token,
      page: 1,
      limit: 50,
      ...filters
    };

    const response = await API.post<AttendanceScheduleListApiResponse>("/listAttendanceSchedulesAPI", params);
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi tải danh sách lịch điểm danh");
  }
};

/**
 * Add or update attendance schedule
 */
export const addAttendanceScheduleAPI = async (
  token: string,
  scheduleData: AttendanceScheduleFormData & { id?: number }
): Promise<AttendanceScheduleApiResponse> => {
  try {
    // Convert days_of_week array to comma-separated string
    const requestData = {
      token,
      ...scheduleData,
      days_of_week: scheduleData.days_of_week.join(','),
      class_id: scheduleData.class_id || undefined, // Send undefined for school-wide schedules
    };

    const response = await API.post<AttendanceScheduleApiResponse>("/addAttendanceScheduleAPI", requestData);
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi lưu lịch điểm danh");
  }
};

/**
 * Get attendance schedule details
 */
export const detailAttendanceScheduleAPI = async (
  token: string,
  scheduleId: number
): Promise<AttendanceScheduleApiResponse> => {
  try {
    const response = await API.post<AttendanceScheduleApiResponse>("/detailAttendanceScheduleAPI", {
      token,
      schedule_id: scheduleId
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi tải chi tiết lịch điểm danh");
  }
};

/**
 * Delete attendance schedule
 */
export const deleteAttendanceScheduleAPI = async (
  token: string,
  scheduleId: number
): Promise<DeleteAttendanceScheduleApiResponse> => {
  try {
    const response = await API.post<DeleteAttendanceScheduleApiResponse>("/deleteAttendanceScheduleAPI", {
      token,
      schedule_id: scheduleId
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi xóa lịch điểm danh");
  }
};
