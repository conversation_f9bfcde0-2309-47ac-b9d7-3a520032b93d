import axios, { AxiosError } from "axios";
import { getAuthToken } from "@/utils/getAuthToken";
import API from "@/lib/axios";

// Types for class information
export interface Class {
  id: number;
  name: string;
  code: string;
  id_semester: number;
  teacher_id: number;
  room_id: number;
  school_id: number;
  semester_info?: {
    id: number;
    name: string;
    code: string;
    start_date: string;
    end_date: string;
  };
  teacher_info?: {
    id: number;
    name: string;
    email: string;
    phone: string;
  };
  room_info?: {
    id: number;
    name: string;
    floor_id: number;
  };
}

export interface ClassFormData {
  name: string;
  code: string;
  id_semester: number;
  teacher_id: number;
  room_id: number;
  school_id: number;
}

export interface AddClassData {
  id?: number;
  name: string;
  code: string;
  id_semester: number;
  teacher_id: number;
  room_id: number;
  school_id: number;
}

export interface ListClassParams {
  token: string;
  id?: number;
  semester_name?: string;
  name?: string;
  limit?: number;
  page?: number;
}

export interface PaginationMeta {
  page: number;
  limit: number;
  totalData: number;
  totalPage: number;
}

export interface ClassPaginationData {
  page: number;
  limit: number;
  totalData: number;
  totalPage: number;
}

interface ClassListApiResponse {
  data: Class[];
  code: number;
  mess: string;
  meta: any[];
  totalData: ClassPaginationData;
}

interface ClassDetailApiResponse {
  data: Class;
  code: number;
  mess: string;
  meta: any[];
}

interface AddClassApiResponse {
  data: Class;
  code: number;
  mess: string;
  meta: any[];
}

interface DeleteClassApiResponse {
  code: number;
  mess: string;
  meta: any[];
}

/**
 * List classes with optional search filters and pagination
 */
export const listClassAPI = async (params: Partial<ListClassParams>): Promise<ClassListApiResponse> => {
  try {
    const requestData = {
      ...params,
      limit: params.limit || 20,
      page: params.page || 1,
    };

    const response = await API.post<ClassListApiResponse>(
      "/listClasseAPIForAdmin",
      requestData
    );

    // Handle API response codes
    if (response.data.code !== 1) {
      throw new Error(response.data.mess || "Lỗi từ API lớp học");
    }

    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi lấy danh sách lớp học");
  }
};

/**
 * Get class details by ID
 */
export const detailClassAPI = async (token: string, id: number): Promise<ClassDetailApiResponse> => {
  try {
    const response = await API.post<ClassDetailApiResponse>(
      "/detailClassAPIForAdmin",
      {
        token,
        id
      }
    );

    if (response.data.code !== 1) {
      throw new Error(response.data.mess || "Lỗi từ API lớp học");
    }

    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi lấy chi tiết lớp học");
  }
};

/**
 * Add or update class
 */
export const addClassAPI = async (
  token: string,
  classData: AddClassData
): Promise<AddClassApiResponse> => {
  try {
    const response = await API.post<AddClassApiResponse>(
      "/addClassAPIForAdmin",
      {
        token,
        ...classData
      }
    );

    if (response.data.code !== 1) {
      throw new Error(response.data.mess || "Lỗi từ API lớp học");
    }

    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi lưu lớp học");
  }
};

/**
 * Delete class
 */
export const deleteClassAPI = async (
  token: string,
  id: number
): Promise<DeleteClassApiResponse> => {
  try {
    const response = await API.post<DeleteClassApiResponse>(
      "/deleteClassAPIForAdmin",
      {
        token,
        id
      }
    );

    if (response.data.code !== 1) {
      throw new Error(response.data.mess || "Lỗi từ API lớp học");
    }

    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi xóa lớp học");
  }
};