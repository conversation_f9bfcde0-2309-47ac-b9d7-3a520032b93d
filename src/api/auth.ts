import API from "@/lib/axios";
import { AxiosError } from "axios";
import { LoginCredentials, User } from "@/types/auth";
import Cookies from 'universal-cookie';

// Đ<PERSON>nh nghĩa kiểu dữ liệu cho phản hồi API đăng nhập
interface ApiResponse {
  data: User;
  code: number;
  mess: string;
}

export const loginSchoolAPI = async (
  credentials: LoginCredentials
): Promise<ApiResponse> => {
  try {
    const response = await API.post<ApiResponse>(
      "/loginAdminAPI",
      credentials
    );
    if (response.data.code === 1 && response.data.data && response.data.data.token) {
      const cookies = new Cookies(null, { path: '/' }); // Khởi tạo cookies cho client-side
      cookies.set('accessTokenSchool', response.data.data.token, {
        path: '/', // <PERSON>ie có thể truy cập từ mọi path
        maxAge: 30 * 24 * 60 * 60, // Thời gian sống của cookie: 30 ngày (tính bằng giây)
        // secure: process.env.NODE_ENV === 'production', // Chỉ gửi cookie qua HTTPS ở môi trường production
        // sameSite: 'lax', // Giúp chống lại tấn công CSRF
      });
    }

    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    // Nếu API trả về lỗi có cấu trúc { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    // Lỗi chung từ Axios hoặc lỗi mạng
    throw new Error(error.message || "Lỗi không xác định khi đăng nhập");
  }
};

export const getSchoolInfoAPI = async (token: string): Promise<ApiResponse> => {
  try {
    const response = await API.post<ApiResponse>(`/getSchoolInfoAPI`, {
     token: token
    });
    return response.data;
  }  catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    // Nếu API trả về lỗi có cấu trúc { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    // Lỗi chung từ Axios hoặc lỗi mạng
    throw new Error(error.message || "Lỗi không xác định khi đăng nhập");
  }
};
