import API from "@/lib/axios";
import { AxiosError } from "axios";

// Types for school information
export interface SchoolInfo {
  id: string;
  name: string;
  code: string;
  logo?: string;
  address: string;
  status: string;
  created_at: string;
  updated_at: string;
  contract_info: {
    expiry_date: string;
    status: string;
    message: string;
    days_remaining: number;
  };
}

export interface UpdateSchoolData {
  name?: string;
  code?: string;
  logo?: string | File;
  address?: string;
}

interface SchoolApiResponse {
  data: SchoolInfo;
  code: number;
  mess: string;
}

interface UpdateSchoolApiResponse {
  data: SchoolInfo;
  code: number;
  mess: string;
}

/**
 * Fetch school information
 */
export const getSchoolInfoAPI = async (token: string): Promise<SchoolApiResponse> => {
  try {
    const response = await API.post<SchoolApiResponse>("/getSchoolInfoAPI", {
      token: token
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    // If API returns error with structure { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    // General error from Axios or network error
    throw new Error(error.message || "Lỗi không xác định khi lấy thông tin trường học");
  }
};

/**
 * Update school information
 * Supports both regular data and file uploads for logo
 */
export const updateSchoolInfoAPI = async (
  token: string,
  updateData: UpdateSchoolData
): Promise<UpdateSchoolApiResponse> => {
  try {
    // Check if logo is a file (for file upload) or string (for URL)
    const hasFileUpload = updateData.logo instanceof File;

    if (hasFileUpload) {
      // Use FormData for file uploads
      const formData = new FormData();
      formData.append('token', token);

      // Add all text fields
      Object.entries(updateData).forEach(([key, value]) => {
        if (value !== undefined && value !== null && !(value instanceof File)) {
          formData.append(key, value.toString());
        }
      });

      // Add logo file if it exists
      if (updateData.logo instanceof File) {
        formData.append('logo', updateData.logo);
      }

      const response = await API.post<UpdateSchoolApiResponse>("/updateSchoolInfoAPI", formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } else {
      // Use regular JSON for URL-based logo updates
      const response = await API.post<UpdateSchoolApiResponse>("/updateSchoolInfoAPI", {
        token: token,
        ...updateData
      });
      return response.data;
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    // If API returns error with structure { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    // General error from Axios or network error
    throw new Error(error.message || "Lỗi không xác định khi cập nhật thông tin trường học");
  }
};
