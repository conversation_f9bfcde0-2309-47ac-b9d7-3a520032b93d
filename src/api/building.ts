import API from "@/lib/axios";
import { AxiosError } from "axios";

// Types for building information
export interface Building {
  id: number;
  name: string;
  school_id: number;
}

export interface Room {
  id: number;
  name: string;
  status: string;
  floor_id?: number;
}

export interface Floor {
  id: number;
  name: string;
  building_id: number;
  rooms: Room[];
}

export interface BuildingDetail {
  building_info: Building;
  floors: Floor[];
}

export interface BuildingFormData {
  name: string;
}

export interface AddBuildingData {
  id?: number;
  name: string;
}

export interface FloorFormData {
  name: string;
  building_id: number;
}

export interface AddFloorData {
  id?: number;
  name: string;
  building_id: number;
}

export interface RoomFormData {
  name: string;
  status: string;
  floor_id: number;
}

export interface AddRoomData {
  id?: number;
  name: string;
  status: string;
  floor_id: number;
}

export interface ListBuildingParams {
  token: string;
  name?: string;
  limit?: number;
  page?: number;
}

export interface PaginationMeta {
  page: number;
  limit: number;
  totalData: number;
  totalPage: number;
}

export interface PaginationMetaOptional {
  page?: number;
  limit?: number;
  totalData?: number;
  totalPage?: number;
}

interface BuildingListApiResponse {
  data: Building[];
  code: number;
  mess: string;
  meta?: PaginationMeta;
  totalData?: PaginationMeta;
}

interface BuildingDetailApiResponse {
  data: BuildingDetail;
  code: number;
  mess: string;
}

interface AddBuildingApiResponse {
  data: Building;
  code: number;
  mess: string;
}

interface DeleteBuildingApiResponse {
  code: number;
  mess: string;
}

// Floor API Response Types
interface AddFloorApiResponse {
  code: number;
  mess: string;
  data?: Floor;
}

interface DeleteFloorApiResponse {
  code: number;
  mess: string;
  data?: any;
}

// Room API Response Types
interface AddRoomApiResponse {
  code: number;
  mess: string;
  data?: Room;
}

interface DeleteRoomApiResponse {
  code: number;
  mess: string;
  data?: any;
}

/**
 * List buildings with optional search filters and pagination
 */
export const listBuildingAPI = async (params: ListBuildingParams): Promise<BuildingListApiResponse> => {
  try {
    const response = await API.post<BuildingListApiResponse>("/listBuildingApi", params);
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi lấy danh sách tòa nhà");
  }
};

/**
 * Get building details by ID including floors and rooms
 */
export const detailBuildingAPI = async (token: string, id: number): Promise<BuildingDetailApiResponse> => {
  try {
    const response = await API.post<BuildingDetailApiResponse>("/detailBuildingApi", {
      token,
      id
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi lấy chi tiết tòa nhà");
  }
};

/**
 * Add or update building
 */
export const addBuildingAPI = async (
  token: string, 
  buildingData: AddBuildingData
): Promise<AddBuildingApiResponse> => {
  try {
    const response = await API.post<AddBuildingApiResponse>("/addBuildingApi", {
      token,
      ...buildingData
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi lưu tòa nhà");
  }
};

/**
 * Delete building
 */
export const deleteBuildingAPI = async (
  token: string, 
  id: number
): Promise<DeleteBuildingApiResponse> => {
  try {
    const response = await API.post<DeleteBuildingApiResponse>("/deleteBuildingApi", {
      token,
      id
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi xóa tòa nhà");
  }
};

/**
 * Add or update floor
 */
export const addFloorAPI = async (
  token: string,
  floorData: AddFloorData
): Promise<AddFloorApiResponse> => {
  try {
    const response = await API.post<AddFloorApiResponse>("/addFloorApi", {
      token,
      ...floorData
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi lưu tầng");
  }
};

/**
 * Delete floor
 */
export const deleteFloorAPI = async (
  token: string,
  id: number
): Promise<DeleteFloorApiResponse> => {
  try {
    const response = await API.post<DeleteFloorApiResponse>("/deleteFloorApi", {
      token,
      id
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi xóa tầng");
  }
};

/**
 * Add or update room
 */
export const addRoomAPI = async (
  token: string,
  roomData: AddRoomData
): Promise<AddRoomApiResponse> => {
  try {
    const response = await API.post<AddRoomApiResponse>("/addRoomApi", {
      token,
      ...roomData
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi lưu phòng học");
  }
};

/**
 * Delete room
 */
export const deleteRoomAPI = async (
  token: string,
  id: number
): Promise<DeleteRoomApiResponse> => {
  try {
    const response = await API.post<DeleteRoomApiResponse>("/deleteRoomApi", {
      token,
      id
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi xóa phòng học");
  }
};
