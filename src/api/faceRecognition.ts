import axios, { AxiosError } from "axios";
import API from "@/lib/axios";

// Types for face recognition log information
export interface FaceRecognitionLog {
  id: number;
  student_id: number;
  student_name: string;
  student_code: string;
  student_phone: string;
  student_avatar?: string | null;
  class_id: number;
  class_name: string;
  class_code: string;
  camera_id: number;
  camera_name: string;
  camera_location: string;
  recognition_time: string;
  face_image_url?: string | null;
  confidence_score?: number;
  created_at: string;
  updated_at: string;
}

export interface FaceRecognitionPaginationData {
  page: number;
  limit: number;
  totalData: number;
  totalPage: number;
}

// Filter parameters for face recognition logs
export interface FaceRecognitionFilters {
  start_date?: string;
  end_date?: string;
  student_id?: string;
  class_id?: string;
  camera_id?: string;
  location?: string;
  student_search?: string;
  page?: number;
  limit?: number;
}

// API Response types
export interface FaceRecognitionListApiResponse {
  data: FaceRecognitionLog[];
  totalData: FaceRecognitionPaginationData;
  code: number;
  mess: string;
}

/**
 * Get face recognition logs with filtering
 */
export const listFaceRecognitionLogsAPI = async (
  token: string,
  filters: FaceRecognitionFilters = {}
): Promise<FaceRecognitionListApiResponse> => {
  try {
    const params = {
      token,
      page: 1,
      limit: 20,
      ...filters
    };

    const response = await API.post<FaceRecognitionListApiResponse>("/listFaceRecognitionLogsAPI", params);
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi tải lịch sử nhận diện khuôn mặt");
  }
};
