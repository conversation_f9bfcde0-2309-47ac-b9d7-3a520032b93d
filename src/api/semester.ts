import API from "@/lib/axios";
import { AxiosError } from "axios";

// Types for semester information
export interface Semester {
  id: number;
  name: string;
  code: string;
  id_parent: number | null;
  start_date: string; // YYYY-MM-DD format
  end_date: string;   // YYYY-MM-DD format
  school_id: number;
}

export interface SemesterFormData {
  name: string;
  code: string;
  start_date: string;
  end_date: string;
  id_parent: number | null;
}

export interface AddSemesterData {
  id?: number;
  name: string;
  code: string;
  start_date: string;
  end_date: string;
  id_parent: number;
}

export interface ListSemesterParams {
  token: string;
  code?: string;
  name?: string;
}

interface SemesterApiResponse {
  data: Semester[];
  code: number;
  mess: string;
}

interface SemesterDetailApiResponse {
  data: Semester;
  code: number;
  mess: string;
}

interface AddSemesterApiResponse {
  data: Semester;
  code: number;
  mess: string;
}

interface DeleteSemesterApiResponse {
  code: number;
  mess: string;
}

/**
 * List semesters with optional search filters
 */
export const listSemesterAPI = async (params: ListSemesterParams): Promise<SemesterApiResponse> => {
  try {
    const response = await API.post<SemesterApiResponse>("/listSemesterAPI", params);
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi lấy danh sách học kỳ");
  }
};

/**
 * Get semester details by ID
 */
export const detailSemesterAPI = async (token: string, id: number): Promise<SemesterDetailApiResponse> => {
  try {
    const response = await API.post<SemesterDetailApiResponse>("/detailSemesterAPI", {
      token,
      id
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi lấy chi tiết học kỳ");
  }
};

/**
 * Add or update semester
 */
export const addSemesterAPI = async (
  token: string, 
  semesterData: AddSemesterData
): Promise<AddSemesterApiResponse> => {
  try {
    const response = await API.post<AddSemesterApiResponse>("/addSemesterAPI", {
      token,
      ...semesterData
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi lưu học kỳ");
  }
};

/**
 * Delete semester
 */
export const deleteSemesterAPI = async (
  token: string, 
  id: number
): Promise<DeleteSemesterApiResponse> => {
  try {
    const response = await API.post<DeleteSemesterApiResponse>("/deleteSemesterAPI", {
      token,
      id
    });
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi xóa học kỳ");
  }
};
