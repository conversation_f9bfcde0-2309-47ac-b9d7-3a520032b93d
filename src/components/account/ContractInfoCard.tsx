"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, FileText, AlertTriangle, CheckCircle, Calendar, Building } from "lucide-react";
import { ContractInfo } from "@/types/account";
import { checkContractExpiryAPI } from "@/api/account";
import { getAuthToken } from "@/utils/getAuthToken";
import { cn } from "@/lib/utils";

interface ContractInfoCardProps {
  className?: string;
}

export const ContractInfoCard = ({ className }: ContractInfoCardProps) => {
  const [contractInfo, setContractInfo] = useState<ContractInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch contract information on component mount
  useEffect(() => {
    fetchContractInfo();
  }, []);

  const fetchContractInfo = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await checkContractExpiryAPI(token);
      
      if (response.code === 1 && response.data) {
        setContractInfo(response.data);
      } else {
        throw new Error(response.mess || "Không thể lấy thông tin hợp đồng");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsLoading(false);
    }
  };

  const getContractStatusColor = (status: string, daysRemaining: number) => {
    if (status === 'expired' || daysRemaining <= 0) {
      return 'text-red-600 bg-red-50 border-red-200';
    } else if (status === 'expiring_soon' || daysRemaining <= 30) {
      return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    } else if (status === 'active') {
      return 'text-green-600 bg-green-50 border-green-200';
    } else {
      return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getContractIcon = (status: string, daysRemaining: number) => {
    if (status === 'expired' || daysRemaining <= 0) {
      return <AlertTriangle className="h-4 w-4" />;
    } else if (status === 'expiring_soon' || daysRemaining <= 30) {
      return <AlertTriangle className="h-4 w-4" />;
    } else if (status === 'active') {
      return <CheckCircle className="h-4 w-4" />;
    } else {
      return <FileText className="h-4 w-4" />;
    }
  };

  const getStatusText = (status: string, daysRemaining: number) => {
    if (status === 'expired' || daysRemaining <= 0) {
      return 'Đã hết hạn';
    } else if (status === 'expiring_soon' || daysRemaining <= 30) {
      return 'Sắp hết hạn';
    } else if (status === 'active') {
      return 'Đang hoạt động';
    } else {
      return status;
    }
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Thông tin hợp đồng
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">Đang tải thông tin...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Thông tin hợp đồng
        </CardTitle>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert className="mb-4 border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-700">{error}</AlertDescription>
          </Alert>
        )}

        {contractInfo && (
          <div className="space-y-4">
            {/* Contract Status */}
            <div className={cn(
              "flex items-center gap-2 p-3 rounded-lg border",
              getContractStatusColor(contractInfo.status, contractInfo.days_remaining)
            )}>
              {getContractIcon(contractInfo.status, contractInfo.days_remaining)}
              <div className="flex-1">
                <p className="font-medium">
                  Trạng thái: {getStatusText(contractInfo.status, contractInfo.days_remaining)}
                </p>
                {contractInfo.days_remaining > 0 ? (
                  <p className="text-sm opacity-90">
                    Còn {contractInfo.days_remaining} ngày đến hạn
                  </p>
                ) : (
                  <p className="text-sm opacity-90">
                    Hợp đồng đã hết hạn
                  </p>
                )}
              </div>
            </div>

            {/* Contract Details */}
            <div className="grid grid-cols-1 gap-4">
              <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                <Building className="h-5 w-5 text-gray-600" />
                <div className="flex-1">
                  <p className="text-sm text-gray-600">Tên trường</p>
                  <p className="font-medium">{contractInfo.school_name}</p>
                </div>
              </div>

              <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                <Calendar className="h-5 w-5 text-gray-600" />
                <div className="flex-1">
                  <p className="text-sm text-gray-600">Ngày hết hạn</p>
                  <p className="font-medium">
                    {new Date(contractInfo.contract_expiry_date).toLocaleDateString('vi-VN', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                <FileText className="h-5 w-5 text-gray-600" />
                <div className="flex-1">
                  <p className="text-sm text-gray-600">Mã trường</p>
                  <p className="font-medium">{contractInfo.school_id}</p>
                </div>
              </div>
            </div>

            {/* Warning for expiring contracts */}
            {contractInfo.days_remaining <= 30 && contractInfo.days_remaining > 0 && (
              <Alert className="border-yellow-200 bg-yellow-50">
                <AlertTriangle className="h-4 w-4 text-yellow-600" />
                <AlertDescription className="text-yellow-700">
                  Hợp đồng sẽ hết hạn trong {contractInfo.days_remaining} ngày. 
                  Vui lòng liên hệ 081.656.0000 để gia hạn hợp đồng.
                </AlertDescription>
              </Alert>
            )}

            {/* Error for expired contracts */}
            {contractInfo.days_remaining <= 0 && (
              <Alert className="border-red-200 bg-red-50">
                <AlertTriangle className="h-4 w-4 text-red-600" />
                <AlertDescription className="text-red-700">
                  Hợp đồng đã hết hạn. Vui lòng liên hệ 081.656.0000 để gia hạn để tiếp tục sử dụng dịch vụ.
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
