"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Lock, AlertTriangle, CheckCircle, Eye, EyeOff } from "lucide-react";
import { PasswordFormData, ChangePasswordData } from "@/types/account";
import { changePasswordAPI } from "@/api/account";
import { getAuthToken } from "@/utils/getAuthToken";
import { cn } from "@/lib/utils";

interface PasswordChangeFormProps {
  className?: string;
}

export const PasswordChangeForm = ({ className }: PasswordChangeFormProps) => {
  const [formData, setFormData] = useState<PasswordFormData>({
    current_password: "",
    new_password: "",
    password_confirmation: "",
  });
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.current_password.trim()) {
      errors.current_password = "Mật khẩu hiện tại là bắt buộc";
    }

    if (!formData.new_password.trim()) {
      errors.new_password = "Mật khẩu mới là bắt buộc";
    } else if (formData.new_password.length < 6) {
      errors.new_password = "Mật khẩu mới phải có ít nhất 6 ký tự";
    }

    if (!formData.password_confirmation.trim()) {
      errors.password_confirmation = "Xác nhận mật khẩu là bắt buộc";
    } else if (formData.new_password !== formData.password_confirmation) {
      errors.password_confirmation = "Mật khẩu xác nhận không khớp";
    }

    if (formData.current_password === formData.new_password) {
      errors.new_password = "Mật khẩu mới phải khác mật khẩu hiện tại";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (field: keyof PasswordFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
    
    // Clear success message when user starts editing
    if (success) {
      setSuccess(null);
    }

    // Real-time validation for password confirmation
    if (field === "password_confirmation" || field === "new_password") {
      const newFormData = { ...formData, [field]: value };
      if (newFormData.new_password && newFormData.password_confirmation) {
        if (newFormData.new_password !== newFormData.password_confirmation) {
          setValidationErrors(prev => ({
            ...prev,
            password_confirmation: "Mật khẩu xác nhận không khớp"
          }));
        } else {
          setValidationErrors(prev => {
            const newErrors = { ...prev };
            delete newErrors.password_confirmation;
            return newErrors;
          });
        }
      }
    }
  };

  const togglePasswordVisibility = (field: 'current' | 'new' | 'confirm') => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setIsUpdating(true);
      setError(null);
      setSuccess(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const passwordData: ChangePasswordData = {
        current_password: formData.current_password,
        new_password: formData.new_password,
        password_confirmation: formData.password_confirmation,
      };

      const response = await changePasswordAPI(token, passwordData);
      
      if (response.code === 1) {
        setSuccess("Đổi mật khẩu thành công!");
        
        // Reset form
        setFormData({
          current_password: "",
          new_password: "",
          password_confirmation: "",
        });
        
        // Hide all passwords
        setShowPasswords({
          current: false,
          new: false,
          confirm: false,
        });
      } else {
        throw new Error(response.mess || "Không thể đổi mật khẩu");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Lock className="h-5 w-5" />
          Đổi mật khẩu
        </CardTitle>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert className="mb-4 border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-700">{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="mb-4 border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-700">{success}</AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="current_password">Mật khẩu hiện tại *</Label>
            <div className="relative">
              <Input
                id="current_password"
                type={showPasswords.current ? "text" : "password"}
                value={formData.current_password}
                onChange={(e) => handleInputChange("current_password", e.target.value)}
                placeholder="Nhập mật khẩu hiện tại"
                className={cn(
                  "pr-10",
                  validationErrors.current_password ? "border-red-500" : ""
                )}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => togglePasswordVisibility('current')}
              >
                {showPasswords.current ? (
                  <EyeOff className="h-4 w-4 text-gray-400" />
                ) : (
                  <Eye className="h-4 w-4 text-gray-400" />
                )}
              </Button>
            </div>
            {validationErrors.current_password && (
              <p className="text-sm text-red-600">{validationErrors.current_password}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="new_password">Mật khẩu mới *</Label>
            <div className="relative">
              <Input
                id="new_password"
                type={showPasswords.new ? "text" : "password"}
                value={formData.new_password}
                onChange={(e) => handleInputChange("new_password", e.target.value)}
                placeholder="Nhập mật khẩu mới (ít nhất 6 ký tự)"
                className={cn(
                  "pr-10",
                  validationErrors.new_password ? "border-red-500" : ""
                )}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => togglePasswordVisibility('new')}
              >
                {showPasswords.new ? (
                  <EyeOff className="h-4 w-4 text-gray-400" />
                ) : (
                  <Eye className="h-4 w-4 text-gray-400" />
                )}
              </Button>
            </div>
            {validationErrors.new_password && (
              <p className="text-sm text-red-600">{validationErrors.new_password}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="password_confirmation">Xác nhận mật khẩu mới *</Label>
            <div className="relative">
              <Input
                id="password_confirmation"
                type={showPasswords.confirm ? "text" : "password"}
                value={formData.password_confirmation}
                onChange={(e) => handleInputChange("password_confirmation", e.target.value)}
                placeholder="Nhập lại mật khẩu mới"
                className={cn(
                  "pr-10",
                  validationErrors.password_confirmation ? "border-red-500" : ""
                )}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => togglePasswordVisibility('confirm')}
              >
                {showPasswords.confirm ? (
                  <EyeOff className="h-4 w-4 text-gray-400" />
                ) : (
                  <Eye className="h-4 w-4 text-gray-400" />
                )}
              </Button>
            </div>
            {validationErrors.password_confirmation && (
              <p className="text-sm text-red-600">{validationErrors.password_confirmation}</p>
            )}
          </div>

          <div className="pt-2">
            <Button 
              type="submit" 
              className="w-full" 
              disabled={isUpdating}
            >
              {isUpdating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Đang đổi mật khẩu...
                </>
              ) : (
                "Đổi mật khẩu"
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
