"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Search, Plus } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface StudentFilterProps {
  searchName: string;
  searchPhoneFather: string;
  selectedStatus: string;
  selectedGender: string;
  onSearchChange: (field: 'search' | 'phone_father' | 'status' | 'gender', value: string) => void;
  onSearch: () => void;
  onClearFilters: () => void;
  onAddStudent: () => void;
}

export const StudentFilter = ({
  searchName,
  searchPhoneFather,
  selectedStatus,
  selectedGender,
  onSearchChange,
  onSearch,
  onClearFilters,
  onAddStudent,
}: StudentFilterProps) => {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">T<PERSON><PERSON> kiếm và lọc</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Search by name */}
          <div className="space-y-2">
            <Label htmlFor="search-name">Tên sinh viên</Label>
            <Input
              id="search-name"
              placeholder="Nhập tên sinh viên..."
              value={searchName}
              onChange={(e) => onSearchChange('search', e.target.value)}
            />
          </div>

          {/* Search by father's phone */}
          <div className="space-y-2">
            <Label htmlFor="search-phone-father">Số điện thoại phụ huynh</Label>
            <Input
              id="search-phone-father"
              placeholder="Nhập số điện thoại..."
              value={searchPhoneFather}
              onChange={(e) => onSearchChange('phone_father', e.target.value)}
            />
          </div>

          {/* Filter by status */}
          <div className="space-y-2">
            <Label htmlFor="filter-status">Trạng thái</Label>
            <Select
              value={selectedStatus}
              onValueChange={(value) => onSearchChange('status', value)}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Chọn trạng thái" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả</SelectItem>
                <SelectItem value="active">Hoạt động</SelectItem>
                <SelectItem value="inactive">Không hoạt động</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Filter by gender */}
          <div className="space-y-2">
            <Label htmlFor="filter-gender">Giới tính</Label>
            <Select
              value={selectedGender}
              onValueChange={(value) => onSearchChange('gender', value)}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Chọn giới tính" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả</SelectItem>
                <SelectItem value="male">Nam</SelectItem>
                <SelectItem value="female">Nữ</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex items-center gap-2 mt-4">
          <Button onClick={onSearch} className="flex-1 md:flex-none">
            <Search className="h-4 w-4 mr-2" />
            Tìm kiếm
          </Button>
          <Button onClick={onClearFilters} variant="outline">
            Xóa bộ lọc
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
