"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Loader2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Student, StudentFormData } from "@/api/student";

interface StudentFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  editingStudent: Student | null;
  onSubmit: (formData: StudentFormData) => Promise<void>;
  isSubmitting: boolean;
}

export const StudentFormDialog = ({
  isOpen,
  onClose,
  editingStudent,
  onSubmit,
  isSubmitting,
}: StudentFormDialogProps) => {
  // Form state
  const [formData, setFormData] = useState<StudentFormData>({
    full_name: "",
    code_student: "",
    phone: "",
    email: "",
    address: "",
    birthday: "",
    gender: "male",
    status: "active",
    note: "",
    phone_father: "",
  });
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Initialize form data when dialog opens or editing student changes
  useEffect(() => {
    if (isOpen) {
      if (editingStudent) {
        setFormData({
          full_name: editingStudent.full_name,
          code_student: editingStudent.code_student,
          phone: editingStudent.phone,
          email: editingStudent.email,
          address: editingStudent.address,
          birthday: editingStudent.birthday,
          gender: editingStudent.gender,
          status: editingStudent.status,
          note: editingStudent.note || "",
          phone_father: editingStudent.phone_father || "",
        });
      } else {
        setFormData({
          full_name: "",
          code_student: "",
          phone: "",
          email: "",
          address: "",
          birthday: "",
          gender: "male",
          status: "active",
          note: "",
          phone_father: "",
        });
      }
      setValidationErrors({});
    }
  }, [isOpen, editingStudent]);

  // Form validation
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.full_name.trim()) {
      errors.full_name = "Họ và tên không được để trống";
    } else if (formData.full_name.length > 100) {
      errors.full_name = "Họ và tên không được vượt quá 100 ký tự";
    }

    if (!formData.code_student.trim()) {
      errors.code_student = "Mã sinh viên không được để trống";
    } else if (formData.code_student.length > 20) {
      errors.code_student = "Mã sinh viên không được vượt quá 20 ký tự";
    }

    if (!formData.phone.trim()) {
      errors.phone = "Số điện thoại không được để trống";
    } else if (!/^[0-9]{10,11}$/.test(formData.phone)) {
      errors.phone = "Số điện thoại phải có 10-11 chữ số";
    }

    if (!formData.email.trim()) {
      errors.email = "Email không được để trống";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = "Email không đúng định dạng";
    }

    if (!formData.address.trim()) {
      errors.address = "Địa chỉ không được để trống";
    } else if (formData.address.length > 200) {
      errors.address = "Địa chỉ không được vượt quá 200 ký tự";
    }

    if (!formData.birthday) {
      errors.birthday = "Ngày sinh không được để trống";
    } else {
      const birthDate = new Date(formData.birthday);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      if (age < 5 || age > 100) {
        errors.birthday = "Tuổi phải từ 5 đến 100";
      }
    }

    if (formData.phone_father && !/^[0-9]{10,11}$/.test(formData.phone_father)) {
      errors.phone_father = "Số điện thoại phụ huynh phải có 10-11 chữ số";
    }

    if (formData.note && formData.note.length > 500) {
      errors.note = "Ghi chú không được vượt quá 500 ký tự";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Form handlers
  const handleInputChange = (field: keyof StudentFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    await onSubmit(formData);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {editingStudent ? "Chỉnh sửa sinh viên" : "Thêm sinh viên mới"}
          </DialogTitle>
          <DialogDescription>
            {editingStudent 
              ? "Cập nhật thông tin sinh viên dưới đây." 
              : "Nhập thông tin sinh viên mới dưới đây."
            }
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Full Name */}
            <div className="space-y-2">
              <Label htmlFor="full_name">Họ và tên *</Label>
              <Input
                id="full_name"
                value={formData.full_name}
                onChange={(e) => handleInputChange("full_name", e.target.value)}
                placeholder="Nhập họ và tên"
                className={validationErrors.full_name ? "border-red-500" : ""}
                disabled={isSubmitting}
              />
              {validationErrors.full_name && (
                <p className="text-sm text-red-600">{validationErrors.full_name}</p>
              )}
            </div>

            {/* Student Code */}
            <div className="space-y-2">
              <Label htmlFor="code_student">Mã sinh viên *</Label>
              <Input
                id="code_student"
                value={formData.code_student}
                onChange={(e) => handleInputChange("code_student", e.target.value)}
                placeholder="Nhập mã sinh viên"
                className={validationErrors.code_student ? "border-red-500" : ""}
                disabled={isSubmitting}
              />
              {validationErrors.code_student && (
                <p className="text-sm text-red-600">{validationErrors.code_student}</p>
              )}
            </div>

            {/* Phone */}
            <div className="space-y-2">
              <Label htmlFor="phone">Số điện thoại *</Label>
              <Input
                id="phone"
                value={formData.phone}
                onChange={(e) => handleInputChange("phone", e.target.value)}
                placeholder="Nhập số điện thoại"
                className={validationErrors.phone ? "border-red-500" : ""}
                disabled={isSubmitting}
              />
              {validationErrors.phone && (
                <p className="text-sm text-red-600">{validationErrors.phone}</p>
              )}
            </div>

            {/* Email */}
            <div className="space-y-2">
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                placeholder="Nhập email"
                className={validationErrors.email ? "border-red-500" : ""}
                disabled={isSubmitting}
              />
              {validationErrors.email && (
                <p className="text-sm text-red-600">{validationErrors.email}</p>
              )}
            </div>

            {/* Birthday */}
            <div className="space-y-2">
              <Label htmlFor="birthday">Ngày sinh *</Label>
              <Input
                id="birthday"
                type="date"
                value={formData.birthday}
                onChange={(e) => handleInputChange("birthday", e.target.value)}
                className={validationErrors.birthday ? "border-red-500" : ""}
                disabled={isSubmitting}
              />
              {validationErrors.birthday && (
                <p className="text-sm text-red-600">{validationErrors.birthday}</p>
              )}
            </div>

            {/* Gender */}
            <div className="space-y-2">
              <Label htmlFor="gender">Giới tính *</Label>
              <Select
                value={formData.gender}
                onValueChange={(value: "male" | "female") => handleInputChange("gender", value)}
                disabled={isSubmitting}
              >
                <SelectTrigger className={validationErrors.gender ? "border-red-500" : ""}>
                  <SelectValue placeholder="Chọn giới tính" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="male">Nam</SelectItem>
                  <SelectItem value="female">Nữ</SelectItem>
                </SelectContent>
              </Select>
              {validationErrors.gender && (
                <p className="text-sm text-red-600">{validationErrors.gender}</p>
              )}
            </div>

            {/* Status */}
            <div className="space-y-2">
              <Label htmlFor="status">Trạng thái *</Label>
              <Select
                value={formData.status}
                onValueChange={(value: "active" | "inactive") => handleInputChange("status", value)}
                disabled={isSubmitting}
              >
                <SelectTrigger className={validationErrors.status ? "border-red-500" : ""}>
                  <SelectValue placeholder="Chọn trạng thái" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Hoạt động</SelectItem>
                  <SelectItem value="inactive">Không hoạt động</SelectItem>
                </SelectContent>
              </Select>
              {validationErrors.status && (
                <p className="text-sm text-red-600">{validationErrors.status}</p>
              )}
            </div>

            {/* Phone Father */}
            <div className="space-y-2">
              <Label htmlFor="phone_father">SĐT phụ huynh</Label>
              <Input
                id="phone_father"
                value={formData.phone_father}
                onChange={(e) => handleInputChange("phone_father", e.target.value)}
                placeholder="Nhập số điện thoại phụ huynh"
                className={validationErrors.phone_father ? "border-red-500" : ""}
                disabled={isSubmitting}
              />
              {validationErrors.phone_father && (
                <p className="text-sm text-red-600">{validationErrors.phone_father}</p>
              )}
            </div>
          </div>

          {/* Address */}
          <div className="space-y-2">
            <Label htmlFor="address">Địa chỉ *</Label>
            <Input
              id="address"
              value={formData.address}
              onChange={(e) => handleInputChange("address", e.target.value)}
              placeholder="Nhập địa chỉ"
              className={validationErrors.address ? "border-red-500" : ""}
              disabled={isSubmitting}
            />
            {validationErrors.address && (
              <p className="text-sm text-red-600">{validationErrors.address}</p>
            )}
          </div>

          {/* Note */}
          <div className="space-y-2">
            <Label htmlFor="note">Ghi chú</Label>
            <Textarea
              id="note"
              value={formData.note}
              onChange={(e) => handleInputChange("note", e.target.value)}
              placeholder="Nhập ghi chú (tùy chọn)"
              className={validationErrors.note ? "border-red-500" : ""}
              disabled={isSubmitting}
              rows={3}
            />
            {validationErrors.note && (
              <p className="text-sm text-red-600">{validationErrors.note}</p>
            )}
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
              Hủy
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Đang lưu...
                </>
              ) : (
                editingStudent ? "Cập nhật" : "Thêm mới"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
