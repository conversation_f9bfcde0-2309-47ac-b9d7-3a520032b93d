"use client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Student } from "@/api/student";

interface StudentDeleteDialogProps {
  isOpen: boolean;
  onClose: () => void;
  student: Student | null;
  onConfirm: () => void;
  isDeleting?: boolean;
}

export const StudentDeleteDialog = ({
  isOpen,
  onClose,
  student,
  onConfirm,
  isDeleting = false,
}: StudentDeleteDialogProps) => {
  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Xác nhận xóa sinh viên</AlertDialogTitle>
          <AlertDialogDescription>
            Bạn có chắc chắn muốn xóa sinh viên `{student?.full_name}` (Mã: {student?.code_student}) không? 
            Hành động này không thể hoàn tác và sẽ xóa vĩnh viễn tất cả thông tin của sinh viên này.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={onClose} disabled={isDeleting}>
            Hủy
          </AlertDialogCancel>
          <AlertDialogAction 
            onClick={onConfirm}
            disabled={isDeleting}
            className="bg-red-600 hover:bg-red-700"
          >
            {isDeleting ? "Đang xóa..." : "Xóa"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
