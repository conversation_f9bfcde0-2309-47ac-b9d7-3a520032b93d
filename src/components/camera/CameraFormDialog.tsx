"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Camera, CameraFormData, validateMacAddress, validateIpAddress } from "@/api/camera";
import { listBuildingAPI, Building } from "@/api/building";
import { listFloorAPI, Floor } from "@/api/floor";
import { getAuthToken } from "@/utils/getAuthToken";

interface CameraFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  editingCamera: Camera | null;
  onSubmit: (formData: CameraFormData) => Promise<void>;
  isSubmitting: boolean;
}

export const CameraFormDialog = ({
  isOpen,
  onClose,
  editingCamera,
  onSubmit,
  isSubmitting,
}: CameraFormDialogProps) => {
  // Form state
  const [formData, setFormData] = useState<CameraFormData>({
    name: "",
    room_id: 0,
    mac_address: "",
    ip_address: "",
  });
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Hierarchical data state
  const [buildings, setBuildings] = useState<Building[]>([]);
  const [floors, setFloors] = useState<Floor[]>([]);
  const [rooms, setRooms] = useState<any[]>([]);
  const [selectedBuildingId, setSelectedBuildingId] = useState<number>(0);
  const [selectedFloorId, setSelectedFloorId] = useState<number>(0);
  const [isLoadingBuildings, setIsLoadingBuildings] = useState(false);
  const [isLoadingFloors, setIsLoadingFloors] = useState(false);
  const [isLoadingRooms, setIsLoadingRooms] = useState(false);

  // Load buildings when dialog opens
  useEffect(() => {
    if (isOpen) {
      loadBuildings();
      if (editingCamera) {
        setFormData({
          name: editingCamera.name,
          room_id: editingCamera.room_id,
          mac_address: editingCamera.mac_address,
          ip_address: editingCamera.ip_address || "",
        });
        // Set hierarchical selections based on existing camera data
        if (editingCamera.building_info) {
          setSelectedBuildingId(editingCamera.building_info.id);
        }
        if (editingCamera.floor_info) {
          setSelectedFloorId(editingCamera.floor_info.id);
        }
      } else {
        setFormData({
          name: "",
          room_id: 0,
          mac_address: "",
          ip_address: "",
        });
        setSelectedBuildingId(0);
        setSelectedFloorId(0);
      }
      setValidationErrors({});
    }
  }, [isOpen, editingCamera]);

  // Load floors when building is selected
  useEffect(() => {
    if (selectedBuildingId > 0) {
      loadFloors(selectedBuildingId);
    } else {
      setFloors([]);
      setRooms([]);
      setSelectedFloorId(0);
    }
  }, [selectedBuildingId]);

  // Load rooms when floor is selected
  useEffect(() => {
    if (selectedFloorId > 0) {
      loadRooms(selectedFloorId);
    } else {
      setRooms([]);
    }
  }, [selectedFloorId]);

  // Data loading functions
  const loadBuildings = async () => {
    try {
      setIsLoadingBuildings(true);
      const token = getAuthToken();
      if (!token) return;

      const response = await listBuildingAPI({
        token,
        limit: 1000 // Get all buildings
      });

      if (response.code === 1 && response.data) {
        setBuildings(response.data);
      }
    } catch (err) {
      console.error("Error loading buildings:", err);
    } finally {
      setIsLoadingBuildings(false);
    }
  };

  const loadFloors = async (buildingId: number) => {
    try {
      setIsLoadingFloors(true);
      const token = getAuthToken();
      if (!token) return;

      const response = await listFloorAPI({
        token,
        building_id: buildingId,
        limit: 1000 // Get all floors for the building
      });

      if (response.code === 1 && response.data) {
        setFloors(response.data);
      }
    } catch (err) {
      console.error("Error loading floors:", err);
    } finally {
      setIsLoadingFloors(false);
    }
  };

  const loadRooms = async (floorId: number) => {
    try {
      setIsLoadingRooms(true);
      const token = getAuthToken();
      if (!token) return;

      // Find the selected floor to get its rooms
      const selectedFloor = floors.find(f => f.id === floorId);
      if (selectedFloor && selectedFloor.rooms) {
        setRooms(selectedFloor.rooms);
      }
    } catch (err) {
      console.error("Error loading rooms:", err);
    } finally {
      setIsLoadingRooms(false);
    }
  };

  // Validation function
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    // Required fields
    if (!formData.name.trim()) {
      errors.name = "Tên camera là bắt buộc";
    }

    if (!formData.room_id || formData.room_id === 0) {
      errors.room_id = "Phòng là bắt buộc";
    }

    if (!formData.mac_address.trim()) {
      errors.mac_address = "Địa chỉ MAC là bắt buộc";
    } else if (!validateMacAddress(formData.mac_address)) {
      errors.mac_address = "Địa chỉ MAC không hợp lệ (định dạng: XX:XX:XX:XX:XX:XX hoặc XX-XX-XX-XX-XX-XX)";
    }

    if (!formData.ip_address.trim()) {
      errors.ip_address = "Địa chỉ IP là bắt buộc";
    } else if (!validateIpAddress(formData.ip_address)) {
      errors.ip_address = "Địa chỉ IP không hợp lệ (định dạng IPv4: *************)";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Form handlers
  const handleInputChange = (field: keyof CameraFormData, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleBuildingChange = (buildingId: string) => {
    const id = parseInt(buildingId);
    setSelectedBuildingId(id);
    setSelectedFloorId(0);
    setFormData(prev => ({ ...prev, room_id: 0 }));

    // Clear room validation error
    if (validationErrors.room_id) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.room_id;
        return newErrors;
      });
    }
  };

  const handleFloorChange = (floorId: string) => {
    const id = parseInt(floorId);
    setSelectedFloorId(id);
    setFormData(prev => ({ ...prev, room_id: 0 }));

    // Clear room validation error
    if (validationErrors.room_id) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.room_id;
        return newErrors;
      });
    }
  };

  const handleRoomChange = (roomId: string) => {
    const id = parseInt(roomId);
    handleInputChange("room_id", id);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    await onSubmit(formData);
  };

  const formatMacAddress = (value: string) => {
    // Remove all non-alphanumeric characters and convert to uppercase
    const cleaned = value.replace(/[^0-9A-Fa-f]/g, '').toUpperCase();
    
    // Add colons every 2 characters
    const formatted = cleaned.match(/.{1,2}/g)?.join(':') || cleaned;
    
    // Limit to 17 characters (XX:XX:XX:XX:XX:XX)
    return formatted.substring(0, 17);
  };

  const handleMacAddressChange = (value: string) => {
    const formatted = formatMacAddress(value);
    handleInputChange("mac_address", formatted);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {editingCamera ? "Chỉnh sửa camera" : "Thêm camera mới"}
          </DialogTitle>
          <DialogDescription>
            {editingCamera 
              ? "Cập nhật thông tin camera trong hệ thống." 
              : "Thêm camera mới vào hệ thống quản lý."
            }
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Tên camera *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              placeholder="Nhập tên camera"
              className={validationErrors.name ? "border-red-500" : ""}
              disabled={isSubmitting}
            />
            {validationErrors.name && (
              <p className="text-sm text-red-600">{validationErrors.name}</p>
            )}
          </div>

          {/* Hierarchical Location Selection */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="building">Tòa nhà *</Label>
              <Select
                value={selectedBuildingId.toString()}
                onValueChange={handleBuildingChange}
                disabled={isSubmitting || isLoadingBuildings}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Chọn tòa nhà" />
                </SelectTrigger>
                <SelectContent>
                  {buildings.map((building) => (
                    <SelectItem key={building.id} value={building.id.toString()}>
                      {building.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="floor">Tầng *</Label>
              <Select
                value={selectedFloorId.toString()}
                onValueChange={handleFloorChange}
                disabled={isSubmitting || isLoadingFloors || selectedBuildingId === 0}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Chọn tầng" />
                </SelectTrigger>
                <SelectContent>
                  {floors.map((floor) => (
                    <SelectItem key={floor.id} value={floor.id.toString()}>
                      {floor.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="room">Phòng *</Label>
              <Select
                value={formData.room_id.toString()}
                onValueChange={handleRoomChange}
                disabled={isSubmitting || isLoadingRooms || selectedFloorId === 0}
              >
                <SelectTrigger className={validationErrors.room_id ? "border-red-500" : ""}>
                  <SelectValue placeholder="Chọn phòng" />
                </SelectTrigger>
                <SelectContent>
                  {rooms.map((room) => (
                    <SelectItem key={room.id} value={room.id.toString()}>
                      {room.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {validationErrors.room_id && (
                <p className="text-sm text-red-600">{validationErrors.room_id}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="mac_address">Địa chỉ MAC *</Label>
              <Input
                id="mac_address"
                value={formData.mac_address}
                onChange={(e) => handleMacAddressChange(e.target.value)}
                placeholder="XX:XX:XX:XX:XX:XX"
                className={validationErrors.mac_address ? "border-red-500" : ""}
                disabled={isSubmitting}
                maxLength={17}
              />
              {validationErrors.mac_address && (
                <p className="text-sm text-red-600">{validationErrors.mac_address}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="ip_address">Địa chỉ IP *</Label>
              <Input
                id="ip_address"
                value={formData.ip_address}
                onChange={(e) => handleInputChange("ip_address", e.target.value)}
                placeholder="*************"
                className={validationErrors.ip_address ? "border-red-500" : ""}
                disabled={isSubmitting}
              />
              {validationErrors.ip_address && (
                <p className="text-sm text-red-600">{validationErrors.ip_address}</p>
              )}
            </div>
          </div>



          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Hủy
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {editingCamera ? "Cập nhật" : "Thêm camera"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
