"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Camera, CameraFormData, validateMacAddress, validateIpAddress } from "@/api/camera";

interface CameraFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  editingCamera: Camera | null;
  onSubmit: (formData: CameraFormData) => Promise<void>;
  isSubmitting: boolean;
}

export const CameraFormDialog = ({
  isOpen,
  onClose,
  editingCamera,
  onSubmit,
  isSubmitting,
}: CameraFormDialogProps) => {
  // Form state
  const [formData, setFormData] = useState<CameraFormData>({
    name: "",
    location: "",
    mac_address: "",
    ip_address: "",
    status: "online",
  });
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Initialize form data when dialog opens or editing camera changes
  useEffect(() => {
    if (isOpen) {
      if (editingCamera) {
        setFormData({
          name: editingCamera.name,
          location: editingCamera.location,
          mac_address: editingCamera.mac_address,
          ip_address: editingCamera.ip_address || "",
          status: editingCamera.status,
        });
      } else {
        setFormData({
          name: "",
          location: "",
          mac_address: "",
          ip_address: "",
          status: "online",
        });
      }
      setValidationErrors({});
    }
  }, [isOpen, editingCamera]);

  // Validation function
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    // Required fields
    if (!formData.name.trim()) {
      errors.name = "Tên camera là bắt buộc";
    }

    if (!formData.location.trim()) {
      errors.location = "Vị trí là bắt buộc";
    }

    if (!formData.mac_address.trim()) {
      errors.mac_address = "Địa chỉ MAC là bắt buộc";
    } else if (!validateMacAddress(formData.mac_address)) {
      errors.mac_address = "Địa chỉ MAC không hợp lệ (định dạng: XX:XX:XX:XX:XX:XX hoặc XX-XX-XX-XX-XX-XX)";
    }

    if (!formData.ip_address.trim()) {
      errors.ip_address = "Địa chỉ IP là bắt buộc";
    } else if (!validateIpAddress(formData.ip_address)) {
      errors.ip_address = "Địa chỉ IP không hợp lệ (định dạng IPv4: *************)";
    }

    if (!formData.status) {
      errors.status = "Trạng thái là bắt buộc";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Form handlers
  const handleInputChange = (field: keyof CameraFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    await onSubmit(formData);
  };

  const formatMacAddress = (value: string) => {
    // Remove all non-alphanumeric characters and convert to uppercase
    const cleaned = value.replace(/[^0-9A-Fa-f]/g, '').toUpperCase();
    
    // Add colons every 2 characters
    const formatted = cleaned.match(/.{1,2}/g)?.join(':') || cleaned;
    
    // Limit to 17 characters (XX:XX:XX:XX:XX:XX)
    return formatted.substring(0, 17);
  };

  const handleMacAddressChange = (value: string) => {
    const formatted = formatMacAddress(value);
    handleInputChange("mac_address", formatted);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {editingCamera ? "Chỉnh sửa camera" : "Thêm camera mới"}
          </DialogTitle>
          <DialogDescription>
            {editingCamera 
              ? "Cập nhật thông tin camera trong hệ thống." 
              : "Thêm camera mới vào hệ thống quản lý."
            }
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Tên camera *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              placeholder="Nhập tên camera"
              className={validationErrors.name ? "border-red-500" : ""}
              disabled={isSubmitting}
            />
            {validationErrors.name && (
              <p className="text-sm text-red-600">{validationErrors.name}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="location">Vị trí *</Label>
            <Textarea
              id="location"
              value={formData.location}
              onChange={(e) => handleInputChange("location", e.target.value)}
              placeholder="Nhập vị trí lắp đặt camera"
              className={validationErrors.location ? "border-red-500" : ""}
              disabled={isSubmitting}
              rows={2}
            />
            {validationErrors.location && (
              <p className="text-sm text-red-600">{validationErrors.location}</p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="mac_address">Địa chỉ MAC *</Label>
              <Input
                id="mac_address"
                value={formData.mac_address}
                onChange={(e) => handleMacAddressChange(e.target.value)}
                placeholder="XX:XX:XX:XX:XX:XX"
                className={validationErrors.mac_address ? "border-red-500" : ""}
                disabled={isSubmitting}
                maxLength={17}
              />
              {validationErrors.mac_address && (
                <p className="text-sm text-red-600">{validationErrors.mac_address}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="ip_address">Địa chỉ IP *</Label>
              <Input
                id="ip_address"
                value={formData.ip_address}
                onChange={(e) => handleInputChange("ip_address", e.target.value)}
                placeholder="*************"
                className={validationErrors.ip_address ? "border-red-500" : ""}
                disabled={isSubmitting}
              />
              {validationErrors.ip_address && (
                <p className="text-sm text-red-600">{validationErrors.ip_address}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">Trạng thái *</Label>
            <Select
              value={formData.status}
              onValueChange={(value) => handleInputChange("status", value)}
              disabled={isSubmitting}
            >
              <SelectTrigger className={validationErrors.status ? "border-red-500" : ""}>
                <SelectValue placeholder="Chọn trạng thái" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">Hoạt động</SelectItem>
                <SelectItem value="offline">Offline</SelectItem>
                <SelectItem value="maintenance">Bảo trì</SelectItem>
                <SelectItem value="error">Lỗi</SelectItem>
              </SelectContent>
            </Select>
            {validationErrors.status && (
              <p className="text-sm text-red-600">{validationErrors.status}</p>
            )}
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Hủy
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {editingCamera ? "Cập nhật" : "Thêm camera"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
