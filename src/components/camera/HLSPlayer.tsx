"use client";

import { useEffect, useRef, useState } from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { AlertTriangle, RefreshCw, Loader2, CheckCircle, ExternalLink, Play } from "lucide-react";
import Hls from "hls.js";

interface HLSPlayerProps {
  streamUrl: string;
  className?: string;
  onError?: (error: string) => void;
  onLoad?: () => void;
  autoPlay?: boolean;
  muted?: boolean;
}

export const HLSPlayer = ({
  streamUrl,
  className = "",
  onError,
  onLoad,
  autoPlay = true,
  muted = true,
}: HLSPlayerProps) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const hlsRef = useRef<Hls | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [streamInfo, setStreamInfo] = useState<any>({});
  const [isPlaying, setIsPlaying] = useState(false);
  const maxRetries = 3;

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
    setIsLoading(false);
    onError?.(errorMessage);
  };

  const testStreamAccess = async (url: string): Promise<any> => {
    try {
      console.log('🔍 Testing stream access:', url);
      const response = await fetch(url, { 
        method: 'HEAD',
        mode: 'cors'
      });
      
      const info = {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        headers: {
          'content-type': response.headers.get('content-type'),
          'access-control-allow-origin': response.headers.get('access-control-allow-origin'),
          'cache-control': response.headers.get('cache-control'),
          'server': response.headers.get('server'),
        }
      };
      
      console.log('📊 Stream access test result:', info);
      setStreamInfo(info);
      return info;
    } catch (err: any) {
      console.error('❌ Stream access test failed:', err);
      const errorInfo = { 
        error: err.message,
        type: err.name,
        cors: err.message.includes('CORS') || err.message.includes('cors')
      };
      setStreamInfo(errorInfo);
      return errorInfo;
    }
  };

  const initializeHLSPlayer = async () => {
    if (!videoRef.current || !streamUrl) return;

    const video = videoRef.current;

    try {
      setIsLoading(true);
      setError(null);

      // Test stream access first
      await testStreamAccess(streamUrl);
      
      // Clean up existing HLS instance
      if (hlsRef.current) {
        hlsRef.current.destroy();
        hlsRef.current = null;
      }

      // Check if HLS is supported
      if (Hls.isSupported()) {
        console.log('✅ HLS.js is supported, initializing...');
        
        const hls = new Hls({
          debug: false,
          enableWorker: true,
          lowLatencyMode: true,
          backBufferLength: 90,
          maxBufferLength: 30,
          maxMaxBufferLength: 600,
          maxBufferSize: 60 * 1000 * 1000,
          maxBufferHole: 0.5,
          liveSyncDurationCount: 3,
          liveMaxLatencyDurationCount: 5,
          liveDurationInfinity: true,
          highBufferWatchdogPeriod: 2,
        });

        hlsRef.current = hls;

        // Event listeners
        hls.on(Hls.Events.MEDIA_ATTACHED, () => {
          console.log('📺 Media attached to video element');
        });

        hls.on(Hls.Events.MANIFEST_PARSED, (_event, data) => {
          console.log('✅ HLS manifest parsed successfully');
          console.log('📊 Stream info:', {
            levels: data.levels.length,
            firstLevel: data.levels[0],
            duration: data.levels[0]?.details?.totalduration
          });
          
          setIsLoading(false);
          setError(null);
          onLoad?.();
          
          // Try to play if autoPlay is enabled
          if (autoPlay) {
            video.play().catch(e => {
              console.warn('⚠️ Autoplay prevented (normal behavior):', e.message);
              // This is normal - browsers prevent autoplay without user interaction
            });
          }
        });

        hls.on(Hls.Events.LEVEL_LOADED, (_event, data) => {
          console.log('📊 Level loaded:', {
            level: data.level,
            details: data.details.totalduration,
            fragments: data.details.fragments.length
          });
        });

        hls.on(Hls.Events.FRAG_LOADED, (_event, data) => {
          console.log('🧩 Fragment loaded:', {
            url: data.frag.url,
            duration: data.frag.duration,
            level: data.frag.level
          });
        });

        hls.on(Hls.Events.ERROR, (_event, data) => {
          console.error('❌ HLS Error:', data);
          
          if (data.fatal) {
            switch (data.type) {
              case Hls.ErrorTypes.NETWORK_ERROR:
                if (retryCount < maxRetries) {
                  console.log(`🔄 Network error, retrying... (${retryCount + 1}/${maxRetries})`);
                  setTimeout(() => {
                    setRetryCount(prev => prev + 1);
                    hls.startLoad();
                  }, 2000);
                } else {
                  handleError(
                    `❌ Network Error: ${data.details}\n\n` +
                    `🔍 Possible causes:\n` +
                    `• CORS policy - Server không cho phép browser truy cập\n` +
                    `• Stream server không phản hồi hoặc offline\n` +
                    `• Network connectivity issues\n` +
                    `• Invalid or expired stream URL\n\n` +
                    `💡 Solutions:\n` +
                    `• Kiểm tra server CORS configuration\n` +
                    `• Verify stream đang active trong VLC\n` +
                    `• Test direct access to stream URL\n` +
                    `• Check network firewall settings`
                  );
                }
                break;
                
              case Hls.ErrorTypes.MEDIA_ERROR:
                console.log('🔄 Media error, attempting recovery...');
                hls.recoverMediaError();
                break;
                
              default:
                handleError(`❌ HLS Fatal Error: ${data.details || data.type}`);
                break;
            }
          } else {
            console.warn('⚠️ Non-fatal HLS error:', data);
          }
        });

        // Video element event listeners
        video.addEventListener('play', () => {
          console.log('▶️ Video started playing');
          setIsPlaying(true);
        });

        video.addEventListener('pause', () => {
          console.log('⏸️ Video paused');
          setIsPlaying(false);
        });

        video.addEventListener('waiting', () => {
          console.log('⏳ Video buffering...');
          setIsLoading(true);
        });

        video.addEventListener('playing', () => {
          console.log('▶️ Video playing (after buffering)');
          setIsLoading(false);
        });

        // Load and attach media
        hls.loadSource(streamUrl);
        hls.attachMedia(video);

      } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
        // Safari native HLS support
        console.log('🍎 Using Safari native HLS support');
        video.src = streamUrl;
        
        video.addEventListener('loadedmetadata', () => {
          console.log('✅ Safari HLS metadata loaded');
          setIsLoading(false);
          setError(null);
          onLoad?.();
        });
        
        video.addEventListener('error', (e) => {
          console.error('❌ Safari HLS error:', e);
          handleError('❌ Safari native HLS playback failed\n\nTry using a different browser or check stream compatibility.');
        });
        
        if (autoPlay) {
          video.play().catch(e => {
            console.warn('⚠️ Safari autoplay prevented:', e);
          });
        }
        
      } else {
        handleError(
          '❌ HLS not supported in this browser\n\n' +
          '💡 Solutions:\n' +
          '• Use a modern browser (Chrome, Firefox, Safari, Edge)\n' +
          '• Update your browser to the latest version\n' +
          '• Try a different device or browser'
        );
      }

    } catch (err: any) {
      console.error('❌ HLS initialization error:', err);
      handleError(`❌ Failed to initialize HLS player: ${err.message}`);
    }
  };

  const handleRetry = () => {
    setRetryCount(0);
    setError(null);
    initializeHLSPlayer();
  };

  const handlePlayPause = async () => {
    if (!videoRef.current) return;
    
    try {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        await videoRef.current.play();
      }
    } catch (err: any) {
      console.error('Play/pause error:', err);
      handleError(`❌ Playback error: ${err.message}`);
    }
  };

  useEffect(() => {
    if (streamUrl) {
      console.log('🚀 HLSPlayer initializing with:', streamUrl);
      initializeHLSPlayer();
    }

    return () => {
      // Cleanup
      if (hlsRef.current) {
        console.log('🧹 Cleaning up HLS instance');
        hlsRef.current.destroy();
        hlsRef.current = null;
      }
    };
  }, [streamUrl]);

  if (error) {
    return (
      <div className={`relative bg-gray-900 rounded-lg overflow-hidden ${className}`}>
        <div className="aspect-video flex items-center justify-center p-6">
          <div className="text-center max-w-2xl">
            <Alert className="border-red-200 bg-red-50 mb-4">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-700 text-left whitespace-pre-line">
                {error}
              </AlertDescription>
            </Alert>

            <div className="text-white text-sm space-y-4">
              <div>
                <p className="text-gray-300 mb-2">Stream URL:</p>
                <code className="bg-gray-800 px-3 py-2 rounded text-xs break-all block">
                  {streamUrl}
                </code>
              </div>

              <div>
                <p className="text-gray-300 mb-2">Debug Information:</p>
                <div className="bg-gray-800 px-3 py-2 rounded text-xs text-left space-y-1">
                  <div>HLS.js Support: {Hls.isSupported() ? '✅ Yes' : '❌ No'}</div>
                  <div>Safari HLS: {videoRef.current?.canPlayType('application/vnd.apple.mpegurl') ? '✅ Yes' : '❌ No'}</div>
                  
                  {streamInfo.status && (
                    <>
                      <div>HTTP Status: {streamInfo.status} {streamInfo.statusText}</div>
                      <div>Response OK: {streamInfo.ok ? '✅' : '❌'}</div>
                    </>
                  )}
                  
                  {streamInfo.headers && (
                    <>
                      <div>Content-Type: {streamInfo.headers['content-type'] || 'N/A'}</div>
                      <div>CORS: {streamInfo.headers['access-control-allow-origin'] || 'Not set'}</div>
                      <div>Server: {streamInfo.headers['server'] || 'Unknown'}</div>
                    </>
                  )}
                  
                  {streamInfo.error && (
                    <>
                      <div className="text-red-400">Error: {streamInfo.error}</div>
                      <div className="text-red-400">Type: {streamInfo.type}</div>
                      {streamInfo.cors && (
                        <div className="text-yellow-400">⚠️ Likely CORS issue</div>
                      )}
                    </>
                  )}
                  
                  <div>Retry Count: {retryCount}/{maxRetries}</div>
                </div>
              </div>

              <div className="flex gap-2 justify-center flex-wrap">
                <Button onClick={handleRetry} variant="outline" size="sm">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Retry
                </Button>
                <Button 
                  onClick={() => window.open(streamUrl, '_blank')} 
                  variant="outline" 
                  size="sm"
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Test Direct
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative bg-black rounded-lg overflow-hidden ${className}`}>
      <video
        ref={videoRef}
        className="w-full h-full object-contain"
        controls
        playsInline
        muted={muted}
      />
      
      {isLoading && (
        <div className="absolute inset-0 bg-black bg-opacity-75 flex items-center justify-center">
          <div className="text-center text-white">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p className="mb-2">Loading HLS stream...</p>
            <div className="text-sm text-gray-300 space-y-1">
              <div className="flex items-center justify-center gap-2">
                <span>HLS.js:</span>
                <CheckCircle className="h-4 w-4 text-green-400" />
                <span>Ready</span>
              </div>
              <div className="text-xs opacity-75">URL: {streamUrl}</div>
              {retryCount > 0 && (
                <div className="text-xs text-yellow-300">
                  Retry attempt: {retryCount}/{maxRetries}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Custom Play Button Overlay (for when autoplay is prevented) */}
      {!isLoading && !isPlaying && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
          <Button
            onClick={handlePlayPause}
            size="lg"
            className="rounded-full w-16 h-16 p-0"
          >
            <Play className="h-8 w-8" />
          </Button>
        </div>
      )}
    </div>
  );
};
