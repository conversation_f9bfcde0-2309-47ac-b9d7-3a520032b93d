"use client";

import { useState, useEffect, memo } from "react";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  AlertTriangle,
  Camera,
  Wifi,
  WifiOff
} from "lucide-react";
import { Camera as CameraType } from "@/api/camera";
import { HLSPlayer } from "./HLSPlayer";
import { Alert, AlertDescription } from "../ui/alert";

interface LiveStreamModalProps {
  isOpen: boolean;
  onClose: () => void;
  camera: CameraType | null;
}

export const LiveStreamModal = memo(({
  isOpen,
  onClose,
  camera,
}: LiveStreamModalProps) => {
  const [error, setError] = useState<string | null>(null);
  const [streamStatus, setStreamStatus] = useState<'connecting' | 'connected' | 'error' | 'disconnected'>('disconnected');

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen && camera) {
      setError(null);
      setStreamStatus('disconnected');
    }
  }, [isOpen, camera]);

  if (!camera) return null;

  const hasStreamUrl = camera.stream_url && camera.stream_url.trim() !== '';

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="min-w-3xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Camera className="h-5 w-5" />
            Live Stream - {camera.name}
            <div className="ml-auto flex items-center gap-2">
              {streamStatus === 'connected' && (
                <div className="flex items-center gap-1 text-green-600">
                  <Wifi className="h-4 w-4" />
                  <span className="text-sm">Đang kết nối</span>
                </div>
              )}
              {streamStatus === 'error' && (
                <div className="flex items-center gap-1 text-red-600">
                  <WifiOff className="h-4 w-4" />
                  <span className="text-sm">Mất kết nối</span>
                </div>
              )}
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Camera Info */}
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Vị trí:</span>
                {camera.building_info && camera.floor_info && camera.room_info ? (
                  <div>
                    <p className="font-medium">{camera.room_info.name}</p>
                    <p className="text-xs text-gray-500">
                      {camera.building_info.name} - {camera.floor_info.name}
                    </p>
                  </div>
                ) : (
                  <p className="font-medium text-gray-400">Chưa có thông tin vị trí</p>
                )}
              </div>
              <div>
                <span className="text-gray-600">IP:</span>
                <p className="font-medium">{camera.ip_address || 'N/A'}</p>
              </div>
              <div>
                <span className="text-gray-600">MAC:</span>
                <p className="font-medium">{camera.mac_address}</p>
              </div>
              <div>
                <span className="text-gray-600">Trạng thái:</span>
                <p className="font-medium capitalize">{camera.status}</p>
              </div>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <Alert className="border-red-200 bg-red-50">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-700">{error}</AlertDescription>
            </Alert>
          )}

          {/* Stream Content */}
          {!hasStreamUrl ? (
            <div className="bg-gray-100 rounded-lg p-8 text-center">
              <Camera className="h-16 w-16 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Live stream không khả dụng
              </h3>
              <p className="text-gray-600">
                Camera này chưa được cấu hình URL stream hoặc không hỗ trợ live streaming.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Stream Info */}
              <div className="flex items-center justify-between bg-gray-100 rounded-lg p-3">
                <div className="flex items-center gap-4">
                  <span className="text-sm font-medium">Giao thức:</span>
                  <span className="px-3 py-1 text-xs rounded bg-blue-600 text-white">
                    HLS
                  </span>
                </div>
                <div className="text-xs text-gray-500">
                  HLS Stream Protocol
                </div>
              </div>

              {/* HLS Player Component */}
              <HLSPlayer
                streamUrl={camera.stream_url!}
                className="aspect-video"
                onError={(error) => setError(error)}
                onLoad={() => setStreamStatus('connected')}
              />
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end items-center pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Đóng
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
});

LiveStreamModal.displayName = 'LiveStreamModal';
