"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search, Filter, Plus } from "lucide-react";

interface CameraFiltersProps {
  searchName: string;
  searchMacAddress: string;
  searchIpAddress: string;
  searchLocation: string;
  filterStatus: string;
  onSearchChange: (field: string, value: string) => void;
  onSearch: () => void;
  onClearFilters: () => void;
  onAddCamera: () => void;
}

export const CameraFilters = ({
  searchName,
  searchMacAddress,
  searchIpAddress,
  searchLocation,
  filterStatus,
  onSearchChange,
  onSearch,
  onClearFilters,
  onAddCamera,
}: CameraFiltersProps) => {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Tìm kiếm và lọc
          </CardTitle>
          <Button onClick={onAddCamera} className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            Thêm camera
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="space-y-2">
            <Label htmlFor="search-name">Tên camera</Label>
            <Input
              id="search-name"
              placeholder="Nhập tên camera..."
              value={searchName}
              onChange={(e) => onSearchChange("name", e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="mac-address">Địa chỉ MAC</Label>
            <Input
              id="mac-address"
              placeholder="XX:XX:XX:XX:XX:XX"
              value={searchMacAddress}
              onChange={(e) => onSearchChange("mac_address", e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="ip-address">Địa chỉ IP</Label>
            <Input
              id="ip-address"
              placeholder="*************"
              value={searchIpAddress}
              onChange={(e) => onSearchChange("ip_address", e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="location">Vị trí</Label>
            <Input
              id="location"
              placeholder="Tầng 1 - Sảnh chính"
              value={searchLocation}
              onChange={(e) => onSearchChange("location", e.target.value)}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
          <div className="space-y-2">
            <Label htmlFor="status">Trạng thái</Label>
            <Select value={filterStatus} onValueChange={(value) => onSearchChange("status", value)}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Chọn trạng thái" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả</SelectItem>
                <SelectItem value="active">Hoạt động</SelectItem>
                <SelectItem value="lock">Khóa</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-end gap-2">
            <Button onClick={onSearch} className="flex-1">
              <Search className="h-4 w-4 mr-2" />
              Tìm kiếm
            </Button>
            <Button onClick={onClearFilters} variant="outline">
              Xóa bộ lọc
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
