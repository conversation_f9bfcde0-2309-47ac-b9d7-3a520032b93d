"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search, Filter, Plus } from "lucide-react";
import { listBuildingAPI, Building } from "@/api/building";
import { listFloorAPI, Floor } from "@/api/floor";
import { getAuthToken } from "@/utils/getAuthToken";

interface CameraFiltersProps {
  searchName: string;
  searchMacAddress: string;
  searchIpAddress: string;
  generalSearch: string;
  filterBuildingId: string;
  filterFloorId: string;
  filterRoomId: string;
  filterStatus: string;
  onSearchChange: (field: string, value: string) => void;
  onSearch: () => void;
  onClearFilters: () => void;
  onAddCamera: () => void;
}

export const CameraFilters = ({
  searchName,
  searchMacAddress,
  searchIpAddress,
  generalSearch,
  filterBuildingId,
  filterFloorId,
  filterRoomId,
  filterStatus,
  onSearchChange,
  onSearch,
  onClearFilters,
  onAddCamera,
}: CameraFiltersProps) => {
  // Hierarchical data state
  const [buildings, setBuildings] = useState<Building[]>([]);
  const [floors, setFloors] = useState<Floor[]>([]);
  const [rooms, setRooms] = useState<any[]>([]);
  const [isLoadingBuildings, setIsLoadingBuildings] = useState(false);
  const [isLoadingFloors, setIsLoadingFloors] = useState(false);

  // Load buildings on component mount
  useEffect(() => {
    loadBuildings();
  }, []);

  // Load floors when building filter changes
  useEffect(() => {
    if (filterBuildingId && filterBuildingId !== "all") {
      loadFloors(parseInt(filterBuildingId));
    } else {
      setFloors([]);
      setRooms([]);
    }
  }, [filterBuildingId]);

  // Load rooms when floor filter changes
  useEffect(() => {
    if (filterFloorId && filterFloorId !== "all") {
      loadRooms(parseInt(filterFloorId));
    } else {
      setRooms([]);
    }
  }, [filterFloorId]);

  const loadBuildings = async () => {
    try {
      setIsLoadingBuildings(true);
      const token = getAuthToken();
      if (!token) return;

      const response = await listBuildingAPI({
        token,
        limit: 1000 // Get all buildings
      });

      if (response.code === 1 && response.data) {
        setBuildings(response.data);
      }
    } catch (err) {
      console.error("Error loading buildings:", err);
    } finally {
      setIsLoadingBuildings(false);
    }
  };

  const loadFloors = async (buildingId: number) => {
    try {
      setIsLoadingFloors(true);
      const token = getAuthToken();
      if (!token) return;

      const response = await listFloorAPI({
        token,
        building_id: buildingId,
        limit: 1000 // Get all floors for the building
      });

      if (response.code === 1 && response.data) {
        setFloors(response.data);
      }
    } catch (err) {
      console.error("Error loading floors:", err);
    } finally {
      setIsLoadingFloors(false);
    }
  };

  const loadRooms = async (floorId: number) => {
    try {
      const token = getAuthToken();
      if (!token) return;

      // Find the selected floor to get its rooms
      const selectedFloor = floors.find(f => f.id === floorId);
      if (selectedFloor && selectedFloor.rooms) {
        setRooms(selectedFloor.rooms);
      }
    } catch (err) {
      console.error("Error loading rooms:", err);
    }
  };
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Tìm kiếm và lọc
          </CardTitle>
          <Button onClick={onAddCamera} className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            Thêm camera
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="space-y-2">
            <Label htmlFor="general-search">Tìm kiếm chung</Label>
            <Input
              id="general-search"
              placeholder="Tên camera, tòa nhà, tầng, phòng..."
              value={generalSearch}
              onChange={(e) => onSearchChange("search", e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="search-name">Tên camera</Label>
            <Input
              id="search-name"
              placeholder="Nhập tên camera..."
              value={searchName}
              onChange={(e) => onSearchChange("name", e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="mac-address">Địa chỉ MAC</Label>
            <Input
              id="mac-address"
              placeholder="XX:XX:XX:XX:XX:XX"
              value={searchMacAddress}
              onChange={(e) => onSearchChange("mac_address", e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="ip-address">Địa chỉ IP</Label>
            <Input
              id="ip-address"
              placeholder="*************"
              value={searchIpAddress}
              onChange={(e) => onSearchChange("ip_address", e.target.value)}
            />
          </div>
        </div>

        {/* Hierarchical Filters */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-4">
          <div className="space-y-2">
            <Label htmlFor="building-filter">Tòa nhà</Label>
            <Select value={filterBuildingId} onValueChange={(value) => onSearchChange("building_id", value)}>
              <SelectTrigger>
                <SelectValue placeholder="Chọn tòa nhà" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả tòa nhà</SelectItem>
                {buildings.map((building) => (
                  <SelectItem key={building.id} value={building.id.toString()}>
                    {building.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="floor-filter">Tầng</Label>
            <Select
              value={filterFloorId}
              onValueChange={(value) => onSearchChange("floor_id", value)}
              disabled={!filterBuildingId || filterBuildingId === "all"}
            >
              <SelectTrigger>
                <SelectValue placeholder="Chọn tầng" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả tầng</SelectItem>
                {floors.map((floor) => (
                  <SelectItem key={floor.id} value={floor.id.toString()}>
                    {floor.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="room-filter">Phòng</Label>
            <Select
              value={filterRoomId}
              onValueChange={(value) => onSearchChange("room_id", value)}
              disabled={!filterFloorId || filterFloorId === "all"}
            >
              <SelectTrigger>
                <SelectValue placeholder="Chọn phòng" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả phòng</SelectItem>
                {rooms.map((room) => (
                  <SelectItem key={room.id} value={room.id.toString()}>
                    {room.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">Trạng thái</Label>
            <Select value={filterStatus} onValueChange={(value) => onSearchChange("status", value)}>
              <SelectTrigger>
                <SelectValue placeholder="Chọn trạng thái" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả</SelectItem>
                <SelectItem value="active">Hoạt động</SelectItem>
                <SelectItem value="lock">Khóa</SelectItem>
                <SelectItem value="offline">Offline</SelectItem>
                <SelectItem value="maintenance">Bảo trì</SelectItem>
                <SelectItem value="error">Lỗi</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="flex items-center justify-end gap-2 mt-4">
          <Button onClick={onSearch} className="flex-1 max-w-xs">
            <Search className="h-4 w-4 mr-2" />
            Tìm kiếm
          </Button>
          <Button onClick={onClearFilters} variant="outline">
            Xóa bộ lọc
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
