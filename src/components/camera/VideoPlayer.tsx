import { useState, useRef } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Play, Pause, Volume2, VolumeX, Maximize, Download, Scissors } from "lucide-react";

interface VideoPlayerProps {
  isOpen: boolean;
  onClose: () => void;
  videoData: {
    id: number;
    name: string;
    camera: string;
    duration: string;
    size: string;
    url?: string;
  } | null;
  onCutVideo: (videoId: number) => void;
}

export const VideoPlayer = ({ isOpen, onClose, videoData, onCutVideo }: VideoPlayerProps) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };

  const handleFullscreen = () => {
    if (videoRef.current) {
      videoRef.current.requestFullscreen();
    }
  };

  const handleDownload = () => {
    // Mock download functionality
    console.log("Downloading video:", videoData?.name);
  };

  const handleCutVideo = () => {
    if (videoData) {
      onCutVideo(videoData.id);
    }
  };

  if (!videoData) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>{videoData.name}</span>
            <div className="flex gap-2">
              <Button size="sm" variant="outline" onClick={handleCutVideo}>
                <Scissors className="h-4 w-4 mr-1" />
                Cắt video
              </Button>
              <Button size="sm" variant="outline" onClick={handleDownload}>
                <Download className="h-4 w-4 mr-1" />
                Tải xuống
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="relative bg-black rounded-lg overflow-hidden">
            <video
              ref={videoRef}
              className="w-full h-64 object-cover"
              onPlay={() => setIsPlaying(true)}
              onPause={() => setIsPlaying(false)}
            >
              <source src={videoData.url || "/api/placeholder/800/450"} type="video/mp4" />
              Your browser does not support the video tag.
            </video>
            
            <div className="absolute bottom-4 left-4 right-4 flex items-center justify-between bg-black/50 rounded p-2">
              <div className="flex items-center gap-2">
                <Button size="sm" variant="ghost" onClick={togglePlay}>
                  {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                </Button>
                <Button size="sm" variant="ghost" onClick={toggleMute}>
                  {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                </Button>
              </div>
              <Button size="sm" variant="ghost" onClick={handleFullscreen}>
                <Maximize className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <p className="font-medium text-gray-700">Camera:</p>
              <p className="text-gray-600">{videoData.camera}</p>
            </div>
            <div>
              <p className="font-medium text-gray-700">Thời lượng:</p>
              <p className="text-gray-600">{videoData.duration}</p>
            </div>
            <div>
              <p className="font-medium text-gray-700">Kích thước:</p>
              <p className="text-gray-600">{videoData.size}</p>
            </div>
            <div>
              <p className="font-medium text-gray-700">Định dạng:</p>
              <p className="text-gray-600">MP4</p>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
