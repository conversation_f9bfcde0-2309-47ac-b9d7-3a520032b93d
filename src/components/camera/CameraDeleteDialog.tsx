"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Loader2, AlertTriangle } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Camera } from "@/api/camera";

interface CameraDeleteDialogProps {
  isOpen: boolean;
  onClose: () => void;
  camera: Camera | null;
  onConfirm: (camera: Camera) => Promise<void>;
  isDeleting: boolean;
}

export const CameraDeleteDialog = ({
  isOpen,
  onClose,
  camera,
  onConfirm,
  isDeleting,
}: CameraDeleteDialogProps) => {
  if (!camera) return null;

  const handleConfirm = async () => {
    await onConfirm(camera);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Xác nhận xóa camera
          </DialogTitle>
          <DialogDescription>
            Bạn có chắc chắn muốn xóa camera này? Hành động này không thể hoàn tác.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-700">
              <strong>Cảnh báo:</strong> Việc xóa camera sẽ:
              <ul className="mt-2 ml-4 list-disc space-y-1">
                <li>Xóa vĩnh viễn thông tin camera khỏi hệ thống</li>
                <li>Ngừng tất cả các hoạt động giám sát từ camera này</li>
                <li>Có thể ảnh hưởng đến các báo cáo và thống kê liên quan</li>
              </ul>
            </AlertDescription>
          </Alert>

          <div className="bg-gray-50 p-4 rounded-lg space-y-2">
            <h4 className="font-medium text-gray-900">Thông tin camera sẽ bị xóa:</h4>
            <div className="space-y-1 text-sm">
              <p><span className="font-medium">Tên:</span> {camera.name}</p>
              <p><span className="font-medium">Vị trí:</span> {camera.location}</p>
              <p><span className="font-medium">Địa chỉ MAC:</span> {camera.mac_address}</p>
              <p><span className="font-medium">Địa chỉ IP:</span> {camera.ip_address}</p>
              <p><span className="font-medium">Trạng thái:</span> {camera.status}</p>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isDeleting}
          >
            Hủy
          </Button>
          <Button
            type="button"
            variant="destructive"
            onClick={handleConfirm}
            disabled={isDeleting}
          >
            {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Xóa camera
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
