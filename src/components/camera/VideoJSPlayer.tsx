"use client";

import { useEffect, useRef, useState } from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { AlertTriangle, RefreshCw, Loader2, ExternalLink } from "lucide-react";

interface VideoJSPlayerProps {
  streamUrl: string;
  className?: string;
  onError?: (error: string) => void;
  onLoad?: () => void;
}

export const VideoJSPlayer = ({
  streamUrl,
  className = "",
  onError,
  onLoad,
}: VideoJSPlayerProps) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const playerRef = useRef<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [videoJSAvailable, setVideoJSAvailable] = useState(false);
  const maxRetries = 3;

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
    setIsLoading(false);
    onError?.(errorMessage);
  };

  // Check if Video.js is available
  useEffect(() => {
    // Check if Video.js is loaded globally
    if (typeof window !== 'undefined' && (window as any).videojs) {
      setVideoJSAvailable(true);
    } else {
      // Try to load Video.js dynamically
      const script = document.createElement('script');
      script.src = 'https://vjs.zencdn.net/8.6.1/video.min.js';
      script.onload = () => {
        setVideoJSAvailable(true);
      };
      script.onerror = () => {
        console.warn('Failed to load Video.js, falling back to native player');
        setVideoJSAvailable(false);
      };
      
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = 'https://vjs.zencdn.net/8.6.1/video-js.css';
      
      document.head.appendChild(link);
      document.head.appendChild(script);
      
      return () => {
        document.head.removeChild(script);
        document.head.removeChild(link);
      };
    }
  }, []);

  const initializeVideoJS = async () => {
    if (!videoRef.current || !streamUrl || !videoJSAvailable) return;

    try {
      setIsLoading(true);
      setError(null);

      const videojs = (window as any).videojs;
      
      // Dispose of existing player
      if (playerRef.current) {
        playerRef.current.dispose();
        playerRef.current = null;
      }

      // Configure Video.js options
      const options: any = {
        controls: true,
        responsive: true,
        fluid: true,
        autoplay: true,
        muted: true,
        preload: 'auto',
        sources: [{
          src: streamUrl,
          type: streamUrl.startsWith('rtmp://') ? 'rtmp/mp4' : 'application/x-mpegURL'
        }],
        html5: {
          vhs: {
            overrideNative: true
          }
        }
      };

      // Add RTMP plugin configuration if available
      if (streamUrl.startsWith('rtmp://')) {
        options.techOrder = ['flash', 'html5'];
        options.flash = {
          swf: 'https://vjs.zencdn.net/swf/5.4.2/video-js.swf'
        };
      }

      // Initialize Video.js player
      playerRef.current = videojs(videoRef.current, options);

      // Set up event listeners
      playerRef.current.ready(() => {
        console.log('Video.js player ready');
        setIsLoading(false);
        onLoad?.();
      });

      playerRef.current.on('error', (e: any) => {
        console.error('Video.js error:', e);
        const error = playerRef.current.error();
        if (error) {
          console.error('Video.js error details:', error);
          if (retryCount < maxRetries) {
            setTimeout(() => {
              setRetryCount(prev => prev + 1);
              initializeVideoJS();
            }, 2000);
          } else {
            handleError(`Video playback failed: ${error.message || 'Unknown error'}`);
          }
        }
      });

      playerRef.current.on('loadstart', () => {
        setIsLoading(true);
      });

      playerRef.current.on('canplay', () => {
        setIsLoading(false);
      });

    } catch (err) {
      console.error('Video.js initialization error:', err);
      handleError('Failed to initialize Video.js player');
    }
  };

  const initializeFallbackPlayer = async () => {
    if (!videoRef.current || !streamUrl) return;

    try {
      setIsLoading(true);
      setError(null);

      const video = videoRef.current;
      video.src = streamUrl;
      video.autoplay = true;
      video.muted = true;
      video.playsInline = true;
      video.controls = true;

      video.oncanplay = () => {
        setIsLoading(false);
        setError(null);
        onLoad?.();
      };

      video.onerror = (e) => {
        console.error('Fallback video error:', e);
        if (retryCount < maxRetries) {
          setTimeout(() => {
            setRetryCount(prev => prev + 1);
            initializeFallbackPlayer();
          }, 2000);
        } else {
          handleError('Unable to load video stream with fallback player');
        }
      };

      video.load();

    } catch (err) {
      console.error('Fallback player error:', err);
      handleError('Failed to initialize fallback player');
    }
  };

  const handleRetry = () => {
    setRetryCount(0);
    if (videoJSAvailable) {
      initializeVideoJS();
    } else {
      initializeFallbackPlayer();
    }
  };

  useEffect(() => {
    if (streamUrl) {
      if (videoJSAvailable) {
        console.log('Using Video.js for stream:', streamUrl);
        initializeVideoJS();
      } else {
        console.log('Using fallback player for stream:', streamUrl);
        initializeFallbackPlayer();
      }
    }

    return () => {
      if (playerRef.current) {
        playerRef.current.dispose();
        playerRef.current = null;
      }
    };
  }, [streamUrl, videoJSAvailable]);

  if (error) {
    return (
      <div className={`relative bg-gray-900 rounded-lg overflow-hidden ${className}`}>
        <div className="aspect-video flex items-center justify-center p-8">
          <div className="text-center max-w-lg">
            <Alert className="border-red-200 bg-red-50 mb-4">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-700 text-left">
                {error}
              </AlertDescription>
            </Alert>
            
            <div className="text-white text-sm space-y-3">
              <p>Stream URL: <code className="bg-gray-800 px-2 py-1 rounded text-xs break-all">{streamUrl}</code></p>
              <p>Player: {videoJSAvailable ? 'Video.js' : 'Native HTML5'}</p>
              
              {streamUrl.startsWith('rtmp://') && (
                <div className="text-left">
                  <p className="text-gray-300 mb-2">For better RTMP support:</p>
                  <ul className="text-gray-400 text-xs space-y-1">
                    <li>• Install Video.js with Flash plugin</li>
                    <li>• Configure server-side RTMP to HLS conversion</li>
                    <li>• Use WebRTC for real-time streaming</li>
                  </ul>
                  <a 
                    href="https://videojs.com/plugins/" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-1 text-blue-400 hover:text-blue-300 text-xs mt-2"
                  >
                    <ExternalLink className="h-3 w-3" />
                    Video.js Plugins
                  </a>
                </div>
              )}
              
              <Button onClick={handleRetry} variant="outline" className="mt-4">
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry ({retryCount}/{maxRetries})
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative bg-black rounded-lg overflow-hidden ${className}`}>
      <video
        ref={videoRef}
        className="video-js vjs-default-skin w-full h-full"
        data-setup="{}"
      />
      
      {isLoading && (
        <div className="absolute inset-0 bg-black bg-opacity-75 flex items-center justify-center">
          <div className="text-center text-white">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p>Loading stream...</p>
            <p className="text-sm text-gray-300 mt-1">
              Using {videoJSAvailable ? 'Video.js' : 'Native'} player
            </p>
            {retryCount > 0 && (
              <p className="text-sm text-gray-300 mt-1">
                Retry attempt {retryCount}/{maxRetries}
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
