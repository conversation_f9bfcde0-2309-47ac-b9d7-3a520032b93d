"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Camera,
  Edit,
  Trash2,
  Eye,
  Wifi,
  WifiOff,
  AlertTriangle,
  Settings,
  Play,
  Lock,
  Video
} from "lucide-react";
import { Camera as CameraType } from "@/api/camera";
import { LiveStreamModal } from "./LiveStreamModal";

interface CameraTableProps {
  cameras: CameraType[];
  onEdit: (camera: CameraType) => void;
  onDelete: (camera: CameraType) => void;
  onView: (camera: CameraType) => void;
  isLoading?: boolean;
  totalCount: number;
}

export const CameraTable = ({
  cameras,
  onEdit,
  onDelete,
  onView,
  isLoading = false,
  totalCount,
}: CameraTableProps) => {
  const [selectedCamera, setSelectedCamera] = useState<CameraType | null>(null);
  const [isStreamModalOpen, setIsStreamModalOpen] = useState(false);

  const handleCameraClick = (camera: CameraType) => {
    setSelectedCamera(camera);
    setIsStreamModalOpen(true);
  };

  const closeStreamModal = () => {
    setIsStreamModalOpen(false);
    setSelectedCamera(null);
  };
  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return <Wifi className="h-4 w-4 text-green-600" />;
      case "lock":
        return <Lock className="h-4 w-4 text-red-600" />;
      case "offline":
        return <WifiOff className="h-4 w-4 text-red-600" />;
      case "maintenance":
        return <Settings className="h-4 w-4 text-yellow-600" />;
      case "error":
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default:
        return <Camera className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">Hoạt động</Badge>;
      case "lock":
        return <Badge variant="destructive" className="bg-red-100 text-red-800 border-red-200">Khóa</Badge>;
      case "offline":
        return <Badge variant="destructive">Offline</Badge>;
      case "maintenance":
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 border-yellow-200">Bảo trì</Badge>;
      case "error":
        return <Badge variant="destructive">Lỗi</Badge>;
      default:
        return <Badge variant="outline" className="capitalize">{status}</Badge>;
    }
  };

  const hasLiveStream = (camera: CameraType) => {
    return camera.stream_url && camera.stream_url.trim() !== '';
  };

  const formatMacAddress = (mac: string) => {
    // Ensure consistent format with colons
    return mac.replace(/[-]/g, ':').toUpperCase();
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Danh sách Camera AI</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
              <p className="text-gray-500">Đang tải dữ liệu...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <Camera className="h-5 w-5" />
          Danh sách Camera AI ({totalCount})
        </CardTitle>
      </CardHeader>
      <CardContent>
        {cameras.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Camera className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p className="text-lg font-medium mb-2">Không có camera nào</p>
            <p className="text-sm">Chưa có camera nào được thêm vào hệ thống</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[50px]">STT</TableHead>
                  <TableHead>Tên camera</TableHead>
                  <TableHead>Vị trí</TableHead>
                  <TableHead>Địa chỉ MAC</TableHead>
                  <TableHead>Địa chỉ IP</TableHead>
                  <TableHead>Trạng thái</TableHead>
                  <TableHead className="text-right">Thao tác</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {cameras.map((camera, index) => (
                  <TableRow
                    key={camera.id}
                    className={`hover:bg-gray-50 ${hasLiveStream(camera) ? 'cursor-pointer' : ''}`}
                    onClick={() => hasLiveStream(camera) && handleCameraClick(camera)}
                  >
                    <TableCell className="font-medium">
                      {index + 1}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(camera.status)}
                        <span className="font-medium">{camera.name}</span>
                        {hasLiveStream(camera) && (
                          <div title="Có live stream">
                            <Video className="h-4 w-4 text-blue-600" />
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-gray-600">
                        {camera.building_info && camera.floor_info && camera.room_info ? (
                          <div className="space-y-1">
                            <div className="text-sm font-medium">{camera.room_info.name}</div>
                            <div className="text-xs text-gray-500">
                              {camera.building_info.name} - {camera.floor_info.name}
                            </div>
                          </div>
                        ) : (
                          <span className="text-gray-400 italic">Chưa có thông tin vị trí</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                        {formatMacAddress(camera.mac_address)}
                      </code>
                    </TableCell>
                    <TableCell>
                      {camera.ip_address ? (
                        <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                          {camera.ip_address}
                        </code>
                      ) : (
                        <span className="text-gray-400 italic">N/A</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(camera.status)}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end gap-2">
                        {hasLiveStream(camera) && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleCameraClick(camera);
                            }}
                            className="h-8 w-8 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                            title="Xem live stream"
                          >
                            <Play className="h-4 w-4" />
                          </Button>
                        )}
                        
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            onEdit(camera);
                          }}
                          className="h-8 w-8 p-0"
                          title="Chỉnh sửa"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            onDelete(camera);
                          }}
                          className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                          title="Xóa"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>

      {/* Live Stream Modal */}
      <LiveStreamModal
        isOpen={isStreamModalOpen}
        onClose={closeStreamModal}
        camera={selectedCamera}
      />
    </Card>
  );
};
