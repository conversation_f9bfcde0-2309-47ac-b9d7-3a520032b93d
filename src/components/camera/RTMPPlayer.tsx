"use client";

import { useEffect, useRef, useState } from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { AlertTriangle, RefreshCw, Loader2 } from "lucide-react";

interface RTMPPlayerProps {
  streamUrl: string;
  className?: string;
  onError?: (error: string) => void;
  onLoad?: () => void;
}

export const RTMPPlayer = ({
  streamUrl,
  className = "",
  onError,
  onLoad,
}: RTMPPlayerProps) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const hlsRef = useRef<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [hlsSupported, setHlsSupported] = useState(false);
  const maxRetries = 3;

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
    setIsLoading(false);
    onError?.(errorMessage);
  };

  // Check and load HLS.js if needed
  useEffect(() => {
    const checkHLSSupport = async () => {
      // Check if HLS.js is already loaded
      if ((window as any).Hls) {
        setHlsSupported(true);
        return;
      }

      // Check if we need HLS.js (for .m3u8 files)
      if (streamUrl.includes('.m3u8')) {
        try {
          // Load HLS.js from CDN
          const script = document.createElement('script');
          script.src = 'https://cdn.jsdelivr.net/npm/hls.js@1.4.12/dist/hls.min.js';
          script.onload = () => {
            console.log('✅ HLS.js loaded successfully');
            setHlsSupported(true);
          };
          script.onerror = () => {
            console.error('❌ Failed to load HLS.js');
            setHlsSupported(false);
          };
          document.head.appendChild(script);
        } catch (err) {
          console.error('Error loading HLS.js:', err);
          setHlsSupported(false);
        }
      }
    };

    checkHLSSupport();
  }, [streamUrl]);

  // Initialize HLS player
  const initializeHLSPlayer = async () => {
    if (!videoRef.current || !streamUrl.includes('.m3u8')) return false;

    const video = videoRef.current;
    const Hls = (window as any).Hls;

    if (!Hls) {
      console.error('HLS.js not available');
      return false;
    }

    try {
      // Clean up existing HLS instance
      if (hlsRef.current) {
        hlsRef.current.destroy();
        hlsRef.current = null;
      }

      if (Hls.isSupported()) {
        console.log('🎯 Using HLS.js for .m3u8 stream');
        const hls = new Hls({
          debug: false,
          enableWorker: true,
          lowLatencyMode: true,
          backBufferLength: 90,
        });

        hlsRef.current = hls;

        hls.loadSource(streamUrl);
        hls.attachMedia(video);

        hls.on(Hls.Events.MANIFEST_PARSED, () => {
          console.log('✅ HLS manifest parsed successfully');
          setIsLoading(false);
          setError(null);
          onLoad?.();
          video.play().catch(e => console.warn('Autoplay prevented:', e));
        });

        hls.on(Hls.Events.ERROR, (_event: any, data: any) => {
          console.error('❌ HLS.js error:', data);
          if (data.fatal) {
            switch (data.type) {
              case Hls.ErrorTypes.NETWORK_ERROR:
                handleError(
                  `❌ Network Error: Không thể tải HLS stream\n\n` +
                  `🔍 Có thể do:\n` +
                  `• CORS policy - Server không cho phép browser truy cập\n` +
                  `• Network connectivity issues\n` +
                  `• Server không phản hồi\n\n` +
                  `💡 Giải pháp:\n` +
                  `• Kiểm tra CORS headers trên server\n` +
                  `• Thử truy cập trực tiếp: ${streamUrl}\n` +
                  `• Kiểm tra network connectivity`
                );
                break;
              case Hls.ErrorTypes.MEDIA_ERROR:
                handleError('❌ Media Error: Lỗi decode video stream');
                break;
              default:
                handleError(`❌ HLS Error: ${data.details || 'Unknown error'}`);
                break;
            }
          }
        });

        return true;
      } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
        // Safari native HLS support
        console.log('🍎 Using Safari native HLS support');
        video.src = streamUrl;
        video.addEventListener('loadedmetadata', () => {
          setIsLoading(false);
          setError(null);
          onLoad?.();
        });
        video.addEventListener('error', (e) => {
          console.error('Safari HLS error:', e);
          handleError('❌ Safari HLS Error: Không thể phát stream');
        });
        return true;
      }
    } catch (err) {
      console.error('HLS initialization error:', err);
      return false;
    }

    return false;
  };

  const tryRTMPConversion = (rtmpUrl: string): string[] => {
    // Extract the stream key from RTMP URL
    // Format: rtmp://server:port/app/streamkey
    const urlParts = rtmpUrl.replace('rtmp://', '').split('/');
    if (urlParts.length < 3) return [];

    const server = urlParts[0];
    const app = urlParts[1];
    const streamKey = urlParts.slice(2).join('/');

    // Try multiple conversion approaches
    const possibleUrls = [
      // Try HLS conversion (common pattern)
      `http://${server.replace(':1935', ':8080')}/hls/${streamKey}.m3u8`,
      `http://${server.replace(':1935', ':8080')}/hls/${streamKey}/index.m3u8`,
      `https://${server.replace(':1935', ':8080')}/hls/${streamKey}.m3u8`,

      // Try HTTP-FLV (another common pattern)
      `http://${server.replace(':1935', ':8080')}/live/${streamKey}.flv`,
      `https://${server.replace(':1935', ':8080')}/live/${streamKey}.flv`,

      // Try WebRTC conversion
      `http://${server.replace(':1935', ':1985')}/rtc/v1/whep/?app=${app}&stream=${streamKey}`,

      // Try direct MP4 stream (some servers support this)
      `http://${server.replace(':1935', ':8080')}/live/${streamKey}.mp4`,
    ];

    return possibleUrls;
  };

  const initializePlayer = async () => {
    if (!videoRef.current || !streamUrl) return;

    try {
      setIsLoading(true);
      setError(null);

      // Try HLS player first for .m3u8 files
      if (streamUrl.includes('.m3u8')) {
        console.log('🎯 HLS stream detected, trying HLS.js player...');
        const hlsSuccess = await initializeHLSPlayer();
        if (hlsSuccess) {
          return; // HLS player initialized successfully
        }
        console.log('⚠️ HLS.js failed, falling back to native player...');
      }

      const video = videoRef.current;
      let playableUrls: string[] = [];

      // Handle RTMP URLs by trying conversion approaches
      if (streamUrl.startsWith('rtmp://')) {
        console.log('⚠️ RTMP stream detected - browsers do NOT support RTMP natively!', streamUrl);
        console.log('🔄 Attempting to find converted web-compatible formats...');
        playableUrls = tryRTMPConversion(streamUrl);

        // Note: We don't try original RTMP URL as browsers cannot play it
        // The original RTMP URL would always fail in modern browsers
      } else {
        // For non-RTMP URLs, use directly
        playableUrls = [streamUrl];
      }

      // Try each URL until one works
      let urlIndex = 0;

      const tryNextUrl = async (): Promise<void> => {
        if (urlIndex >= playableUrls.length) {
          // All URLs failed, show appropriate error
          if (streamUrl.startsWith('rtmp://')) {
            handleError(
              'Unable to play RTMP stream. This may require:\n' +
              '• A media server that converts RTMP to HLS/WebRTC\n' +
              '• Browser plugins or specialized players\n' +
              '• Server-side stream conversion setup\n\n' +
              `Original URL: ${streamUrl}`
            );
          } else {
            handleError('Unable to load video stream. Please check the stream URL and try again.');
          }
          return;
        }

        const currentUrl = playableUrls[urlIndex];
        console.log(`Trying stream URL ${urlIndex + 1}/${playableUrls.length}:`, currentUrl);

        // Set up video element
        video.src = currentUrl;
        video.autoplay = true;
        video.muted = true; // Start muted to allow autoplay
        video.playsInline = true;

        // Create a promise that resolves when video can play or rejects on error
        const loadPromise = new Promise<void>((resolve, reject) => {
          const cleanup = () => {
            video.onloadstart = null;
            video.oncanplay = null;
            video.onerror = null;
            video.onabort = null;
          };

          video.onloadstart = () => {
            setIsLoading(true);
          };

          video.oncanplay = () => {
            setIsLoading(false);
            setError(null);
            onLoad?.();
            cleanup();
            resolve();
          };

          video.onerror = (e) => {
            console.warn(`Failed to load URL ${urlIndex + 1}:`, currentUrl, e);
            cleanup();
            reject(e);
          };

          video.onabort = () => {
            console.warn(`Loading aborted for URL ${urlIndex + 1}:`, currentUrl);
            cleanup();
            reject(new Error('Loading aborted'));
          };
        });

        try {
          video.load();
          await loadPromise;

          // Success! Set up ongoing event listeners
          video.onstalled = () => {
            console.warn('Video stream stalled');
          };

          video.onwaiting = () => {
            setIsLoading(true);
          };

          video.onplaying = () => {
            setIsLoading(false);
          };

        } catch (err) {
          // This URL failed, try the next one
          urlIndex++;
          await tryNextUrl();
        }
      };

      await tryNextUrl();

    } catch (err) {
      console.error('Player initialization error:', err);
      if (retryCount < maxRetries) {
        setTimeout(() => {
          setRetryCount(prev => prev + 1);
          initializePlayer();
        }, 2000);
      } else {
        handleError('Failed to initialize video player after multiple attempts.');
      }
    }
  };

  const handleRetry = () => {
    setRetryCount(0);
    initializePlayer();
  };

  useEffect(() => {
    if (streamUrl) {
      console.log('RTMPPlayer: Initializing with stream URL:', streamUrl);
      initializePlayer();
    }

    return () => {
      // Clean up HLS instance
      if (hlsRef.current) {
        console.log('RTMPPlayer: Cleaning up HLS instance');
        hlsRef.current.destroy();
        hlsRef.current = null;
      }

      // Clean up video element
      if (videoRef.current) {
        console.log('RTMPPlayer: Cleaning up video element');
        videoRef.current.pause();
        videoRef.current.src = '';
      }
    };
  }, [streamUrl]);

  if (error) {
    return (
      <div className={`relative bg-gray-900 rounded-lg overflow-hidden ${className}`}>
        <div className="aspect-video flex items-center justify-center p-8">
          <div className="text-center max-w-lg">
            <Alert className="border-red-200 bg-red-50 mb-4">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-700 text-left whitespace-pre-line">
                {error}
              </AlertDescription>
            </Alert>

            <div className="text-white text-sm space-y-3">
              <p>Stream URL: <code className="bg-gray-800 px-2 py-1 rounded text-xs break-all">{streamUrl}</code></p>

              <div className="text-left">
                <p className="text-gray-300 mb-2">
                  {streamUrl.startsWith('rtmp://') ? 'Attempted conversion methods:' : 'Troubleshooting steps:'}
                </p>
                <ul className="text-gray-400 text-xs space-y-1">
                  {streamUrl.startsWith('rtmp://') ? (
                    <>
                      <li>• HLS conversion (HTTP Live Streaming)</li>
                      <li>• HTTP-FLV streaming</li>
                      <li>• WebRTC conversion</li>
                      <li>• Direct MP4 streaming</li>
                    </>
                  ) : streamUrl.includes('.m3u8') ? (
                    <>
                      <li>• HLS.js library loaded: {hlsSupported ? '✅' : '❌'}</li>
                      <li>• Check CORS headers on server</li>
                      <li>• Try accessing URL directly in browser</li>
                      <li>• Check network connectivity</li>
                      <li>• Verify stream is active and accessible</li>
                    </>
                  ) : (
                    <>
                      <li>• Check if URL is accessible</li>
                      <li>• Verify video format is supported</li>
                      <li>• Check CORS policy</li>
                      <li>• Try different video format</li>
                    </>
                  )}
                </ul>

                {streamUrl.includes('.m3u8') && (
                  <div className="mt-3 p-2 bg-blue-900 rounded text-xs">
                    <p className="text-blue-300 font-medium">HLS Stream Debug:</p>
                    <p className="text-blue-200">
                      • HLS.js: {hlsSupported ? 'Loaded' : 'Failed to load'}<br/>
                      • Try opening URL directly: <br/>
                      <a href={streamUrl} target="_blank" rel="noopener noreferrer"
                         className="text-blue-400 hover:text-blue-300 underline break-all">
                        {streamUrl}
                      </a>
                    </p>
                  </div>
                )}
              </div>

              <Button onClick={handleRetry} variant="outline" className="mt-4">
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry ({retryCount}/{maxRetries})
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative bg-black rounded-lg overflow-hidden ${className}`}>
      <video
        ref={videoRef}
        className="w-full h-full object-contain"
        controls
        playsInline
        muted
      />
      
      {isLoading && (
        <div className="absolute inset-0 bg-black bg-opacity-75 flex items-center justify-center">
          <div className="text-center text-white">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p>Loading stream...</p>
            {retryCount > 0 && (
              <p className="text-sm text-gray-300 mt-1">
                Retry attempt {retryCount}/{maxRetries}
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
