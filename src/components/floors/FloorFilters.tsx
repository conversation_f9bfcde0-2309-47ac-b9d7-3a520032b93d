"use client";

import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Search } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Building } from "@/api/building";

interface FloorFiltersProps {
  searchName: string;
  selectedBuildingId: string;
  buildings: Building[];
  onSearchChange: (field: 'name', value: string) => void;
  onBuildingChange: (buildingId: string) => void;
  onSearch: () => void;
  onClearFilters: () => void;
}

export const FloorFilters = ({
  searchName,
  selectedBuildingId,
  buildings,
  onSearchChange,
  onBuildingChange,
  onSearch,
  onClearFilters,
}: FloorFiltersProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">T<PERSON><PERSON> kiếm và lọc</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="search-name">Tên tầng</Label>
            <Input
              id="search-name"
              placeholder="Nhập tên tầng..."
              value={searchName}
              onChange={(e) => onSearchChange('name', e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="building-select">Tòa nhà</Label>
            <Select value={selectedBuildingId} onValueChange={onBuildingChange}>
              <SelectTrigger>
                <SelectValue placeholder="Chọn tòa nhà" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả tòa nhà</SelectItem>
                {buildings.map((building) => (
                  <SelectItem key={building.id} value={building.id.toString()}>
                    {building.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex items-end gap-2">
            <Button onClick={onSearch} className="flex-1">
              <Search className="h-4 w-4 mr-2" />
              Tìm kiếm
            </Button>
            <Button onClick={onClearFilters} variant="outline">
              Xóa bộ lọc
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
