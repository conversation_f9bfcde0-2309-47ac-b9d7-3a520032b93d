"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2 } from "lucide-react";
import {
  Di<PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Floor, FloorFormData } from "@/api/floor";
import { Building } from "@/api/building";

interface FloorFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  editingFloor: Floor | null;
  buildings: Building[];
  onSubmit: (formData: FloorFormData) => Promise<void>;
  isSubmitting: boolean;
}

export const FloorFormDialog = ({
  isOpen,
  onClose,
  editingFloor,
  buildings,
  onSubmit,
  isSubmitting,
}: FloorFormDialogProps) => {
  // Form state
  const [formData, setFormData] = useState<FloorFormData>({
    name: "",
    building_id: 0,
  });
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Initialize form data when dialog opens or editing floor changes
  useEffect(() => {
    if (isOpen) {
      if (editingFloor) {
        setFormData({
          name: editingFloor.name,
          building_id: editingFloor.building_info?.id || 0,
        });
      } else {
        setFormData({
          name: "",
          building_id: 0,
        });
      }
      setValidationErrors({});
    }
  }, [isOpen, editingFloor]);

  // Form validation
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = "Tên tầng là bắt buộc";
    } else if (formData.name.trim().length < 2) {
      errors.name = "Tên tầng phải có ít nhất 2 ký tự";
    } else if (formData.name.trim().length > 100) {
      errors.name = "Tên tầng không được vượt quá 100 ký tự";
    }

    if (!formData.building_id || formData.building_id === 0) {
      errors.building_id = "Vui lòng chọn tòa nhà";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Form handlers
  const handleInputChange = (field: keyof FloorFormData, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    await onSubmit(formData);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[400px]">
        <DialogHeader>
          <DialogTitle>
            {editingFloor ? "Chỉnh sửa tầng" : "Thêm tầng mới"}
          </DialogTitle>
          <DialogDescription>
            {editingFloor 
              ? "Cập nhật thông tin tầng dưới đây." 
              : "Nhập thông tin tầng mới dưới đây."
            }
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Tên tầng *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              placeholder="Nhập tên tầng"
              className={validationErrors.name ? "border-red-500" : ""}
              disabled={isSubmitting}
            />
            {validationErrors.name && (
              <p className="text-sm text-red-600">{validationErrors.name}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="building">Tòa nhà *</Label>
            <Select 
              value={formData.building_id.toString()} 
              onValueChange={(value) => handleInputChange("building_id", parseInt(value))}
              disabled={isSubmitting}
            >
              <SelectTrigger className={validationErrors.building_id ? "border-red-500" : ""}>
                <SelectValue placeholder="Chọn tòa nhà" />
              </SelectTrigger>
              <SelectContent>
                {buildings.map((building) => (
                  <SelectItem key={building.id} value={building.id.toString()}>
                    {building.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {validationErrors.building_id && (
              <p className="text-sm text-red-600">{validationErrors.building_id}</p>
            )}
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
              Hủy
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Đang lưu...
                </>
              ) : (
                editingFloor ? "Cập nhật" : "Thêm mới"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
