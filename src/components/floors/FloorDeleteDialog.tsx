"use client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Floor } from "@/api/floor";

interface FloorDeleteDialogProps {
  isOpen: boolean;
  onClose: () => void;
  floor: Floor | null;
  onConfirm: () => void;
  isDeleting?: boolean;
}

export const FloorDeleteDialog = ({
  isOpen,
  onClose,
  floor,
  onConfirm,
  isDeleting = false,
}: FloorDeleteDialogProps) => {
  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Xác nhận xóa tầng</AlertDialogTitle>
          <AlertDialogDescription>
            Bạn có chắc chắn muốn xóa tầng `{floor?.name}` không? 
            Hành động này không thể hoàn tác và sẽ ảnh hưởng đến tất cả các phòng học trong tầng này.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={onClose} disabled={isDeleting}>
            Hủy
          </AlertDialogCancel>
          <AlertDialogAction 
            onClick={onConfirm}
            disabled={isDeleting}
            className="bg-red-600 hover:bg-red-700"
          >
            {isDeleting ? "Đang xóa..." : "Xóa"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
