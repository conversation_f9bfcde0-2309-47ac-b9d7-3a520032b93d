"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Layers, 
  Edit, 
  Trash2, 
  MoreHorizontal,
  ChevronDown,
  ChevronRight,
  Building2,
  DoorOpen
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Floor, Room, BuildingInfo } from "@/api/floor";

interface FloorTableProps {
  floors: Floor[];
  floorDetails: Record<number, { building_info: BuildingInfo; rooms: Room[] }>;
  onEdit: (floor: Floor) => void;
  onDelete: (floor: Floor) => void;
  onLoadFloorDetails: (floorId: number) => void;
  isLoading?: boolean;
  totalCount: number;
}

export const FloorTable = ({
  floors,
  floorDetails,
  onEdit,
  onDelete,
  onLoadFloorDetails,
  isLoading = false,
  totalCount,
}: FloorTableProps) => {
  const [expandedFloors, setExpandedFloors] = useState<Set<number>>(new Set());

  const toggleFloorExpansion = (floorId: number) => {
    const newExpanded = new Set(expandedFloors);
    if (newExpanded.has(floorId)) {
      newExpanded.delete(floorId);
    } else {
      newExpanded.add(floorId);
      // Load floor details if not already loaded
      if (!floorDetails[floorId]) {
        onLoadFloorDetails(floorId);
      }
    }
    setExpandedFloors(newExpanded);
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center text-gray-500">
            Đang tải dữ liệu...
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">
          Danh sách tầng ({totalCount})
        </CardTitle>
      </CardHeader>
      <CardContent>
        {floors.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            Không có tầng nào được tìm thấy
          </div>
        ) : (
          <div className="space-y-2">
            {floors.map((floor) => (
              <Collapsible
                key={floor.id}
                open={expandedFloors.has(floor.id)}
                onOpenChange={() => toggleFloorExpansion(floor.id)}
              >
                <div className="border rounded-lg">
                  <div className="flex items-center justify-between p-4 hover:bg-gray-50">
                    <div className="flex items-center gap-4 flex-1">
                      <CollapsibleTrigger asChild>
                        <Button variant="ghost" size="sm" className="p-0 h-auto">
                          {expandedFloors.has(floor.id) ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                        </Button>
                      </CollapsibleTrigger>
                      
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
                          <Layers className="h-5 w-5 text-green-600" />
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900">{floor.name}</h3>
                          <p className="text-sm text-gray-500">ID: {floor.id}</p>
                          {floorDetails[floor.id] && (
                            <p className="text-sm text-blue-600 flex items-center gap-1">
                              <Building2 className="h-3 w-3" />
                              {floorDetails[floor.id].building_info.name}
                            </p>
                          )}
                        </div>
                      </div>
                      
                      <div className="ml-auto flex items-center gap-2">
                        <Badge variant="outline">
                          {floorDetails[floor.id]?.rooms?.length || 0} phòng
                        </Badge>
                      </div>
                    </div>
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Mở menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => onEdit(floor)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Chỉnh sửa
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => onDelete(floor)}
                          className="text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Xóa
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  
                  <CollapsibleContent>
                    <div className="border-t bg-gray-50 p-4">
                      <h4 className="font-medium text-gray-900 mb-3">Danh sách phòng</h4>
                      {floorDetails[floor.id] ? (
                        floorDetails[floor.id].rooms.length > 0 ? (
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                            {floorDetails[floor.id].rooms.map((room) => (
                              <div key={room.id} className="flex items-center gap-3 p-3 bg-white rounded-lg border">
                                <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                                  <DoorOpen className="h-4 w-4 text-gray-600" />
                                </div>
                                <div className="flex-1 min-w-0">
                                  <p className="font-medium text-sm text-gray-900 truncate">{room.name}</p>
                                  <p className="text-xs text-gray-500">ID: {room.id}</p>
                                </div>
                                <Badge 
                                  variant={room.status === "active" ? "default" : "secondary"}
                                  className="text-xs"
                                >
                                  {room.status === "active" ? "Hoạt động" : "Không hoạt động"}
                                </Badge>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <p className="text-gray-500 text-sm">Chưa có phòng nào trong tầng này</p>
                        )
                      ) : (
                        <div className="text-center py-4">
                          <div className="text-gray-500 text-sm">Đang tải danh sách phòng...</div>
                        </div>
                      )}
                    </div>
                  </CollapsibleContent>
                </div>
              </Collapsible>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
