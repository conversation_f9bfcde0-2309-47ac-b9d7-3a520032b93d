"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Loader2, UserPlus } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ProfessionalTeam } from "@/api/professionalTeam";
import { Teacher } from "@/api/teacher";

interface TeacherAssignmentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  professionalTeam: ProfessionalTeam | null;
  availableTeachers: Teacher[];
  assignedTeacherIds: number[];
  onAssignTeacher: (teacherId: number) => Promise<void>;
  isAssigning: boolean;
}

export const TeacherAssignmentDialog = ({
  isOpen,
  onClose,
  professionalTeam,
  availableTeachers,
  assignedTeacherIds,
  onAssignTeacher,
  isAssigning,
}: TeacherAssignmentDialogProps) => {
  const [selectedTeacherId, setSelectedTeacherId] = useState<string>("0");
  const [validationError, setValidationError] = useState<string>("");

  // Filter out already assigned teachers
  const unassignedTeachers = availableTeachers.filter(
    teacher => !assignedTeacherIds.includes(teacher.id)
  );

  console.log("assignedTeacherIds", assignedTeacherIds);
  console.log("availableTeachers", availableTeachers)
  console.log("unassignedTeachers", unassignedTeachers);

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (isOpen) {
      setSelectedTeacherId("0");
      setValidationError("");
    }
  }, [isOpen]);

  // Form validation
  const validateForm = (): boolean => {
    if (!selectedTeacherId || selectedTeacherId === "0") {
      setValidationError("Vui lòng chọn giáo viên");
      return false;
    }
    setValidationError("");
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    await onAssignTeacher(parseInt(selectedTeacherId));
  };

  const handleTeacherChange = (value: string) => {
    setSelectedTeacherId(value);
    if (validationError) {
      setValidationError("");
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[400px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5 text-blue-600" />
            Thêm giáo viên vào tổ
          </DialogTitle>
          <DialogDescription>
            Chọn giáo viên để thêm vào tổ chuyên môn `{professionalTeam?.name}`.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="teacher-select">Chọn giáo viên *</Label>
            <Select 
              value={selectedTeacherId} 
              onValueChange={handleTeacherChange}
              disabled={isAssigning}
            >
              <SelectTrigger className={validationError ? "border-red-500" : ""}>
                <SelectValue placeholder="Chọn giáo viên" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0">Chọn giáo viên</SelectItem>
                {unassignedTeachers.map((teacher) => (
                  <SelectItem key={teacher.id} value={teacher.id.toString()}>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{teacher.name}</span>
                      <span className="text-sm text-gray-500">({teacher.email})</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {validationError && (
              <p className="text-sm text-red-600">{validationError}</p>
            )}
            {unassignedTeachers.length === 0 && (
              <p className="text-sm text-gray-500">
                Tất cả giáo viên đã được phân công vào tổ chuyên môn
              </p>
            )}
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose} disabled={isAssigning}>
              Hủy
            </Button>
            <Button 
              type="submit" 
              disabled={isAssigning || unassignedTeachers.length === 0}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isAssigning ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Đang thêm...
                </>
              ) : (
                <>
                  <UserPlus className="mr-2 h-4 w-4" />
                  Thêm giáo viên
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
