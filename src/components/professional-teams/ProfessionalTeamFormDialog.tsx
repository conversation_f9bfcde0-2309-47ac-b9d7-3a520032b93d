"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ProfessionalTeam, ProfessionalTeamFormData } from "@/api/professionalTeam";

interface ProfessionalTeamFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  editingTeam: ProfessionalTeam | null;
  onSubmit: (formData: ProfessionalTeamFormData) => Promise<void>;
  isSubmitting: boolean;
}

export const ProfessionalTeamFormDialog = ({
  isOpen,
  onClose,
  editingTeam,
  onSubmit,
  isSubmitting,
}: ProfessionalTeamFormDialogProps) => {
  // Form state
  const [formData, setFormData] = useState<ProfessionalTeamFormData>({
    name: "",
  });
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Initialize form data when dialog opens or editing team changes
  useEffect(() => {
    if (isOpen) {
      if (editingTeam) {
        setFormData({
          name: editingTeam.name,
        });
      } else {
        setFormData({
          name: "",
        });
      }
      setValidationErrors({});
    }
  }, [isOpen, editingTeam]);

  // Form validation
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = "Tên tổ chuyên môn là bắt buộc";
    } else if (formData.name.trim().length < 2) {
      errors.name = "Tên tổ chuyên môn phải có ít nhất 2 ký tự";
    } else if (formData.name.trim().length > 100) {
      errors.name = "Tên tổ chuyên môn không được vượt quá 100 ký tự";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Form handlers
  const handleInputChange = (field: keyof ProfessionalTeamFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    await onSubmit(formData);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[400px]">
        <DialogHeader>
          <DialogTitle>
            {editingTeam ? "Chỉnh sửa tổ chuyên môn" : "Thêm tổ chuyên môn mới"}
          </DialogTitle>
          <DialogDescription>
            {editingTeam 
              ? "Cập nhật thông tin tổ chuyên môn dưới đây." 
              : "Nhập thông tin tổ chuyên môn mới dưới đây."
            }
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Tên tổ chuyên môn *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              placeholder="Nhập tên tổ chuyên môn"
              className={validationErrors.name ? "border-red-500" : ""}
              disabled={isSubmitting}
            />
            {validationErrors.name && (
              <p className="text-sm text-red-600">{validationErrors.name}</p>
            )}
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
              Hủy
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Đang lưu...
                </>
              ) : (
                editingTeam ? "Cập nhật" : "Thêm mới"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
