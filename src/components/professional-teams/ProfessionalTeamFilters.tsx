"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Search } from "lucide-react";

interface ProfessionalTeamFiltersProps {
  searchName: string;
  searchId: string;
  onSearchChange: (field: 'name' | 'id', value: string) => void;
  onSearch: () => void;
  onClearFilters: () => void;
}

export const ProfessionalTeamFilters = ({
  searchName,
  searchId,
  onSearchChange,
  onSearch,
  onClearFilters,
}: ProfessionalTeamFiltersProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Tìm kiếm và lọc</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="search-name">Tên tổ chuyên môn</Label>
            <Input
              id="search-name"
              placeholder="Nhập tên tổ chuyên môn..."
              value={searchName}
              onChange={(e) => onSearchChange('name', e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="search-id">ID tổ chuyên môn</Label>
            <Input
              id="search-id"
              placeholder="Nhập ID..."
              value={searchId}
              onChange={(e) => onSearchChange('id', e.target.value)}
              type="number"
            />
          </div>
          <div className="flex items-end gap-2">
            <Button onClick={onSearch} className="flex-1">
              <Search className="h-4 w-4 mr-2" />
              Tìm kiếm
            </Button>
            <Button onClick={onClearFilters} variant="outline">
              Xóa bộ lọc
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
