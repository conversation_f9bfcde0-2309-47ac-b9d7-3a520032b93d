"use client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { ProfessionalTeam } from "@/api/professionalTeam";

interface ProfessionalTeamDeleteDialogProps {
  isOpen: boolean;
  onClose: () => void;
  team: ProfessionalTeam | null;
  onConfirm: () => void;
  isDeleting?: boolean;
}

export const ProfessionalTeamDeleteDialog = ({
  isOpen,
  onClose,
  team,
  onConfirm,
  isDeleting = false,
}: ProfessionalTeamDeleteDialogProps) => {
  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Xác nhận xóa tổ chuyên môn</AlertDialogTitle>
          <AlertDialogDescription>
            Bạn có chắc chắn muốn xóa tổ chuyên môn `{team?.name}` không? 
            Hành động này không thể hoàn tác và sẽ ảnh hưởng đến tất cả giáo viên trong tổ này.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={onClose} disabled={isDeleting}>
            Hủy
          </AlertDialogCancel>
          <AlertDialogAction 
            onClick={onConfirm}
            disabled={isDeleting}
            className="bg-red-600 hover:bg-red-700"
          >
            {isDeleting ? "Đang xóa..." : "Xóa"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
