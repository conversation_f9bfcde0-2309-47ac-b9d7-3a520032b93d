"use client";

import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  Edit,
  Trash2,
  MoreHorizontal,
  ChevronDown,
  ChevronRight,
  User,
  UserPlus,
  UserMinus
} from "lucide-react";
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
// Note: Using simple expand/collapse without Collapsible component
import { ProfessionalTeam, Teacher } from "@/api/professionalTeam";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "../ui/collapsible";

interface ProfessionalTeamTableProps {
  teams: ProfessionalTeam[];
  teamDetails: Record<number, Teacher[]>;
  onEdit: (team: ProfessionalTeam) => void;
  onDelete: (team: ProfessionalTeam) => void;
  onLoadTeamDetails: (teamId: number) => void;
  onAddTeacher: (team: ProfessionalTeam) => void;
  onRemoveTeacher: (teacher: Teacher, team: ProfessionalTeam) => void;
  isLoading?: boolean;
  totalCount: number;
}

export const ProfessionalTeamTable = ({
  teams,
  teamDetails,
  onEdit,
  onDelete,
  onLoadTeamDetails,
  onAddTeacher,
  onRemoveTeacher,
  isLoading = false,
  totalCount,
}: ProfessionalTeamTableProps) => {
  const [expandedTeams, setExpandedTeams] = useState<Set<number>>(new Set());

  const toggleTeamExpansion = (teamId: number) => {
    const newExpanded = new Set(expandedTeams);
    if (newExpanded.has(teamId)) {
      newExpanded.delete(teamId);
    } else {
      newExpanded.add(teamId);
      // Load team details if not already loaded
      if (!teamDetails[teamId]) {
        onLoadTeamDetails(teamId);
      }
    }
    setExpandedTeams(newExpanded);
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center text-gray-500">
            Đang tải dữ liệu...
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">
          Danh sách tổ chuyên môn ({totalCount})
        </CardTitle>
      </CardHeader>
      <CardContent>
        {teams.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            Không có tổ chuyên môn nào được tìm thấy
          </div>
        ) : (
          <div className="space-y-2">
            {teams.map((team) => (
              <Collapsible
                key={team.id}
                open={expandedTeams.has(team.id)}
                onOpenChange={() => toggleTeamExpansion(team.id)}
              >
                <div className="border rounded-lg">
                  <div className="flex items-center justify-between p-4 hover:bg-gray-50">
                    <div className="flex items-center gap-4 flex-1">
                      <CollapsibleTrigger asChild>
                        <Button variant="ghost" size="sm" className="p-0 h-auto">
                          {expandedTeams.has(team.id) ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                        </Button>
                      </CollapsibleTrigger>
                      
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                          <Users className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900">{team.name}</h3>
                          <p className="text-sm text-gray-500">ID: {team.id}</p>
                        </div>
                      </div>
                      
                      <div className="ml-auto flex items-center gap-2">
                        <Badge variant="outline">
                          {teamDetails[team.id]?.length || 0} giáo viên
                        </Badge>
                      </div>
                    </div>
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Mở menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => onEdit(team)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Chỉnh sửa
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => onDelete(team)}
                          className="text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Xóa
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  
                  <CollapsibleContent>
                    <div className="border-t bg-gray-50 p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-gray-900">Danh sách giáo viên</h4>
                        <Button
                          size="sm"
                          onClick={() => onAddTeacher(team)}
                          className="bg-blue-600 hover:bg-blue-700"
                        >
                          <UserPlus className="h-4 w-4 mr-2" />
                          Thêm giáo viên
                        </Button>
                      </div>
                      {teamDetails[team.id] ? (
                        teamDetails[team.id].length > 0 ? (
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                            {teamDetails[team.id].map((teacher) => (
                              <div key={teacher.id} className="flex items-center gap-3 p-3 bg-white rounded-lg border group hover:shadow-sm transition-shadow">
                                <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center overflow-hidden">
                                  {teacher.avatar ? (
                                    <img
                                      src={teacher.avatar}
                                      alt={teacher.name}
                                      className="w-full h-full object-cover"
                                    />
                                  ) : (
                                    <User className="h-4 w-4 text-gray-400" />
                                  )}
                                </div>
                                <div className="flex-1 min-w-0">
                                  <p className="font-medium text-sm text-gray-900 truncate">{teacher.name}</p>
                                  <p className="text-xs text-gray-500 truncate">{teacher.phone}</p>
                                </div>
                                <div className="flex items-center gap-2">
                                  <Badge
                                    variant="default"
                                    className="text-xs"
                                  >
                                    Hoạt động
                                  </Badge>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => onRemoveTeacher(teacher, team)}
                                    className="h-6 w-6 p-0 text-red-600 hover:text-red-700 hover:bg-red-50 opacity-0 group-hover:opacity-100 transition-opacity"
                                    title="Xóa khỏi tổ"
                                  >
                                    <UserMinus className="h-3 w-3" />
                                  </Button>
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <p className="text-gray-500 text-sm">Chưa có giáo viên nào trong tổ này</p>
                        )
                      ) : (
                        <div className="text-center py-4">
                          <div className="text-gray-500 text-sm">Đang tải danh sách giáo viên...</div>
                        </div>
                      )}
                    </div>
                  </CollapsibleContent>
                </div>
              </Collapsible>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
