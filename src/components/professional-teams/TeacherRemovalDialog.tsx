"use client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Teacher } from "@/api/professionalTeam";

interface TeacherRemovalDialogProps {
  isOpen: boolean;
  onClose: () => void;
  teacher: Teacher | null;
  professionalTeamName: string;
  onConfirm: () => void;
  isRemoving?: boolean;
}

export const TeacherRemovalDialog = ({
  isOpen,
  onClose,
  teacher,
  professionalTeamName,
  onConfirm,
  isRemoving = false,
}: TeacherRemovalDialogProps) => {
  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Xác nhận xóa giáo viên khỏi tổ</AlertDialogTitle>
          <AlertDialogDescription>
            Bạn có chắc chắn muốn xóa giáo viên `{teacher?.name}` khỏi tổ chuyên môn `{professionalTeamName}` không? 
            Giáo viên sẽ không còn thuộc tổ chuyên môn này nữa.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={onClose} disabled={isRemoving}>
            Hủy
          </AlertDialogCancel>
          <AlertDialogAction 
            onClick={onConfirm}
            disabled={isRemoving}
            className="bg-red-600 hover:bg-red-700"
          >
            {isRemoving ? "Đang xóa..." : "Xóa khỏi tổ"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
