"use client";

import { memo } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from "lucide-react";
import { FaceRecognitionPaginationData } from "@/api/faceRecognition";

interface HistoryPaginationProps {
  pagination: FaceRecognitionPaginationData;
  onPageChange: (page: number) => void;
  onLimitChange: (limit: number) => void;
  isLoading?: boolean;
}

export const HistoryPagination = memo(({
  pagination,
  onPageChange,
  onLimitChange,
  isLoading = false,
}: HistoryPaginationProps) => {
  const { page, limit, totalData, totalPage } = pagination;

  // Calculate display range
  const startItem = totalData === 0 ? 0 : (page - 1) * limit + 1;
  const endItem = Math.min(page * limit, totalData);

  // Generate page numbers to display
  const getPageNumbers = () => {
    const pages: (number | string)[] = [];
    const maxVisiblePages = 5;
    
    if (totalPage <= maxVisiblePages) {
      // Show all pages if total pages is small
      for (let i = 1; i <= totalPage; i++) {
        pages.push(i);
      }
    } else {
      // Show pages with ellipsis
      if (page <= 3) {
        // Show first pages
        for (let i = 1; i <= 4; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPage);
      } else if (page >= totalPage - 2) {
        // Show last pages
        pages.push(1);
        pages.push('...');
        for (let i = totalPage - 3; i <= totalPage; i++) {
          pages.push(i);
        }
      } else {
        // Show middle pages
        pages.push(1);
        pages.push('...');
        for (let i = page - 1; i <= page + 1; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPage);
      }
    }
    
    return pages;
  };

  if (totalData === 0) {
    return null;
  }

  return (
    <Card>
      <CardContent className="py-4">
        <div className="flex items-center justify-between">
          {/* Results info */}
          <div className="text-sm text-gray-600">
            Hiển thị {startItem} - {endItem} trong tổng số {totalData} kết quả
          </div>

          {/* Pagination controls */}
          <div className="flex items-center gap-2">
            {/* Page size selector */}
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">Hiển thị:</span>
              <Select
                value={limit.toString()}
                onValueChange={(value) => onLimitChange(parseInt(value))}
                disabled={isLoading}
              >
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Page navigation */}
            <div className="flex items-center gap-1">
              {/* First page */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange(1)}
                disabled={page === 1 || isLoading}
                className="h-8 w-8 p-0"
                title="Trang đầu"
              >
                <ChevronsLeft className="h-4 w-4" />
              </Button>

              {/* Previous page */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange(page - 1)}
                disabled={page === 1 || isLoading}
                className="h-8 w-8 p-0"
                title="Trang trước"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>

              {/* Page numbers */}
              {getPageNumbers().map((pageNum, index) => (
                <Button
                  key={index}
                  variant={pageNum === page ? "default" : "outline"}
                  size="sm"
                  onClick={() => typeof pageNum === 'number' ? onPageChange(pageNum) : undefined}
                  disabled={typeof pageNum !== 'number' || isLoading}
                  className="h-8 min-w-8 px-2"
                >
                  {pageNum}
                </Button>
              ))}

              {/* Next page */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange(page + 1)}
                disabled={page === totalPage || isLoading}
                className="h-8 w-8 p-0"
                title="Trang sau"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>

              {/* Last page */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange(totalPage)}
                disabled={page === totalPage || isLoading}
                className="h-8 w-8 p-0"
                title="Trang cuối"
              >
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

HistoryPagination.displayName = 'HistoryPagination';
