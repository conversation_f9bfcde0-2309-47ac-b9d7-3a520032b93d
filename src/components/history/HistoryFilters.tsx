"use client";

import { memo, useCallback } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Search, 
  Filter, 
  Calendar,
  Users,
  Camera,
  MapPin
} from "lucide-react";
import { FaceRecognitionFilters } from "@/api/faceRecognition";
import { Class } from "@/api/class";
import { Camera as CameraType } from "@/api/camera";

interface HistoryFiltersProps {
  filters: FaceRecognitionFilters;
  onFiltersChange: (filters: FaceRecognitionFilters) => void;
  onSearch: () => void;
  onClearFilters: () => void;
  classes: Class[];
  cameras: CameraType[];
  isLoading?: boolean;
}

export const HistoryFilters = memo(({
  filters,
  onFiltersChange,
  onSearch,
  onClearFilters,
  classes,
  cameras,
  isLoading = false,
}: HistoryFiltersProps) => {
  
  const handleFilterChange = useCallback((key: keyof FaceRecognitionFilters, value: any) => {
    onFiltersChange({ ...filters, [key]: value });
  }, [filters, onFiltersChange]);

  // Get today's date for max date validation
  const today = new Date().toISOString().split('T')[0];

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          <Filter className="h-5 w-5" />
          Bộ lọc lịch sử nhận diện
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Date Range */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Từ ngày
            </Label>
            <Input
              type="date"
              value={filters.start_date || ""}
              onChange={(e) => handleFilterChange("start_date", e.target.value)}
              max={today}
            />
          </div>
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Đến ngày
            </Label>
            <Input
              type="date"
              value={filters.end_date || ""}
              onChange={(e) => handleFilterChange("end_date", e.target.value)}
              max={today}
              min={filters.start_date || undefined}
            />
          </div>
        </div>

        {/* Student Search and Class Filter */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              Tìm kiếm học sinh
            </Label>
            <Input
              placeholder="Tên, mã học sinh, số điện thoại..."
              value={filters.student_search || ""}
              onChange={(e) => handleFilterChange("student_search", e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Lớp học
            </Label>
            <Select 
              value={filters.class_id || ""} 
              onValueChange={(value) => handleFilterChange("class_id", value === "all" ? "" : value)}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Chọn lớp học" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả lớp học</SelectItem>
                {classes.map((classItem) => (
                  <SelectItem key={classItem.id} value={classItem.id.toString()}>
                    {classItem.name} ({classItem.code})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Camera and Location Filter */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <Camera className="h-4 w-4" />
              Camera
            </Label>
            <Select 
              value={filters.camera_id || ""} 
              onValueChange={(value) => handleFilterChange("camera_id", value === "all" ? "" : value)}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Chọn camera" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả camera</SelectItem>
                {cameras.map((camera) => (
                  <SelectItem key={camera.id} value={camera.id.toString()}>
                    {camera.name} - {camera.location}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Vị trí
            </Label>
            <Input
              placeholder="Tìm kiếm theo vị trí..."
              value={filters.location || ""}
              onChange={(e) => handleFilterChange("location", e.target.value)}
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-2 pt-2">
          <Button 
            onClick={onSearch} 
            disabled={isLoading}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Search className="h-4 w-4 mr-2" />
            Tìm kiếm
          </Button>
          <Button 
            onClick={onClearFilters} 
            variant="outline"
            disabled={isLoading}
          >
            Xóa bộ lọc
          </Button>
        </div>
      </CardContent>
    </Card>
  );
});

HistoryFilters.displayName = 'HistoryFilters';
