"use client";

import { memo, useCallback, useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  User,
  Camera,
  Clock,
  Users,
  MapPin,
  Eye,
  History
} from "lucide-react";
import { FaceRecognitionLog } from "@/api/faceRecognition";

interface HistoryTableProps {
  logs: FaceRecognitionLog[];
  isLoading: boolean;
  totalCount: number;
}

export const HistoryTable = memo(({
  logs,
  isLoading,
  totalCount,
}: HistoryTableProps) => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isImageDialogOpen, setIsImageDialogOpen] = useState(false);

  const formatDateTime = useCallback((dateTimeString: string) => {
    try {
      const date = new Date(dateTimeString);
      return {
        date: date.toLocaleDateString('vi-VN'),
        time: date.toLocaleTimeString('vi-VN', { 
          hour: '2-digit', 
          minute: '2-digit',
          second: '2-digit'
        })
      };
    } catch {
      return { date: 'N/A', time: 'N/A' };
    }
  }, []);

  const handleImageClick = useCallback((imageUrl: string) => {
    setSelectedImage(imageUrl);
    setIsImageDialogOpen(true);
  }, []);

  const closeImageDialog = useCallback(() => {
    setIsImageDialogOpen(false);
    setSelectedImage(null);
  }, []);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Lịch sử nhận diện khuôn mặt
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
              <p className="text-gray-500">Đang tải dữ liệu...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Lịch sử nhận diện khuôn mặt ({totalCount})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {logs.length === 0 ? (
            <div className="text-center py-12 text-gray-500">
              <History className="h-16 w-16 mx-auto mb-4 text-gray-300" />
              <p className="text-lg font-medium mb-2">Không có dữ liệu lịch sử</p>
              <p className="text-sm">Thử thay đổi bộ lọc hoặc khoảng thời gian tìm kiếm</p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[50px]">#</TableHead>
                    <TableHead>Học sinh</TableHead>
                    <TableHead>Lớp học</TableHead>
                    <TableHead>Camera</TableHead>
                    <TableHead>Thời gian nhận diện</TableHead>
                    <TableHead>Ảnh khuôn mặt</TableHead>
                    <TableHead>Độ tin cậy</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {logs.map((log, index) => {
                    const { date, time } = formatDateTime(log.recognition_time);

                    return (
                      <TableRow key={log.id} className="hover:bg-gray-50">
                        <TableCell className="font-medium">
                          {index + 1}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                              {log.student_avatar ? (
                                <img
                                  src={log.student_avatar}
                                  alt={log.student_name}
                                  className="w-8 h-8 rounded-full object-cover"
                                />
                              ) : (
                                <User className="h-4 w-4 text-blue-600" />
                              )}
                            </div>
                            <div className="min-w-0">
                              <div className="font-medium">{log.student_name}</div>
                              <div className="text-sm text-gray-500">
                                {log.student_code} • {log.student_phone}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Users className="h-4 w-4 text-gray-400" />
                            <div>
                              <div className="font-medium">{log.class_name}</div>
                              <div className="text-sm text-gray-500">{log.class_code}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              <Camera className="h-4 w-4 text-gray-400" />
                              <span className="font-medium">{log.camera_name}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <MapPin className="h-3 w-3 text-gray-400" />
                              <span className="text-sm text-gray-500">{log.camera_location}</span>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4 text-gray-400" />
                            <div>
                              <div className="font-medium">{date}</div>
                              <div className="text-sm text-gray-500">{time}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {log.face_image_url ? (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleImageClick(log.face_image_url!)}
                              className="h-8 w-8 p-0"
                              title="Xem ảnh khuôn mặt"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          ) : (
                            <span className="text-sm text-gray-400">Không có</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {log.confidence_score ? (
                            <div className="text-sm">
                              <span className={`font-medium ${
                                log.confidence_score >= 0.9 ? 'text-green-600' :
                                log.confidence_score >= 0.7 ? 'text-yellow-600' :
                                'text-red-600'
                              }`}>
                                {(log.confidence_score * 100).toFixed(1)}%
                              </span>
                            </div>
                          ) : (
                            <span className="text-sm text-gray-400">N/A</span>
                          )}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Image Preview Dialog */}
      <Dialog open={isImageDialogOpen} onOpenChange={closeImageDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Ảnh khuôn mặt nhận diện</DialogTitle>
          </DialogHeader>
          <div className="flex justify-center">
            {selectedImage && (
              <img
                src={selectedImage}
                alt="Face recognition"
                className="max-w-full max-h-96 object-contain rounded-lg"
              />
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
});

HistoryTable.displayName = 'HistoryTable';
