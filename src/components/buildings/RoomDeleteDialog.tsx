"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Loader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Door<PERSON>pen, Layers, Building2 } from "lucide-react";
import { Room } from "@/api/building";

interface RoomDeleteDialogProps {
  isOpen: boolean;
  onClose: () => void;
  room: Room | null;
  floorName: string;
  buildingName: string;
  onConfirm: () => Promise<void>;
  isDeleting: boolean;
}

export const RoomDeleteDialog = ({
  isOpen,
  onClose,
  room,
  floorName,
  buildingName,
  onConfirm,
  isDeleting,
}: RoomDeleteDialogProps) => {
  if (!room) return null;

  const handleConfirm = async () => {
    try {
      await onConfirm();
    } catch (error) {
      // Error handling is done in parent component
      console.error("Delete confirmation error:", error);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">Hoạt động</Badge>;
      case "inactive":
        return <Badge variant="secondary">Không hoạt động</Badge>;
      case "maintenance":
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">Bảo trì</Badge>;
      case "reserved":
        return <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">Đã đặt trước</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Xác nhận xóa phòng học
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Warning Message */}
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-700">
              <strong>Cảnh báo:</strong> Hành động này không thể hoàn tác!
            </AlertDescription>
          </Alert>

          {/* Room Information */}
          <div className="bg-gray-50 rounded-lg p-4 space-y-3">
            <div className="flex items-center gap-2">
              <DoorOpen className="h-4 w-4 text-gray-600" />
              <span className="font-medium text-gray-900">Thông tin phòng học</span>
            </div>
            
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <Building2 className="h-3 w-3 text-gray-500" />
                <span className="text-gray-600">Tòa nhà:</span>
                <span className="font-medium">{buildingName}</span>
              </div>
              <div className="flex items-center gap-2">
                <Layers className="h-3 w-3 text-gray-500" />
                <span className="text-gray-600">Tầng:</span>
                <span className="font-medium">{floorName}</span>
              </div>
              <div className="flex items-center gap-2">
                <DoorOpen className="h-3 w-3 text-gray-500" />
                <span className="text-gray-600">Tên phòng:</span>
                <span className="font-medium">{room.name}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-600">Trạng thái:</span>
                {getStatusBadge(room.status)}
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-600">ID:</span>
                <span className="font-mono text-xs bg-gray-200 px-1 rounded">{room.id}</span>
              </div>
            </div>
          </div>

          {/* Additional Warning for Active Rooms */}
          {room.status === "active" && (
            <Alert className="border-orange-200 bg-orange-50">
              <DoorOpen className="h-4 w-4 text-orange-600" />
              <AlertDescription className="text-orange-700">
                <strong>Lưu ý:</strong> Phòng học này đang ở trạng thái hoạt động. 
                Việc xóa có thể ảnh hưởng đến các hoạt động giảng dạy đang diễn ra.
              </AlertDescription>
            </Alert>
          )}

          {/* Confirmation Text */}
          <div className="text-sm text-gray-600">
            Bạn có chắc chắn muốn xóa phòng học{" "}
            <strong className="text-gray-900">`{room.name}`</strong>{" "}
            tại <strong>{floorName}</strong>, <strong>{buildingName}</strong>?
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isDeleting}
            >
              Hủy
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleConfirm}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Đang xóa...
                </>
              ) : (
                "Xác nhận xóa"
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
