"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2, DoorOpen } from "lucide-react";
import { Room } from "@/api/building";

interface RoomFormData {
  name: string;
  status: string;
  floor_id: number;
}

interface RoomFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  editingRoom: Room | null;
  floorId: number;
  floorName: string;
  buildingName: string;
  onSubmit: (formData: RoomFormData) => Promise<void>;
  isSubmitting: boolean;
}

export const RoomFormDialog = ({
  isOpen,
  onClose,
  editingRoom,
  floorId,
  floorName,
  buildingName,
  onSubmit,
  isSubmitting,
}: RoomFormDialogProps) => {
  const [formData, setFormData] = useState<RoomFormData>({
    name: "",
    status: "active",
    floor_id: floorId,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Reset form when dialog opens/closes or editing room changes
  useEffect(() => {
    if (isOpen) {
      if (editingRoom) {
        setFormData({
          name: editingRoom.name,
          status: editingRoom.status,
          floor_id: editingRoom.floor_id || floorId,
        });
      } else {
        setFormData({
          name: "",
          status: "active",
          floor_id: floorId,
        });
      }
      setErrors({});
    }
  }, [isOpen, editingRoom, floorId]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Tên phòng học là bắt buộc";
    } else if (formData.name.trim().length < 2) {
      newErrors.name = "Tên phòng học phải có ít nhất 2 ký tự";
    } else if (formData.name.trim().length > 100) {
      newErrors.name = "Tên phòng học không được vượt quá 100 ký tự";
    }

    if (!formData.status) {
      newErrors.status = "Trạng thái là bắt buộc";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit({
        ...formData,
        name: formData.name.trim(),
      });
    } catch (error) {
      // Error handling is done in parent component
      console.error("Form submission error:", error);
    }
  };

  const handleInputChange = (field: keyof RoomFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <DoorOpen className="h-5 w-5 text-blue-600" />
            {editingRoom ? "Chỉnh sửa phòng học" : "Thêm phòng học mới"}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Location Info */}
          <div className="bg-gray-50 rounded-lg p-3 space-y-1">
            <Label className="text-sm font-medium text-gray-700">Vị trí</Label>
            <p className="text-sm text-gray-900">{buildingName} → {floorName}</p>
          </div>

          {/* Room Name */}
          <div className="space-y-2">
            <Label htmlFor="name">
              Tên phòng học <span className="text-red-500">*</span>
            </Label>
            <Input
              id="name"
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              placeholder="Ví dụ: Phòng 101, Lab Tin học, Phòng họp..."
              className={errors.name ? "border-red-500" : ""}
              disabled={isSubmitting}
            />
            {errors.name && (
              <p className="text-sm text-red-600">{errors.name}</p>
            )}
          </div>

        
          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Hủy
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {editingRoom ? "Đang cập nhật..." : "Đang thêm..."}
                </>
              ) : (
                editingRoom ? "Cập nhật" : "Thêm phòng"
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
