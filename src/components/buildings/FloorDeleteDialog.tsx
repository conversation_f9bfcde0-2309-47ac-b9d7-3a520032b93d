"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, <PERSON><PERSON><PERSON><PERSON>gle, Layers, DoorOpen } from "lucide-react";
import { Floor } from "@/api/building";

interface FloorDeleteDialogProps {
  isOpen: boolean;
  onClose: () => void;
  floor: Floor | null;
  buildingName: string;
  onConfirm: () => Promise<void>;
  isDeleting: boolean;
}

export const FloorDeleteDialog = ({
  isOpen,
  onClose,
  floor,
  buildingName,
  onConfirm,
  isDeleting,
}: FloorDeleteDialogProps) => {
  if (!floor) return null;

  const roomCount = floor.rooms?.length || 0;
  const hasRooms = roomCount > 0;

  const handleConfirm = async () => {
    try {
      await onConfirm();
    } catch (error) {
      // Error handling is done in parent component
      console.error("Delete confirmation error:", error);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Xác nhận xóa tầng
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Warning Message */}
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-700">
              <strong>Cảnh báo:</strong> Hành động này không thể hoàn tác!
            </AlertDescription>
          </Alert>

          {/* Floor Information */}
          <div className="bg-gray-50 rounded-lg p-4 space-y-3">
            <div className="flex items-center gap-2">
              <Layers className="h-4 w-4 text-gray-600" />
              <span className="font-medium text-gray-900">Thông tin tầng</span>
            </div>
            
            <div className="space-y-2 text-sm">
              <div>
                <span className="text-gray-600">Tòa nhà:</span>
                <span className="ml-2 font-medium">{buildingName}</span>
              </div>
              <div>
                <span className="text-gray-600">Tên tầng:</span>
                <span className="ml-2 font-medium">{floor.name}</span>
              </div>
              <div>
                <span className="text-gray-600">ID:</span>
                <span className="ml-2 font-mono text-xs bg-gray-200 px-1 rounded">{floor.id}</span>
              </div>
            </div>
          </div>

          {/* Rooms Warning */}
          {hasRooms && (
            <Alert className="border-orange-200 bg-orange-50">
              <DoorOpen className="h-4 w-4 text-orange-600" />
              <AlertDescription className="text-orange-700">
                <strong>Lưu ý:</strong> Tầng này có <strong>{roomCount}</strong> phòng học. 
                Xóa tầng sẽ đồng thời xóa tất cả các phòng học thuộc tầng này.
              </AlertDescription>
            </Alert>
          )}

          {/* Confirmation Text */}
          <div className="text-sm text-gray-600">
            Bạn có chắc chắn muốn xóa tầng{" "}
            <strong className="text-gray-900">`{floor.name}`</strong>{" "}
            {hasRooms && (
              <>và <strong className="text-red-600">{roomCount}</strong> phòng học thuộc tầng này</>
            )}?
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isDeleting}
            >
              Hủy
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleConfirm}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Đang xóa...
                </>
              ) : (
                "Xác nhận xóa"
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
