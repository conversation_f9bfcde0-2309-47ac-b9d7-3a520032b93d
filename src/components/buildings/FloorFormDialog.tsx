"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, Layers } from "lucide-react";
import { Floor } from "@/api/building";

interface FloorFormData {
  name: string;
  building_id: number;
}

interface FloorFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  editingFloor: Floor | null;
  buildingId: number;
  buildingName: string;
  onSubmit: (formData: FloorFormData) => Promise<void>;
  isSubmitting: boolean;
}

export const FloorFormDialog = ({
  isOpen,
  onClose,
  editingFloor,
  buildingId,
  buildingName,
  onSubmit,
  isSubmitting,
}: FloorFormDialogProps) => {
  const [formData, setFormData] = useState<FloorFormData>({
    name: "",
    building_id: buildingId,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Reset form when dialog opens/closes or editing floor changes
  useEffect(() => {
    if (isOpen) {
      if (editingFloor) {
        setFormData({
          name: editingFloor.name,
          building_id: editingFloor.building_id,
        });
      } else {
        setFormData({
          name: "",
          building_id: buildingId,
        });
      }
      setErrors({});
    }
  }, [isOpen, editingFloor, buildingId]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Tên tầng là bắt buộc";
    } else if (formData.name.trim().length < 2) {
      newErrors.name = "Tên tầng phải có ít nhất 2 ký tự";
    } else if (formData.name.trim().length > 100) {
      newErrors.name = "Tên tầng không được vượt quá 100 ký tự";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit({
        ...formData,
        name: formData.name.trim(),
      });
    } catch (error) {
      // Error handling is done in parent component
      console.error("Form submission error:", error);
    }
  };

  const handleInputChange = (field: keyof FloorFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Layers className="h-5 w-5 text-blue-600" />
            {editingFloor ? "Chỉnh sửa tầng" : "Thêm tầng mới"}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Building Info */}
          <div className="bg-gray-50 rounded-lg p-3">
            <Label className="text-sm font-medium text-gray-700">Tòa nhà</Label>
            <p className="text-sm text-gray-900 mt-1">{buildingName}</p>
          </div>

          {/* Floor Name */}
          <div className="space-y-2">
            <Label htmlFor="name">
              Tên tầng <span className="text-red-500">*</span>
            </Label>
            <Input
              id="name"
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              placeholder="Ví dụ: Tầng 1, Tầng trệt, Tầng hầm..."
              className={errors.name ? "border-red-500" : ""}
              disabled={isSubmitting}
            />
            {errors.name && (
              <p className="text-sm text-red-600">{errors.name}</p>
            )}
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Hủy
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {editingFloor ? "Đang cập nhật..." : "Đang thêm..."}
                </>
              ) : (
                editingFloor ? "Cập nhật" : "Thêm tầng"
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
