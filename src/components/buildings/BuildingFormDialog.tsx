"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2 } from "lucide-react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Building, BuildingFormData } from "@/api/building";

interface BuildingFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  editingBuilding: Building | null;
  onSubmit: (formData: BuildingFormData) => Promise<void>;
  isSubmitting: boolean;
}

export const BuildingFormDialog = ({
  isOpen,
  onClose,
  editingBuilding,
  onSubmit,
  isSubmitting,
}: BuildingFormDialogProps) => {
  // Form state
  const [formData, setFormData] = useState<BuildingFormData>({
    name: "",
  });
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Initialize form data when dialog opens or editing building changes
  useEffect(() => {
    if (isOpen) {
      if (editingBuilding) {
        setFormData({
          name: editingBuilding.name,
        });
      } else {
        setFormData({
          name: "",
        });
      }
      setValidationErrors({});
    }
  }, [isOpen, editingBuilding]);

  // Form validation
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = "Tên tòa nhà là bắt buộc";
    } else if (formData.name.trim().length < 2) {
      errors.name = "Tên tòa nhà phải có ít nhất 2 ký tự";
    } else if (formData.name.trim().length > 100) {
      errors.name = "Tên tòa nhà không được vượt quá 100 ký tự";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Form handlers
  const handleInputChange = (field: keyof BuildingFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    await onSubmit(formData);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[400px]">
        <DialogHeader>
          <DialogTitle>
            {editingBuilding ? "Chỉnh sửa tòa nhà" : "Thêm tòa nhà mới"}
          </DialogTitle>
          <DialogDescription>
            {editingBuilding 
              ? "Cập nhật thông tin tòa nhà dưới đây." 
              : "Nhập thông tin tòa nhà mới dưới đây."
            }
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Tên tòa nhà *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              placeholder="Nhập tên tòa nhà"
              className={validationErrors.name ? "border-red-500" : ""}
              disabled={isSubmitting}
            />
            {validationErrors.name && (
              <p className="text-sm text-red-600">{validationErrors.name}</p>
            )}
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
              Hủy
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Đang lưu...
                </>
              ) : (
                editingBuilding ? "Cập nhật" : "Thêm mới"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
