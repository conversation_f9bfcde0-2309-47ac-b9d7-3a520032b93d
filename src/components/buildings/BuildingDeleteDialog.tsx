"use client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Building } from "@/api/building";

interface BuildingDeleteDialogProps {
  isOpen: boolean;
  onClose: () => void;
  building: Building | null;
  onConfirm: () => void;
  isDeleting?: boolean;
}

export const BuildingDeleteDialog = ({
  isOpen,
  onClose,
  building,
  onConfirm,
  isDeleting = false,
}: BuildingDeleteDialogProps) => {
  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Xác nhận xóa tòa nhà</AlertDialogTitle>
          <AlertDialogDescription>
            Bạn có chắc chắn muốn xóa tòa nhà `{building?.name}` không? 
            Hành động này không thể hoàn tác và sẽ ảnh hưởng đến tất cả các tầng và phòng học trong tòa nhà này.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={onClose} disabled={isDeleting}>
            Hủy
          </AlertDialogCancel>
          <AlertDialogAction 
            onClick={onConfirm}
            disabled={isDeleting}
            className="bg-red-600 hover:bg-red-700"
          >
            {isDeleting ? "Đang xóa..." : "Xóa"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
