"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Building2,
  Edit,
  Trash2,
  MoreHorizontal,
  ChevronDown,
  ChevronRight,
  Layers,
  DoorOpen,
  Plus
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Building, Floor, Room } from "@/api/building";

interface BuildingTableProps {
  buildings: Building[];
  buildingDetails: Record<number, { floors: Floor[] }>;
  onEdit: (building: Building) => void;
  onDelete: (building: Building) => void;
  onLoadBuildingDetails: (buildingId: number) => void;
  onAddFloor: (buildingId: number, buildingName: string) => void;
  onEditFloor: (floor: Floor, buildingName: string) => void;
  onDeleteFloor: (floor: Floor, buildingName: string) => void;
  onAddRoom: (floorId: number, floorName: string, buildingName: string) => void;
  onEditRoom: (room: Room, floorName: string, floorId: number, buildingName: string) => void;
  onDeleteRoom: (room: Room, floorName: string, buildingName: string) => void;
  isLoading?: boolean;
  totalCount: number;
}

export const BuildingTable = ({
  buildings,
  buildingDetails,
  onEdit,
  onDelete,
  onLoadBuildingDetails,
  onAddFloor,
  onEditFloor,
  onDeleteFloor,
  onAddRoom,
  onEditRoom,
  onDeleteRoom,
  isLoading = false,
  totalCount,
}: BuildingTableProps) => {
  const [expandedBuildings, setExpandedBuildings] = useState<Set<number>>(new Set());

  const toggleBuildingExpansion = (buildingId: number) => {
    const newExpanded = new Set(expandedBuildings);
    if (newExpanded.has(buildingId)) {
      newExpanded.delete(buildingId);
    } else {
      newExpanded.add(buildingId);
      // Load building details if not already loaded
      if (!buildingDetails[buildingId]) {
        onLoadBuildingDetails(buildingId);
      }
    }
    setExpandedBuildings(newExpanded);
  };

  const getTotalRoomsCount = (floors: Floor[]): number => {
    return floors.reduce((total, floor) => total + (floor.rooms?.length || 0), 0);
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center text-gray-500">
            Đang tải dữ liệu...
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">
          Danh sách tòa nhà ({totalCount})
        </CardTitle>
      </CardHeader>
      <CardContent>
        {buildings.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            Không có tòa nhà nào được tìm thấy
          </div>
        ) : (
          <div className="space-y-2">
            {buildings.map((building) => (
              <Collapsible
                key={building.id}
                open={expandedBuildings.has(building.id)}
                onOpenChange={() => toggleBuildingExpansion(building.id)}
              >
                <div className="border rounded-lg">
                  <div className="flex items-center justify-between p-4 hover:bg-gray-50">
                    <div className="flex items-center gap-4 flex-1">
                      <CollapsibleTrigger asChild>
                        <Button variant="ghost" size="sm" className="p-0 h-auto">
                          {expandedBuildings.has(building.id) ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                        </Button>
                      </CollapsibleTrigger>
                      
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                          <Building2 className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900">{building.name}</h3>
                        
                        </div>
                      </div>
                      
                      <div className="ml-auto flex items-center gap-2">
                        <Badge variant="outline">
                          {buildingDetails[building.id]?.floors?.length || 0} tầng
                        </Badge>
                        <Badge variant="secondary">
                          {buildingDetails[building.id] 
                            ? getTotalRoomsCount(buildingDetails[building.id].floors) 
                            : 0} phòng
                        </Badge>
                      </div>
                    </div>
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Mở menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => onEdit(building)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Chỉnh sửa
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => onDelete(building)}
                          className="text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Xóa
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  
                  <CollapsibleContent>
                    <div className="border-t bg-gray-50 p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-gray-900">Cấu trúc tòa nhà</h4>
                        <Button
                          size="sm"
                          onClick={() => onAddFloor(building.id, building.name)}
                          className="bg-blue-600 hover:bg-blue-700 h-8"
                        >
                          <Plus className="h-3 w-3 mr-1" />
                          Thêm tầng
                        </Button>
                      </div>
                      {buildingDetails[building.id] ? (
                        buildingDetails[building.id].floors.length > 0 ? (
                          <div className="space-y-3">
                            {buildingDetails[building.id].floors.map((floor) => (
                              <div key={floor.id} className="bg-white rounded-lg border p-3">
                                <div className="flex items-center justify-between mb-2">
                                  <div className="flex items-center gap-2">
                                    <Layers className="h-4 w-4 text-gray-600" />
                                    <span className="font-medium text-gray-900">{floor.name}</span>
                                    <Badge variant="outline" className="text-xs">
                                      {floor.rooms?.length || 0} phòng
                                    </Badge>
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() => onAddRoom(floor.id, floor.name, building.name)}
                                      className="h-7 px-2"
                                    >
                                      <Plus className="h-3 w-3 mr-1" />
                                      Thêm phòng
                                    </Button>
                                    <DropdownMenu>
                                      <DropdownMenuTrigger asChild>
                                        <Button variant="ghost" className="h-7 w-7 p-0">
                                          <MoreHorizontal className="h-3 w-3" />
                                        </Button>
                                      </DropdownMenuTrigger>
                                      <DropdownMenuContent align="end">
                                        <DropdownMenuItem onClick={() => onEditFloor(floor, building.name)}>
                                          <Edit className="mr-2 h-3 w-3" />
                                          Sửa tầng
                                        </DropdownMenuItem>
                                        <DropdownMenuItem
                                          onClick={() => onDeleteFloor(floor, building.name)}
                                          className="text-red-600"
                                        >
                                          <Trash2 className="mr-2 h-3 w-3" />
                                          Xóa tầng
                                        </DropdownMenuItem>
                                      </DropdownMenuContent>
                                    </DropdownMenu>
                                  </div>
                                </div>
                                {floor.rooms && floor.rooms.length > 0 && (
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 ml-6">
                                    {floor.rooms.map((room) => (
                                      <div key={room.id} className="flex items-center justify-between p-2 bg-gray-50 rounded border">
                                        <div className="flex items-center gap-2 flex-1 min-w-0">
                                          <DoorOpen className="h-3 w-3 text-gray-500 flex-shrink-0" />
                                          <span className="text-sm text-gray-700 truncate">{room.name}</span>
                                          <Badge
                                            variant={room.status === "active" ? "default" : "secondary"}
                                            className="text-xs flex-shrink-0"
                                          >
                                            {room.status === "active" ? "Hoạt động" : "Không hoạt động"}
                                          </Badge>
                                        </div>
                                        <DropdownMenu>
                                          <DropdownMenuTrigger asChild>
                                            <Button variant="ghost" className="h-6 w-6 p-0 flex-shrink-0">
                                              <MoreHorizontal className="h-3 w-3" />
                                            </Button>
                                          </DropdownMenuTrigger>
                                          <DropdownMenuContent align="end">
                                            <DropdownMenuItem onClick={() => onEditRoom(room, floor.name, floor.id, building.name,)}>
                                              <Edit className="mr-2 h-3 w-3" />
                                              Sửa phòng
                                            </DropdownMenuItem>
                                            <DropdownMenuItem
                                              onClick={() => onDeleteRoom(room, floor.name, building.name)}
                                              className="text-red-600"
                                            >
                                              <Trash2 className="mr-2 h-3 w-3" />
                                              Xóa phòng
                                            </DropdownMenuItem>
                                          </DropdownMenuContent>
                                        </DropdownMenu>
                                      </div>
                                    ))}
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="text-center py-6">
                            <p className="text-gray-500 text-sm mb-3">Chưa có tầng nào trong tòa nhà này</p>
                            <Button
                              size="sm"
                              onClick={() => onAddFloor(building.id, building.name)}
                              className="bg-blue-600 hover:bg-blue-700"
                            >
                              <Plus className="h-3 w-3 mr-1" />
                              Thêm tầng đầu tiên
                            </Button>
                          </div>
                        )
                      ) : (
                        <div className="text-center py-4">
                          <div className="text-gray-500 text-sm">Đang tải cấu trúc tòa nhà...</div>
                        </div>
                      )}
                    </div>
                  </CollapsibleContent>
                </div>
              </Collapsible>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
