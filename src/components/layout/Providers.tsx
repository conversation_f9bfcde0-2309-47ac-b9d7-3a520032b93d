"use client";

import {
  QueryClient,
  QueryClientProvider,
  QueryClientConfig,
} from "@tanstack/react-query";
import { Provider as ReduxProvider } from "react-redux";
import { store } from "@/redux/store"; // Đường dẫn tới store của bạn
import { useEffect, useState } from "react";
import {
  clearUserAndToken,
  loadUserFromStorage,
  setUserAndToken,
} from "@/redux/features/user/userSlice";
import { getSchoolInfoAPI } from "@/api/auth";
import { getAuthToken } from "@/utils/getAuthToken";
import Cookies from "universal-cookie";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

// Cấu hình mặc định cho react-query (tùy chọn)
const queryClientOptions: QueryClientConfig = {
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 phút
      refetchOnWindowFocus: false, // Tắt refetch khi focus lại cửa sổ
    },
  },
};

// Khởi tạo queryClient một lần bên ngoài component để tránh tạo lại mỗi lần render
const queryClient = new QueryClient(queryClientOptions);

// Component Dialog thông báo phiên đăng nhập hết hạn
function SessionExpiredDialog({
  isOpen,
  onClose,
  onLoginRedirect,
}: {
  isOpen: boolean;
  onClose: () => void;
  onLoginRedirect: () => void;
}) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Phiên đăng nhập đã hết hạn</DialogTitle>
          <DialogDescription>
            Phiên đăng nhập của bạn đã hết hạn, vui lòng đăng nhập lại để tiếp
            tục sử dụng hệ thống.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button onClick={onLoginRedirect} className="w-full">
            Đăng nhập lại
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export function Providers({ children }: { children: React.ReactNode }) {
  const [showSessionExpiredDialog, setShowSessionExpiredDialog] =
    useState(false);
  const [hasShownDialog, setHasShownDialog] = useState(false); // Để tránh spam dialog

  // Function để xử lý chuyển hướng đến trang login
  const handleLoginRedirect = () => {
    if (typeof window !== "undefined") {
      const cookies = new Cookies();
      cookies.remove("accessTokenSchool", { path: "/" });
      store.dispatch(clearUserAndToken());
      setShowSessionExpiredDialog(false);
      setHasShownDialog(false);
      window.location.href = "/"; // Chuyển hướng về trang login
    }
  };

  // Function để đóng dialog
  const handleCloseDialog = () => {
    setShowSessionExpiredDialog(false);
  };

  useEffect(() => {
    const loadUser = async () => {
      if (typeof window !== "undefined") {
        const token = getAuthToken();
        const cookies = new Cookies(); // Khởi tạo Cookies
        if (token) {
          try {
            const response = await getSchoolInfoAPI(token);

            if (response.code === 1 && response.data) {
              store.dispatch(
                setUserAndToken({
                  user: response.data,
                  accessToken: response.data.token,
                })
              );
              // Reset dialog state nếu đăng nhập thành công
              setHasShownDialog(false);
            } else {
              console.error(
                "Failed to fetch user info with token:",
                response.mess
              );
              // Phiên đăng nhập không hợp lệ - hiển thị dialog
              if (!hasShownDialog) {
                setShowSessionExpiredDialog(true);
                setHasShownDialog(true);
              }
            }
          } catch (error) {
            console.error("Error fetching user info:", error);
            // Lỗi khi gọi API - hiển thị dialog
            if (!hasShownDialog) {
              setShowSessionExpiredDialog(true);
              setHasShownDialog(true);
            }
          }
        }
        // Không cần else vì nếu không có token thì không làm gì cả
        // Hoặc bạn có thể dispatch một action để clear user state nếu cần
      }
    };

    loadUser();
  }, []);

  return (
    <ReduxProvider store={store}>
      <QueryClientProvider client={queryClient}>
        {children}

        {/* Dialog thông báo phiên đăng nhập hết hạn */}
        <SessionExpiredDialog
          isOpen={showSessionExpiredDialog}
          onClose={handleCloseDialog}
          onLoginRedirect={handleLoginRedirect}
        />
      </QueryClientProvider>
    </ReduxProvider>
  );
}
