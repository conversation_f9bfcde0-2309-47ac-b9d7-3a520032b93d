import {
  Camera,
  Users,
  User,
  Calendar,
  BookOpen,
  Settings,
  History,
  Search,
  BarChart3,
  School,
  LogOut,
  Building,
  Building2,
  Layers,
  DoorOpen,
  ChevronDown,
  ChevronRight,
  Video,
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarHeader,
  SidebarFooter,
  useSidebar,
} from "@/components/ui/sidebar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useRouter, usePathname } from "next/navigation";
import Cookies from "universal-cookie";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import API from "@/lib/axios";
import { toast } from "sonner";
import { clearUserAndToken } from "@/redux/features/user/userSlice";
import { useState } from "react";

// Types for menu items
interface SubMenuItem {
  id: string;
  label: string;
  icon: any;
  path: string;
}

interface MenuItem {
  id: string;
  label: string;
  icon: any;
  path?: string;
  hasSubMenu?: boolean;
  subItems?: SubMenuItem[];
}

export const AppSidebar = () => {
  const user_info = useSelector((state: RootState) => state.user.currentUser);
  const dispatch = useDispatch();

  const router = useRouter();
  const pathname = usePathname();
  const { state } = useSidebar();
  const isCollapsed = state === "collapsed";
  const activeTab = (pathname ?? "").split("/").pop() ?? "/";

  // State for expandable sub-menus
  const [expandedMenus, setExpandedMenus] = useState<Set<string>>(new Set());

  const onTabChange = (path: string) => {
    router.push(path);
  };

  const toggleSubMenu = (menuId: string) => {
    const newExpanded = new Set(expandedMenus);
    if (newExpanded.has(menuId)) {
      newExpanded.delete(menuId);
    } else {
      newExpanded.add(menuId);
    }
    setExpandedMenus(newExpanded);
  };

  const menuItems: MenuItem[] = [
    { id: "dashboard", label: "Tổng quan", icon: BarChart3, path: "/school" },
    { id: "camera", label: "Quản lý Camera AI", icon: Camera, path: "/camera" },
    { id: "teachers", label: "Giáo viên", icon: User, path: "/teachers" },
    { id: "students", label: "Học sinh", icon: Users, path: "/students" },
    { id: "classes", label: "Lớp học", icon: School, path: "/classes" },
    {
      id: "schoolyears",
      label: "Năm học",
      icon: Calendar,
      path: "/semester",
    },
    {
      id: "subjects",
      label: "Tổ chuyên môn",
      icon: BookOpen,
      path: "/subjects",
    },
    {
      id: "buildings",
      label: "Quản lý sơ đồ trường",
      icon: Building2,
      path: "/buildings",
    },
    { id: "attendance", label: "Điểm danh", icon: Users, path: "/attendance" },
    {
      id: "history",
      label: "Lịch sử xuất hiện",
      icon: History,
      path: "/history",
    },
    {
      id: "search",
      label: "Tìm kiếm thời gian thực",
      icon: Search,
      path: "/search",
    },
    { id: "stream-test", label: "Stream Test", icon: Video, path: "/stream-test" },
    { id: "settings", label: "Cài đặt", icon: Settings, path: "/settings" },
  ];

  const handleLogout = async () => {
    const cookies = new Cookies();
    try {
      const token = user_info?.token || ""; // Lấy token từ user info trong redux store

      // Gọi API đăng xuất nếu có token
      if (token) {
        const response = await API.post("/logoutAdminAPI", {
          token: token,
        });
        const data = response.data;

        // Xử lý phản hồi từ API
        switch (data.code) {
          case 1:
            toast.success(data.mess || "Đăng xuất thành công");
            break;
          case 3:
            toast.error("Tài khoản không tồn tại hoặc sai token");
            break;
          case 0:
            toast.error("Dữ liệu gửi kiểu POST");
            break;
          default:
            toast.error(data.mess || "Có lỗi xảy ra khi đăng xuất từ server");
            break;
        }
      } else {
        // Nếu không có token, vẫn coi như đăng xuất thành công ở client
        toast.success("Đăng xuất thành công (không có token phía client)");
      }
    } catch (error) {
      console.error("Lỗi khi gọi API đăng xuất:", error);
      toast.error("Có lỗi xảy ra khi kết nối máy chủ để đăng xuất.");
    } finally {
      // Luôn thực hiện các hành động dọn dẹp này
      cookies.remove("accessTokenSchool", { path: "/" });
      dispatch(clearUserAndToken());
      router.push("/"); // Chuyển hướng về trang đăng nhập (đường dẫn gốc)
    }
  };

  return (
    <Sidebar collapsible="icon" className="border-r border-gray-200">
      <SidebarHeader className="border-b border-gray-200 p-4">
        <div className="flex items-center gap-3">
          <img
            src={user_info?.logo}
            alt="Logo Phoenix"
            className="h-8 w-auto"
          />
          {!isCollapsed && (
            <h1 className="text-lg font-bold text-gray-800">
              Quản lý Trường học
            </h1>
          )}
        </div>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel className={cn(isCollapsed && "sr-only")}>
            Điều hướng chính
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {menuItems.map((item) => {
                const Icon = item.icon;
                const isActive = activeTab === item.id;
                const hasSubMenu = item.hasSubMenu;
                const isExpanded = expandedMenus.has(item.id);

                return (
                  <SidebarMenuItem key={item.id}>
                    {hasSubMenu ? (
                      <>
                        {isCollapsed ? (
                          // Collapsed state: Show popover with sub-items
                          <Popover>
                            <PopoverTrigger asChild>
                              <SidebarMenuButton
                                tooltip={item.label}
                                className={cn(
                                  "w-full justify-center cursor-pointer"
                                )}
                              >
                                <Icon className="h-5 w-5 flex-shrink-0" />
                              </SidebarMenuButton>
                            </PopoverTrigger>
                            <PopoverContent
                              side="right"
                              align="start"
                              className="w-48 p-2"
                              sideOffset={8}
                            >
                              <div className="space-y-1">
                                <div className="px-2 py-1 text-sm font-medium text-gray-900 border-b border-gray-200 mb-2">
                                  {item.label}
                                </div>
                                {item.subItems?.map((subItem) => {
                                  const SubIcon = subItem.icon;
                                  const isSubActive = activeTab === subItem.id;

                                  return (
                                    <button
                                      key={subItem.id}
                                      onClick={() => onTabChange(subItem.path)}
                                      className={cn(
                                        "w-full flex items-center gap-2 px-2 py-1.5 text-sm rounded-md cursor-pointer transition-colors",
                                        isSubActive
                                          ? "bg-blue-600 text-white"
                                          : "hover:bg-gray-100 text-gray-700"
                                      )}
                                    >
                                      <SubIcon className="h-4 w-4 flex-shrink-0" />
                                      <span>{subItem.label}</span>
                                    </button>
                                  );
                                })}
                              </div>
                            </PopoverContent>
                          </Popover>
                        ) : (
                          // Expanded state: Show normal expandable menu
                          <>
                            <SidebarMenuButton
                              onClick={() => toggleSubMenu(item.id)}
                              tooltip={item.label}
                              className={cn(
                                "w-full justify-start cursor-pointer"
                              )}
                            >
                              <Icon className="h-5 w-5 flex-shrink-0" />
                              <span className="flex-1">{item.label}</span>
                              {isExpanded ? (
                                <ChevronDown className="h-4 w-4" />
                              ) : (
                                <ChevronRight className="h-4 w-4" />
                              )}
                            </SidebarMenuButton>
                            {isExpanded && item.subItems && (
                              <div className="ml-6 mt-1 space-y-1">
                                {item.subItems.map((subItem) => {
                                  const SubIcon = subItem.icon;
                                  const isSubActive = activeTab === subItem.id;

                                  return (
                                    <SidebarMenuButton
                                      key={subItem.id}
                                      onClick={() => onTabChange(subItem.path)}
                                      isActive={isSubActive}
                                      tooltip={subItem.label}
                                      className={cn(
                                        "w-full justify-start cursor-pointer text-sm",
                                        isSubActive && "bg-blue-600 text-white hover:bg-blue-700"
                                      )}
                                    >
                                      <SubIcon className="h-4 w-4 flex-shrink-0" />
                                      <span>{subItem.label}</span>
                                    </SidebarMenuButton>
                                  );
                                })}
                              </div>
                            )}
                          </>
                        )}
                      </>
                    ) : (
                      <SidebarMenuButton
                        onClick={() => onTabChange(item.path!)}
                        isActive={isActive}
                        tooltip={item.label}
                        className={cn(
                          "w-full justify-start cursor-pointer",
                          isActive && "bg-blue-600 text-white hover:bg-blue-700",
                          isCollapsed && "justify-center"
                        )}
                      >
                        <Icon className="h-5 w-5 flex-shrink-0" />
                        {!isCollapsed && <span>{item.label}</span>}
                      </SidebarMenuButton>
                    )}
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="border-t border-gray-200 p-4">
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              tooltip="Đăng xuất"
              className={cn(
                "w-full cursor-pointer justify-start text-gray-600 hover:bg-red-50 hover:text-red-600",
                isCollapsed && "justify-center"
              )}
              onClick={handleLogout}
            >
              <LogOut className="h-5 w-5 flex-shrink-0" />
              {!isCollapsed && <span>Đăng xuất</span>}
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
};
