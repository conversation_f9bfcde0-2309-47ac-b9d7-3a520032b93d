"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, School, AlertTriangle, CheckCircle, Upload, X } from "lucide-react";
import { getSchoolInfoAPI, SchoolInfo, UpdateSchoolData, updateSchoolInfoAPI } from "@/api/school";
import { getAuthToken } from "@/utils/getAuthToken";
import { cn } from "@/lib/utils";
import { validateLogoFile } from "@/utils/validators";
import { Alert, AlertDescription } from "../ui/alert";

interface SchoolInfoFormProps {
  className?: string;
}

interface SchoolFormData {
  name: string;
  code: string;
  logo: string;
  address: string;
}

export const SchoolInfoForm = ({ className }: SchoolInfoFormProps) => {
  const [schoolInfo, setSchoolInfo] = useState<SchoolInfo | null>(null);
  const [formData, setFormData] = useState<SchoolFormData>({
    name: "",
    code: "",
    logo: "",
    address: "",
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // File upload state
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);

  // Fetch school information on component mount
  useEffect(() => {
    fetchSchoolInfo();
  }, []);

  const fetchSchoolInfo = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await getSchoolInfoAPI(token);
      
      if (response.code === 1 && response.data) {
        setSchoolInfo(response.data);
        setFormData({
          name: response.data.name || "",
          code: response.data.code || "",
          logo: response.data.logo || "",
          address: response.data.address || "",
        });

        // Set logo preview if logo URL exists
        if (response.data.logo) {
          setLogoPreview(response.data.logo);
        }
      } else {
        throw new Error(response.mess || "Không thể lấy thông tin trường học");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsLoading(false);
    }
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = "Tên trường là bắt buộc";
    }

    if (!formData.code.trim()) {
      errors.code = "Mã trường là bắt buộc";
    }

    if (!formData.address.trim()) {
      errors.address = "Địa chỉ là bắt buộc";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (field: keyof SchoolFormData & string, value: string) => {
    setFormData((prev: SchoolFormData) => ({ ...prev, [field]: value }));

    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }

    // Clear success message when user starts editing
    if (success) {
      setSuccess(null);
    }
  };

  const handleLogoFileChange = (file: File | null) => {
    setLogoFile(file);

    if (file) {
      // Validate file
      const validationError = validateLogoFile(file);
      if (validationError) {
        setValidationErrors(prev => ({ ...prev, logo: validationError }));
        setLogoPreview(null);
        return;
      }

      // Clear validation error
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.logo;
        return newErrors;
      });

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setLogoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);

      // Clear the URL field since we're using file upload
      setFormData(prev => ({ ...prev, logo: "" }));
    } else {
      setLogoPreview(null);
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.logo;
        return newErrors;
      });
    }

    // Clear success message when user starts editing
    if (success) {
      setSuccess(null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setIsUpdating(true);
      setError(null);
      setSuccess(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const updateData: UpdateSchoolData = {
        name: formData.name,
        code: formData.code,
        logo: logoFile || formData.logo, // Use file if uploaded, otherwise use URL
        address: formData.address,
      };

      const response = await updateSchoolInfoAPI(token, updateData);
      
      if (response.code === 0 && response.data) {
        setSchoolInfo(response.data);
        setSuccess("Cập nhật thông tin trường học thành công!");
        
        // Refresh the form data with updated information
        setFormData({
          name: response.data.name || "",
          code: response.data.code || "",
          logo: response.data.logo || "",
          address: response.data.address || "",
        });

        // Clear file upload state and update preview
        setLogoFile(null);
        if (response.data.logo) {
          setLogoPreview(response.data.logo);
        } else {
          setLogoPreview(null);
        }
      } else {
        throw new Error(response.mess || "Không thể cập nhật thông tin trường học");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsUpdating(false);
    }
  };



  if (isLoading) {
    return (
      <div className={cn("space-y-6", className)}>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <School className="h-5 w-5" />
              Thông tin trường học
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <span className="ml-2 text-gray-600">Đang tải thông tin...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* School Information Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <School className="h-5 w-5" />
            Thông tin trường học
          </CardTitle>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert className="mb-4 border-red-200 bg-red-50">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-700">{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert className="mb-4 border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-700">{success}</AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
             <div className="space-y-2">
                <Label htmlFor="name">Tên trường *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="Nhập tên trường học"
                  className={validationErrors.name ? "border-red-500" : ""}
                />
                {validationErrors.name && (
                  <p className="text-sm text-red-600">{validationErrors.name}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="code">Mã trường *</Label>
                <Input
                  id="code"
                  value={formData.code}
                  onChange={(e) => handleInputChange("code", e.target.value)}
                  placeholder="Nhập mã trường học"
                  className={validationErrors.code ? "border-red-500" : ""}
                />
                {validationErrors.code && (
                  <p className="text-sm text-red-600">{validationErrors.code}</p>
                )}
              </div>

            <div className="space-y-2">
              <Label htmlFor="address">Địa chỉ *</Label>
              <Input
                id="address"
                value={formData.address}
                onChange={(e) => handleInputChange("address", e.target.value)}
                placeholder="Nhập địa chỉ trường học"
                className={validationErrors.address ? "border-red-500" : ""}
              />
              {validationErrors.address && (
                <p className="text-sm text-red-600">{validationErrors.address}</p>
              )}
            </div>

            {/* Logo Upload Section */}
            <div className="space-y-2">
              <Label>Logo trường</Label>
              <div className="flex items-start gap-4">
                {/* Logo Preview */}
                <div className="w-20 h-20 rounded-lg bg-gray-100 flex items-center justify-center overflow-hidden border-2 border-dashed border-gray-300">
                  {logoPreview ? (
                    <img
                      src={logoPreview}
                      alt="Logo preview"
                      className="w-full h-full object-contain"
                    />
                  ) : (
                    <School className="h-8 w-8 text-gray-400" />
                  )}
                </div>

                {/* Upload Controls */}
                <div className="flex-1 space-y-2">
                  <div className="flex gap-2">
                    <Input
                      type="file"
                      accept="image/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        handleLogoFileChange(file || null);
                      }}
                      className="hidden"
                      id="logo-upload"
                    />
                    <Label htmlFor="logo-upload" className="cursor-pointer">
                      <Button type="button" variant="outline" asChild>
                        <span>
                          <Upload className="h-4 w-4 mr-2" />
                          Chọn ảnh
                        </span>
                      </Button>
                    </Label>

                    {(logoFile || logoPreview) && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setLogoFile(null);
                          setLogoPreview(null);
                          setFormData(prev => ({ ...prev, logo: "" }));
                          // Clear file input
                          const fileInput = document.getElementById('logo-upload') as HTMLInputElement;
                          if (fileInput) fileInput.value = '';
                        }}
                      >
                        <X className="h-4 w-4 mr-1" />
                        Xóa
                      </Button>
                    )}
                  </div>

                  <p className="text-sm text-gray-500">
                    Chấp nhận: JPG, PNG, GIF. Tối đa 2MB.
                  </p>

                  {validationErrors.logo && (
                    <p className="text-sm text-red-600">{validationErrors.logo}</p>
                  )}
                </div>
              </div>
            </div>

            <Button 
              type="submit" 
              className="w-full" 
              disabled={isUpdating}
            >
              {isUpdating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Đang cập nhật...
                </>
              ) : (
                "Lưu thay đổi"
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};
