"use client";

import { memo } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>alogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Trash2, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>r, 
  Loader2, 
  Clock, 
  School, 
  MapPin,
  Calendar,
  Camera,
  FileText
} from "lucide-react";
import { AttendanceRecord } from "@/api/attendance";
import { Badge } from "@/components/ui/badge";

interface DeleteAttendanceDialogProps {
  isOpen: boolean;
  onClose: () => void;
  record: AttendanceRecord | null;
  onConfirm: (record: AttendanceRecord) => void;
  isDeleting: boolean;
}

export const DeleteAttendanceDialog = memo(({
  isOpen,
  onClose,
  record,
  onConfirm,
  isDeleting,
}: DeleteAttendanceDialogProps) => {
  if (!record) return null;

  const handleConfirm = () => {
    onConfirm(record);
  };

  const formatDateTime = (dateTimeString: string) => {
    try {
      const date = new Date(dateTimeString);
      return {
        date: date.toLocaleDateString('vi-VN'),
        time: date.toLocaleTimeString('vi-VN', { 
          hour: '2-digit', 
          minute: '2-digit' 
        })
      };
    } catch {
      return { date: 'N/A', time: 'N/A' };
    }
  };

  const { date, time } = formatDateTime(record.attendance_time);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5 text-red-600" />
            Xóa bản ghi điểm danh
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Warning */}
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-700">
              <strong>Cảnh báo:</strong> Bạn có chắc chắn muốn xóa bản ghi điểm danh này? 
              Thao tác này không thể hoàn tác.
            </AlertDescription>
          </Alert>

          {/* Record Details */}
          <div className="bg-gray-50 rounded-lg p-6 space-y-4">
            <h3 className="font-medium text-lg mb-4">Thông tin bản ghi điểm danh</h3>
            
            {/* Student Info */}
            <div className="flex items-center gap-3 pb-3 border-b">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <User className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h4 className="font-medium text-lg">{record.student_name}</h4>
                <p className="text-sm text-gray-600">
                  Mã SV: {record.student_code} • SĐT: {record.student_phone}
                </p>
              </div>
              <div className="ml-auto">
                <Badge className={
                  record.attendance_type === "auto" 
                    ? "bg-green-100 text-green-800 hover:bg-green-100"
                    : "bg-blue-100 text-blue-800 hover:bg-blue-100"
                }>
                  {record.attendance_type === "auto" ? (
                    <>
                      <Camera className="h-3 w-3 mr-1" />
                      Tự động
                    </>
                  ) : (
                    <>
                      <User className="h-3 w-3 mr-1" />
                      Thủ công
                    </>
                  )}
                </Badge>
              </div>
            </div>

            {/* Attendance Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm">
                  <School className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">Lớp:</span>
                  <span className="font-medium">
                    {record.class_name} ({record.class_code})
                  </span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Clock className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">Ca học:</span>
                  <span>{record.schedule_name}</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">Thời gian:</span>
                  <span>{date} lúc {time}</span>
                </div>
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm">
                  <MapPin className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">Vị trí:</span>
                  <span>{record.location || 'Không xác định'}</span>
                </div>
                {record.camera_name && (
                  <div className="flex items-center gap-2 text-sm">
                    <Camera className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-600">Camera:</span>
                    <span>{record.camera_name}</span>
                  </div>
                )}
                {record.note && (
                  <div className="flex items-start gap-2 text-sm">
                    <FileText className="h-4 w-4 text-gray-400 mt-0.5" />
                    <span className="text-gray-600">Ghi chú:</span>
                    <span className="flex-1">{record.note}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Confirmation Text */}
          <div className="text-sm text-gray-700 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <p>
              <strong>Lưu ý:</strong> Việc xóa bản ghi điểm danh của học sinh{" "}
              <strong>{record.student_name}</strong> trong lớp{" "}
              <strong>{record.class_name}</strong> vào ngày{" "}
              <strong>{date}</strong> sẽ không thể hoàn tác.
            </p>
            <p className="mt-2">
              Hãy chắc chắn rằng đây là thao tác bạn muốn thực hiện.
            </p>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button 
              variant="outline" 
              onClick={onClose}
              disabled={isDeleting}
            >
              Hủy
            </Button>
            <Button 
              variant="destructive"
              onClick={handleConfirm}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Đang xóa...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Xóa bản ghi
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
});

DeleteAttendanceDialog.displayName = 'DeleteAttendanceDialog';
