"use client";

import { memo, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from "lucide-react";
import { AttendanceSchedulePaginationData } from "@/api/attendance";

interface SchedulePaginationProps {
  pagination: AttendanceSchedulePaginationData;
  onPageChange: (page: number) => void;
  onLimitChange: (limit: number) => void;
}

export const SchedulePagination = memo(({
  pagination,
  onPageChange,
  onLimitChange,
}: SchedulePaginationProps) => {
  const { page, limit, totalData, totalPage } = pagination;

  // Memoize page numbers calculation to prevent unnecessary recalculations
  const pageNumbers = useMemo(() => {
    const pages = [];
    const maxPagesToShow = 5;
    
    if (totalPage <= maxPagesToShow) {
      // Show all pages if total pages is small
      for (let i = 1; i <= totalPage; i++) {
        pages.push(i);
      }
    } else {
      // Show pages around current page
      const startPage = Math.max(1, page - 2);
      const endPage = Math.min(totalPage, page + 2);
      
      if (startPage > 1) {
        pages.push(1);
        if (startPage > 2) {
          pages.push('...');
        }
      }
      
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
      
      if (endPage < totalPage) {
        if (endPage < totalPage - 1) {
          pages.push('...');
        }
        pages.push(totalPage);
      }
    }
    
    return pages;
  }, [page, totalPage]);

  // Only show pagination if there are items
  if (totalData === 0) {
    return null;
  }

  const startItem = (page - 1) * limit + 1;
  const endItem = Math.min(page * limit, totalData);

  return (
    <Card>
      <CardContent className="py-4">
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
          {/* Page info and limit selector */}
          <div className="flex items-center gap-4">
            <div className="text-sm text-gray-600">
              Hiển thị {startItem} - {endItem} trong tổng số {totalData} lịch điểm danh
            </div>
            
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">Hiển thị:</span>
              <Select 
                value={limit.toString()} 
                onValueChange={(value) => onLimitChange(parseInt(value))}
              >
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Pagination controls */}
          <div className="flex items-center gap-1">
            {/* First page */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(1)}
              disabled={page === 1}
              className="h-8 w-8 p-0"
              title="Trang đầu"
            >
              <ChevronsLeft className="h-4 w-4" />
            </Button>

            {/* Previous page */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(page - 1)}
              disabled={page === 1}
              className="h-8 w-8 p-0"
              title="Trang trước"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            {/* Page numbers */}
            {pageNumbers.map((pageNum, index) => (
              <Button
                key={index}
                variant={pageNum === page ? "default" : "outline"}
                size="sm"
                onClick={() => typeof pageNum === 'number' ? onPageChange(pageNum) : undefined}
                disabled={typeof pageNum !== 'number'}
                className="h-8 w-8 p-0"
              >
                {pageNum}
              </Button>
            ))}

            {/* Next page */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(page + 1)}
              disabled={page === totalPage}
              className="h-8 w-8 p-0"
              title="Trang sau"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>

            {/* Last page */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(totalPage)}
              disabled={page === totalPage}
              className="h-8 w-8 p-0"
              title="Trang cuối"
            >
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

SchedulePagination.displayName = 'SchedulePagination';
