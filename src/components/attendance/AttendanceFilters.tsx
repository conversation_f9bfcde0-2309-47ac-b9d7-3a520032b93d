"use client";

import { memo, useCallback } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Search, 
  Filter, 
  X, 
  Calendar,
  Users,
  Clock,
  Camera,
  MapPin,
  UserCheck
} from "lucide-react";
import { AttendanceFilters as FilterType, AttendanceSchedule, Camera as CameraType } from "@/api/attendance";
import { Class } from "@/api/class";

interface AttendanceFiltersProps {
  filters: FilterType;
  onFiltersChange: (filters: FilterType) => void;
  onSearch: () => void;
  onClearFilters: () => void;
  classes: Class[];
  schedules: AttendanceSchedule[];
  cameras: CameraType[];
  isLoading?: boolean;
  isLoadingSchedules?: boolean;
}

export const AttendanceFilters = memo(({
  filters,
  onFiltersChange,
  onSearch,
  onClearFilters,
  classes,
  schedules,
  cameras,
  isLoading = false,
  isLoadingSchedules = false,
}: AttendanceFiltersProps) => {
  
  const handleFilterChange = useCallback((key: keyof FilterType, value: any) => {
    onFiltersChange({ ...filters, [key]: value });
  }, [filters, onFiltersChange]);

  const hasActiveFilters = Object.values(filters).some(value => 
    value !== undefined && value !== "" && value !== null
  );

  // Get today's date in YYYY-MM-DD format
  const today = new Date().toISOString().split('T')[0];

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          <Filter className="h-5 w-5" />
          Bộ lọc điểm danh
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Date Range */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Từ ngày
            </Label>
            <Input
              type="date"
              value={filters.start_date || ""}
              onChange={(e) => handleFilterChange("start_date", e.target.value)}
              max={today}
            />
          </div>
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Đến ngày
            </Label>
            <Input
              type="date"
              value={filters.end_date || ""}
              onChange={(e) => handleFilterChange("end_date", e.target.value)}
              max={today}
              min={filters.start_date || ""}
            />
          </div>
        </div>

        {/* Student Search */}
        <div className="space-y-2">
          <Label className="flex items-center gap-2">
            <Search className="h-4 w-4" />
            Tìm kiếm học sinh
          </Label>
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Tìm theo tên, mã học sinh, số điện thoại..."
              value={filters.student_search || ""}
              onChange={(e) => handleFilterChange("student_search", e.target.value)}
              className="pl-10"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  onSearch();
                }
              }}
            />
          </div>
        </div>

        {/* Filter Dropdowns */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* Class Filter */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Lớp học
            </Label>
            <Select
              value={filters.class_id || "all"}
              onValueChange={(value) => handleFilterChange("class_id", value === "all" ? "" : value)}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Tất cả lớp" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả lớp</SelectItem>
                {classes.map((classItem) => (
                  <SelectItem key={classItem.id} value={classItem.id.toString()}>
                    {classItem.name} ({classItem.code})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Schedule Filter */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Ca học
              {isLoadingSchedules && (
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"></div>
              )}
            </Label>
            <Select
              value={filters.schedule_id || "all"}
              onValueChange={(value) => handleFilterChange("schedule_id", value === "all" ? "" : value)}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder={
                  schedules.length === 0 ? "Không có ca học nào" : "Tất cả ca"
                } />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả ca</SelectItem>
                {schedules.length === 0 ? (
                  <SelectItem value="no" disabled>
                    <div className="flex items-center gap-2 text-gray-500">
                      <Clock className="h-4 w-4" />
                      Không có ca học nào
                    </div>
                  </SelectItem>
                ) : (
                  schedules.map((schedule) => (
                    <SelectItem key={schedule.id} value={schedule.id.toString()}>
                      <div className="flex flex-col">
                        <span className="font-medium">{schedule.schedule_name}</span>
                        <span className="text-sm text-gray-500">
                          {schedule.start_time} - {schedule.end_time}
                        </span>
                      </div>
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>

          {/* Attendance Type Filter */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <UserCheck className="h-4 w-4" />
              Loại điểm danh
            </Label>
            <Select
              value={filters.attendance_type || "all"}
              onValueChange={(value) => handleFilterChange("attendance_type", value === "all" ? "" : value)}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Tất cả loại" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả loại</SelectItem>
                <SelectItem value="auto">Tự động</SelectItem>
                <SelectItem value="manual">Thủ công</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Additional Filters */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Camera Filter */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <Camera className="h-4 w-4" />
              Camera
            </Label>
            <Select
              value={filters.camera_id || "all"}
              onValueChange={(value) => handleFilterChange("camera_id", value === "all" ? "" : value)}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Tất cả camera" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả camera</SelectItem>
                {cameras.map((camera) => (
                  <SelectItem key={camera.id} value={camera.id.toString()}>
                    <div className="flex flex-col">
                      <span className="font-medium">{camera.name}</span>
                      <span className="text-sm text-gray-500">{camera.location}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Location Filter */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Vị trí
            </Label>
            <Input
              placeholder="Lọc theo vị trí..."
              value={filters.location || ""}
              onChange={(e) => handleFilterChange("location", e.target.value)}
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-4 pt-4 border-t">
          <Button 
            onClick={onSearch} 
            className="bg-blue-600 hover:bg-blue-700"
            disabled={isLoading}
          >
            <Search className="h-4 w-4 mr-2" />
            Tìm kiếm
          </Button>
          
          {hasActiveFilters && (
            <Button onClick={onClearFilters} variant="outline" disabled={isLoading}>
              <X className="h-4 w-4 mr-2" />
              Xóa bộ lọc
            </Button>
          )}
          
       
        </div>

      
      </CardContent>
    </Card>
  );
});

AttendanceFilters.displayName = 'AttendanceFilters';
