"use client";

import { useState, useEffect, memo, useCallback } from "react";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Calendar, 
  Clock, 
  Loader2, 
  AlertTriangle,
  Users,
  CheckCircle,
  School
} from "lucide-react";
import { 
  addAttendanceScheduleAPI,
  detailAttendanceScheduleAPI,
  AttendanceScheduleFormData,
  AttendanceSchedule
} from "@/api/attendance";
import { Class } from "@/api/class";
import { getAuthToken } from "@/utils/getAuthToken";

interface ScheduleFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  editingSchedule: AttendanceSchedule | null;
  onSuccess: (schedule: AttendanceSchedule) => void;
  classes: Class[];
}

export const ScheduleFormDialog = memo(({
  isOpen,
  onClose,
  editingSchedule,
  onSuccess,
  classes,
}: ScheduleFormDialogProps) => {
  const [formData, setFormData] = useState<AttendanceScheduleFormData>({
    schedule_name: "",
    start_time: "",
    end_time: "",
    days_of_week: [],
    attendance_type: "both",
    is_active: true,
    class_id: undefined,
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const daysOfWeek = [
    { value: 0, label: "Chủ nhật", short: "CN" },
    { value: 1, label: "Thứ hai", short: "T2" },
    { value: 2, label: "Thứ ba", short: "T3" },
    { value: 3, label: "Thứ tư", short: "T4" },
    { value: 4, label: "Thứ năm", short: "T5" },
    { value: 5, label: "Thứ sáu", short: "T6" },
    { value: 6, label: "Thứ bảy", short: "T7" },
  ];

  // Load schedule details for editing
  const loadScheduleDetails = useCallback(async (scheduleId: number) => {
    try {
      setIsLoading(true);
      setError(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await detailAttendanceScheduleAPI(token, scheduleId);

      if (response.code === 1) {
        const schedule = response.data;
        setFormData({
          schedule_name: schedule.schedule_name,
          start_time: schedule.start_time,
          end_time: schedule.end_time,
          days_of_week: schedule.days_of_week.split(',').map(d => parseInt(d.trim())),
          attendance_type: schedule.attendance_type,
          is_active: schedule.is_active,
          class_id: schedule.class_id || undefined,
        });
      } else {
        throw new Error(response.mess || "Lỗi khi tải chi tiết lịch điểm danh");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Load data when dialog opens
  useEffect(() => {
    if (isOpen) {
      if (editingSchedule) {
        loadScheduleDetails(editingSchedule.id);
      } else {
        // Reset form for new schedule
        setFormData({
          schedule_name: "",
          start_time: "",
          end_time: "",
          days_of_week: [],
          attendance_type: "both",
          is_active: true,
          class_id: undefined,
        });
      }
      setError(null);
      setValidationErrors({});
    }
  }, [isOpen, editingSchedule, loadScheduleDetails]);

  // Handle form field changes
  const handleFieldChange = useCallback((field: keyof AttendanceScheduleFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  }, [validationErrors]);

  // Handle days of week selection
  const handleDayToggle = useCallback((dayValue: number) => {
    setFormData(prev => ({
      ...prev,
      days_of_week: prev.days_of_week.includes(dayValue)
        ? prev.days_of_week.filter(d => d !== dayValue)
        : [...prev.days_of_week, dayValue].sort()
    }));
    
    // Clear validation error
    if (validationErrors.days_of_week) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.days_of_week;
        return newErrors;
      });
    }
  }, [validationErrors.days_of_week]);

  // Validate form
  const validateForm = useCallback((): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.schedule_name.trim()) {
      errors.schedule_name = "Vui lòng nhập tên lịch điểm danh";
    }

    if (!formData.start_time) {
      errors.start_time = "Vui lòng chọn thời gian bắt đầu";
    }

    if (!formData.end_time) {
      errors.end_time = "Vui lòng chọn thời gian kết thúc";
    }

    if (formData.start_time && formData.end_time && formData.start_time >= formData.end_time) {
      errors.end_time = "Thời gian kết thúc phải sau thời gian bắt đầu";
    }

    if (formData.days_of_week.length === 0) {
      errors.days_of_week = "Vui lòng chọn ít nhất một ngày trong tuần";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, [formData]);

  // Handle form submission
  const handleSubmit = useCallback(async () => {
    if (!validateForm()) return;

    try {
      setIsSubmitting(true);
      setError(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const submitData = {
        ...formData,
        ...(editingSchedule && { id: editingSchedule.id }),
      };

      const response = await addAttendanceScheduleAPI(token, submitData);

      if (response.code === 1) {
        onSuccess(response.data);
        onClose();
      } else {
        throw new Error(response.mess || "Lỗi khi lưu lịch điểm danh");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, editingSchedule, validateForm, onSuccess, onClose]);

  // Handle dialog close
  const handleClose = useCallback(() => {
    if (!isSubmitting) {
      onClose();
    }
  }, [isSubmitting, onClose]);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            {editingSchedule ? "Chỉnh sửa lịch điểm danh" : "Tạo lịch điểm danh mới"}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Error display */}
          {error && (
            <Alert className="border-red-200 bg-red-50">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-700">{error}</AlertDescription>
            </Alert>
          )}

          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2 text-blue-600" />
                <p className="text-gray-500">Đang tải dữ liệu...</p>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Schedule Name */}
              <div className="space-y-2">
                <Label>Tên lịch điểm danh *</Label>
                <Input
                  placeholder="Ví dụ: Điểm danh buổi sáng, Điểm danh ca chiều..."
                  value={formData.schedule_name}
                  onChange={(e) => handleFieldChange("schedule_name", e.target.value)}
                  className={validationErrors.schedule_name ? "border-red-500" : ""}
                />
                {validationErrors.schedule_name && (
                  <p className="text-sm text-red-600">{validationErrors.schedule_name}</p>
                )}
              </div>

              {/* Time Range */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Thời gian bắt đầu *</Label>
                  <div className="relative">
                    <Clock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      type="time"
                      value={formData.start_time}
                      onChange={(e) => handleFieldChange("start_time", e.target.value)}
                      className={`pl-10 ${validationErrors.start_time ? "border-red-500" : ""}`}
                    />
                  </div>
                  {validationErrors.start_time && (
                    <p className="text-sm text-red-600">{validationErrors.start_time}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label>Thời gian kết thúc *</Label>
                  <div className="relative">
                    <Clock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      type="time"
                      value={formData.end_time}
                      onChange={(e) => handleFieldChange("end_time", e.target.value)}
                      className={`pl-10 ${validationErrors.end_time ? "border-red-500" : ""}`}
                    />
                  </div>
                  {validationErrors.end_time && (
                    <p className="text-sm text-red-600">{validationErrors.end_time}</p>
                  )}
                </div>
              </div>

              {/* Days of Week */}
              <div className="space-y-2">
                <Label>Ngày trong tuần *</Label>
                <div className="grid grid-cols-4 md:grid-cols-7 gap-2">
                  {daysOfWeek.map((day) => (
                    <div key={day.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={`day-${day.value}`}
                        checked={formData.days_of_week.includes(day.value)}
                        onCheckedChange={() => handleDayToggle(day.value)}
                      />
                      <Label 
                        htmlFor={`day-${day.value}`} 
                        className="text-sm font-normal cursor-pointer"
                      >
                        {day.short}
                      </Label>
                    </div>
                  ))}
                </div>
                {validationErrors.days_of_week && (
                  <p className="text-sm text-red-600">{validationErrors.days_of_week}</p>
                )}
              </div>

              {/* Attendance Type */}
              <div className="space-y-2">
                <Label>Loại điểm danh</Label>
                <Select 
                  value={formData.attendance_type} 
                  onValueChange={(value: "check_in" | "check_out" | "both") => 
                    handleFieldChange("attendance_type", value)
                  }
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="check_in">Điểm danh vào</SelectItem>
                    <SelectItem value="check_out">Điểm danh ra</SelectItem>
                    <SelectItem value="both">Cả hai (vào và ra)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Class Selection */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Áp dụng cho lớp (tùy chọn)
                </Label>
                <Select 
                  value={formData.class_id?.toString() || ""} 
                  onValueChange={(value) => handleFieldChange("class_id", value ? parseInt(value) : undefined)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue defaultValue="all" placeholder="Chọn lớp hoặc để trống cho toàn trường" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">
                      <div className="flex items-center gap-2">
                        <School className="h-4 w-4" />
                        Toàn trường
                      </div>
                    </SelectItem>
                    {classes.map((classItem) => (
                      <SelectItem key={classItem.id} value={classItem.id.toString()}>
                        {classItem.name} ({classItem.code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-sm text-gray-500">
                  Để trống để áp dụng cho toàn trường, hoặc chọn lớp cụ thể
                </p>
              </div>

              {/* Active Status */}
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="space-y-1">
                  <Label className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    Trạng thái hoạt động
                  </Label>
                  <p className="text-sm text-gray-500">
                    Lịch điểm danh sẽ {formData.is_active ? "được kích hoạt" : "bị tạm dừng"}
                  </p>
                </div>
                <Switch
                  checked={formData.is_active}
                  onCheckedChange={(checked) => handleFieldChange("is_active", checked)}
                />
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button variant="outline" onClick={handleClose} disabled={isSubmitting}>
              Hủy
            </Button>
            <Button 
              onClick={handleSubmit} 
              disabled={isLoading || isSubmitting}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Đang lưu...
                </>
              ) : (
                <>
                  <Calendar className="h-4 w-4 mr-2" />
                  {editingSchedule ? "Cập nhật" : "Tạo lịch"}
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
});

ScheduleFormDialog.displayName = 'ScheduleFormDialog';
