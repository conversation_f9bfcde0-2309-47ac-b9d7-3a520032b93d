"use client";

import { memo, useCallback } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  Filter,
  X,
  Users,
  Clock,
  Calendar,
  CheckCircle,
} from "lucide-react";
import { AttendanceScheduleFilters as FilterType } from "@/api/attendance";
import { Class } from "@/api/class";

interface ScheduleFiltersProps {
  filters: FilterType;
  onFiltersChange: (filters: FilterType) => void;
  onSearch: () => void;
  onClearFilters: () => void;
  classes: Class[];
  isLoading?: boolean;
}

export const ScheduleFilters = memo(
  ({
    filters,
    onFiltersChange,
    onSearch,
    onClearFilters,
    classes,
    isLoading = false,
  }: ScheduleFiltersProps) => {
    const handleFilterChange = useCallback(
      (key: keyof FilterType, value: any) => {
        onFiltersChange({ ...filters, [key]: value });
      },
      [filters, onFiltersChange]
    );

    const hasActiveFilters = Object.values(filters).some(
      (value) => value !== undefined && value !== "" && value !== null
    );

    const daysOfWeek = [
      { value: 0, label: "Chủ nhật" },
      { value: 1, label: "Thứ hai" },
      { value: 2, label: "Thứ ba" },
      { value: 3, label: "Thứ tư" },
      { value: 4, label: "Thứ năm" },
      { value: 5, label: "Thứ sáu" },
      { value: 6, label: "Thứ bảy" },
    ];

    return (
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Bộ lọc lịch điểm danh
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Schedule Name Search */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              Tìm kiếm lịch điểm danh
            </Label>
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Tìm theo tên lịch điểm danh..."
                value={filters.search || ""}
                onChange={(e) => handleFilterChange("search", e.target.value)}
                className="pl-10"
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    onSearch();
                  }
                }}
              />
            </div>
          </div>

          {/* Filter Dropdowns */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Class Filter */}
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Lớp học
              </Label>
              <Select
                value={filters.class_id || ""}
                onValueChange={(value) => handleFilterChange("class_id", value)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue defaultValue="all" placeholder="Tất cả lớp" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả lớp</SelectItem>
                  <SelectItem value="school-wide">Toàn trường</SelectItem>
                  {classes.map((classItem) => (
                    <SelectItem
                      key={classItem.id}
                      value={classItem.id.toString()}
                    >
                      {classItem.name} ({classItem.code})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Day of Week Filter */}
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Ngày trong tuần
              </Label>
              <Select
                value={filters.day_of_week?.toString() || ""}
                onValueChange={(value) =>
                  handleFilterChange(
                    "day_of_week",
                    value ? parseInt(value) : undefined
                  )
                }
              >
                <SelectTrigger className="w-full">
                  <SelectValue defaultValue="all" placeholder="Tất cả ngày" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả ngày</SelectItem>
                  {daysOfWeek.map((day) => (
                    <SelectItem key={day.value} value={day.value.toString()}>
                      {day.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Attendance Type Filter */}
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Loại điểm danh
              </Label>
              <Select
                value={filters.attendance_type || ""}
                onValueChange={(value) =>
                  handleFilterChange("attendance_type", value)
                }
              >
                <SelectTrigger className="w-full">
                  <SelectValue defaultValue="all" placeholder="Tất cả loại" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả loại</SelectItem>
                  <SelectItem value="check_in">Điểm danh vào</SelectItem>
                  <SelectItem value="check_out">Điểm danh ra</SelectItem>
                  <SelectItem value="both">Cả hai</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Active Status Filter */}
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                Trạng thái
              </Label>
              <Select
                value={filters.is_active?.toString() || ""}
                onValueChange={(value) =>
                  handleFilterChange(
                    "is_active",
                    value === "" ? undefined : value === "true"
                  )
                }
              >
                <SelectTrigger className="w-full">
                  <SelectValue defaultValue="all" placeholder="Tất cả" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả</SelectItem>
                  <SelectItem value="true">Đang hoạt động</SelectItem>
                  <SelectItem value="false">Không hoạt động</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-4 pt-4 border-t">
            <Button
              onClick={onSearch}
              className="bg-blue-600 hover:bg-blue-700"
              disabled={isLoading}
            >
              <Search className="h-4 w-4 mr-2" />
              Tìm kiếm
            </Button>

            {hasActiveFilters && (
              <Button
                onClick={onClearFilters}
                variant="outline"
                disabled={isLoading}
              >
                <X className="h-4 w-4 mr-2" />
                Xóa bộ lọc
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }
);

ScheduleFilters.displayName = "ScheduleFilters";
