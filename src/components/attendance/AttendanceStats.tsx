"use client";

import { memo } from "react";
import { Card, CardContent } from "@/components/ui/card";
import {
  Users,
  UserCheck,
  UserX,
  Clock,
  TrendingUp,
  BarChart3,
} from "lucide-react";
import { AttendanceStats as StatsType } from "@/api/attendance";

interface AttendanceStatsProps {
  stats: StatsType;
  isLoading?: boolean;
}

export const AttendanceStats = memo(
  ({ stats, isLoading = false }: AttendanceStatsProps) => {
    console.log(stats);

    if (isLoading) {
      return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i}>
              <CardContent className="p-6 text-center">
                <div className="animate-pulse">
                  <div className="h-8 w-16 bg-gray-200 rounded mx-auto mb-2"></div>
                  <div className="h-4 w-20 bg-gray-200 rounded mx-auto"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      );
    }

    const safeStats = stats ?? {
      total_present: 0,
      total_absent: 0,
      total_late: 0,
      attendance_rate: 0,
    };
    const totalStudents =
      safeStats.total_present + safeStats.total_absent + safeStats.total_late;

    const statsData = [
      {
        title: "Có mặt",
        value: safeStats.total_present.toLocaleString(),
        icon: UserCheck,
        color: "text-green-600",
        bgColor: "bg-green-50",
        iconColor: "text-green-600",
        description: "Học sinh có mặt",
      },
      {
        title: "Vắng mặt",
        value: safeStats.total_absent.toLocaleString(),
        icon: UserX,
        color: "text-red-600",
        bgColor: "bg-red-50",
        iconColor: "text-red-600",
        description: "Học sinh vắng mặt",
      },
      {
        title: "Đi muộn",
        value: safeStats.total_late.toLocaleString(),
        icon: Clock,
        color: "text-yellow-600",
        bgColor: "bg-yellow-50",
        iconColor: "text-yellow-600",
        description: "Học sinh đến muộn",
      },
      {
        title: "Tỷ lệ có mặt",
        value: `${safeStats.attendance_rate.toFixed(1)}%`,
        icon: TrendingUp,
        color:
          safeStats.attendance_rate >= 90
            ? "text-green-600"
            : safeStats.attendance_rate >= 75
            ? "text-yellow-600"
            : "text-red-600",
        bgColor:
          safeStats.attendance_rate >= 90
            ? "bg-green-50"
            : safeStats.attendance_rate >= 75
            ? "bg-yellow-50"
            : "bg-red-50",
        iconColor:
          safeStats.attendance_rate >= 90
            ? "text-green-600"
            : safeStats.attendance_rate >= 75
            ? "text-yellow-600"
            : "text-red-600",
        description: "Tỷ lệ điểm danh",
      },
    ];

    return (
      <div className="space-y-4">
        {/* Main Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {statsData.map((stat, index) => {
            const Icon = stat.icon;

            return (
              <Card key={index} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className={`text-2xl font-bold ${stat.color}`}>
                        {stat.value}
                      </p>
                      <p className="text-sm text-gray-600 mt-1">{stat.title}</p>
                    </div>
                    <div className={`p-3 rounded-full ${stat.bgColor}`}>
                      <Icon className={`h-6 w-6 ${stat.iconColor}`} />
                    </div>
                  </div>

                  {/* Additional info */}
                  <div className="mt-4 pt-4 border-t border-gray-100">
                    <p className="text-xs text-gray-500">{stat.description}</p>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Summary Card */}
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-blue-100 rounded-full">
                <BarChart3 className="h-6 w-6 text-blue-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900 mb-1">
                  Tổng quan điểm danh
                </h3>
                <p className="text-sm text-gray-600">
                  Tổng cộng <strong>{totalStudents.toLocaleString()}</strong>{" "}
                  lượt điểm danh với tỷ lệ có mặt{" "}
                  <strong>{safeStats.attendance_rate.toFixed(1)}%</strong>
                </p>
              </div>

              {/* Progress Bar */}
              <div className="w-32">
                <div className="flex justify-between text-xs text-gray-600 mb-1">
                  <span>Tỷ lệ</span>
                  <span>{safeStats.attendance_rate.toFixed(1)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      safeStats.attendance_rate >= 90
                        ? "bg-green-500"
                        : safeStats.attendance_rate >= 75
                        ? "bg-yellow-500"
                        : "bg-red-500"
                    }`}
                    style={{
                      width: `${Math.min(safeStats.attendance_rate, 100)}%`,
                    }}
                  ></div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }
);

AttendanceStats.displayName = "AttendanceStats";
