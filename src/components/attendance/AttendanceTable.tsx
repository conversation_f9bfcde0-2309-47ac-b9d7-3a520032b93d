"use client";

import { memo, useCallback } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Users,
  Trash2,
  User,
  Clock,
  MapPin,
  Camera,
  FileText,
  Calendar,
  School,
  Phone
} from "lucide-react";
import { AttendanceRecord } from "@/api/attendance";

interface AttendanceTableProps {
  records: AttendanceRecord[];
  isLoading: boolean;
  totalCount: number;
  onDeleteRecord: (record: AttendanceRecord) => void;
}

export const AttendanceTable = memo(({
  records,
  isLoading,
  totalCount,
  onDeleteRecord,
}: AttendanceTableProps) => {
  
  const formatDateTime = useCallback((dateTimeString: string) => {
    try {
      const date = new Date(dateTimeString);
      return {
        date: date.toLocaleDateString('vi-VN'),
        time: date.toLocaleTimeString('vi-VN', { 
          hour: '2-digit', 
          minute: '2-digit' 
        })
      };
    } catch {
      return { date: 'N/A', time: 'N/A' };
    }
  }, []);

  const getAttendanceTypeBadge = useCallback((type: "auto" | "manual") => {
    return type === "auto" ? (
      <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
        <Camera className="h-3 w-3 mr-1" />
        Tự động
      </Badge>
    ) : (
      <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">
        <User className="h-3 w-3 mr-1" />
        Thủ công
      </Badge>
    );
  }, []);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Danh sách điểm danh
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
              <p className="text-gray-500">Đang tải dữ liệu...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Danh sách điểm danh ({totalCount})
        </CardTitle>
      </CardHeader>
      <CardContent>
        {records.length === 0 ? (
          <div className="text-center py-12 text-gray-500">
            <Users className="h-16 w-16 mx-auto mb-4 text-gray-300" />
            <p className="text-lg font-medium mb-2">Không có dữ liệu điểm danh</p>
            <p className="text-sm">Thử thay đổi bộ lọc hoặc khoảng thời gian tìm kiếm</p>
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[50px]">#</TableHead>
                  <TableHead>Học sinh</TableHead>
                  <TableHead>Lớp học</TableHead>
                  <TableHead>Ca học</TableHead>
                  <TableHead>Thời gian điểm danh</TableHead>
                  <TableHead>Loại</TableHead>
                  <TableHead>Camera</TableHead>
                  <TableHead className="text-right">Thao tác</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {records.map((record, index) => {
                  const { date, time } = formatDateTime(record.attendance_time);

                  return (
                    <TableRow key={record.id} className="hover:bg-gray-50">
                      <TableCell className="font-medium">
                        {index + 1}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <User className="h-4 w-4 text-blue-600" />
                          </div>
                          <div className="min-w-0">
                            <div className="font-medium">{record.student_name}</div>
                            <div className="text-sm text-gray-500">
                              {record.student_code} • {record.student_phone}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{record.class_name}</div>
                          <div className="text-sm text-gray-500">{record.class_code}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-gray-400" />
                          <span>{record.schedule_name}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{date}</div>
                          <div className="text-sm text-gray-500">{time}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getAttendanceTypeBadge(record.attendance_type)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Camera className="h-4 w-4 text-gray-400" />
                          <span className="text-sm">{record.camera_name || 'N/A'}</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => onDeleteRecord(record)}
                          className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                          title="Xóa bản ghi"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
});

AttendanceTable.displayName = 'AttendanceTable';
