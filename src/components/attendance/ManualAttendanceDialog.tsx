"use client";

import { useState, useEffect, memo, useCallback } from "react";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  UserCheck,
  Search,
  Loader2,
  AlertTriangle,
  MapPin,
  Clock,
  User,
  FileText,
  RefreshCw
} from "lucide-react";
import {
  manualAttendanceAPI,
  ManualAttendanceData,
  AttendanceRecord,
  AttendanceSchedule,
  listAttendanceSchedulesAPI
} from "@/api/attendance";
import { listStudentAPI, listStudentsInClassAPI, Student } from "@/api/student";
import { listClassAPI, Class } from "@/api/class";
import { getAuthToken } from "@/utils/getAuthToken";

interface ManualAttendanceDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (record: AttendanceRecord) => void;
}

export const ManualAttendanceDialog = memo(({
  isOpen,
  onClose,
  onSuccess,
}: ManualAttendanceDialogProps) => {
  const [formData, setFormData] = useState<ManualAttendanceData>({
    student_id: 0,
    schedule_id: 0,
    location: "",
    note: "",
  });
  
  const [students, setStudents] = useState<Student[]>([]);
  const [classes, setClasses] = useState<Class[]>([]);
  const [schedules, setSchedules] = useState<AttendanceSchedule[]>([]);
  const [studentSearch, setStudentSearch] = useState("");
  const [selectedClass, setSelectedClass] = useState<string>("");
  const [isLoadingSchedules, setIsLoadingSchedules] = useState(false);
  
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Load initial data
  const loadInitialData = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      // Load classes and students in parallel
      const [classResponse, studentResponse] = await Promise.all([
        listClassAPI({ token }),
        listStudentAPI({ token, page: 1, limit: 100 })
      ]);

      if (classResponse.code === 1) setClasses(classResponse.data);
      if (studentResponse.code === 1) setStudents(studentResponse.data);

    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi khi tải dữ liệu");
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Load schedules separately with better error handling
  const loadSchedules = useCallback(async () => {
    try {
      setIsLoadingSchedules(true);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await listAttendanceSchedulesAPI(token, {
        is_active: true, // Only get active schedules
        limit: 100
      });

      if (response.code === 1) {
        setSchedules(response.data);
      } else {
        throw new Error(response.mess || "Lỗi khi tải danh sách ca học");
      }
    } catch (err) {
      console.error("Error loading schedules:", err);
      // Don't set main error for schedule loading failure, just log it
      setSchedules([]);
    } finally {
      setIsLoadingSchedules(false);
    }
  }, []);

  // Load students by class
  const loadStudentsByClass = useCallback(async (classId: string) => {
    if (!classId) return;

    try {
      const token = getAuthToken();
      if (!token) return;

      const response = await listStudentsInClassAPI({
        token,
        id_class: parseInt(classId),
        page: 1,
        limit: 100
      });

      if (response.code === 1) {
        setStudents(response.data.students);
      }
    } catch (err) {
      console.error("Error loading students by class:", err);
    }
  }, []);

  // Load data when dialog opens
  useEffect(() => {
    if (isOpen) {
      loadInitialData();
      loadSchedules();
    }
  }, [isOpen, loadInitialData, loadSchedules]);

  // Load students when class changes
  useEffect(() => {
    if (selectedClass) {
      loadStudentsByClass(selectedClass);
    }
  }, [selectedClass, loadStudentsByClass]);

  // Handle form field changes
  const handleFieldChange = useCallback((field: keyof ManualAttendanceData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  }, [validationErrors]);

  // Handle class selection
  const handleClassChange = useCallback((classId: string) => {
    setSelectedClass(classId);
    setFormData(prev => ({ ...prev, student_id: 0 })); // Reset student selection
    setStudentSearch(""); // Clear search when class changes

    // Auto-fill location with class location if available
    const selectedClassData = classes.find(c => c.id.toString() === classId);
    if (selectedClassData && selectedClassData.room_info?.name) {
      setFormData(prev => ({
        ...prev,
        location: selectedClassData.room_info?.name || ""
      }));
    }
  }, [classes]);

  // Validate form
  const validateForm = useCallback((): boolean => {
    const errors: Record<string, string> = {};

    // Validate class selection first
    if (!selectedClass) {
      errors.student_id = "Vui lòng chọn lớp học trước";
    } else if (!formData.student_id || formData.student_id === 0) {
      errors.student_id = "Vui lòng chọn học sinh";
    }

    if (!formData.schedule_id || formData.schedule_id === 0) {
      errors.schedule_id = "Vui lòng chọn ca học";
    }

    // Additional validation for schedule availability
    if (formData.schedule_id && schedules.length > 0) {
      const selectedSchedule = schedules.find(s => s.id === formData.schedule_id);
      if (!selectedSchedule) {
        errors.schedule_id = "Ca học đã chọn không hợp lệ";
      }
    }

    // Validate that schedules are loaded
    if (schedules.length === 0 && !isLoadingSchedules) {
      errors.schedule_id = "Không có ca học nào khả dụng";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, [formData, schedules, isLoadingSchedules, selectedClass]);

  // Handle form submission
  const handleSubmit = useCallback(async () => {
    if (!validateForm()) return;

    try {
      setIsSubmitting(true);
      setError(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await manualAttendanceAPI(token, formData);

      if (response.code === 1) {
        onSuccess(response.data);
        onClose();
        // Reset form
        setFormData({
          student_id: 0,
          schedule_id: 0,
          location: "",
          note: "",
        });
        setSelectedClass("");
        setStudentSearch("");
      } else {
        throw new Error(response.mess || "Lỗi khi tạo điểm danh thủ công");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, validateForm, onSuccess, onClose]);

  // Handle dialog close
  const handleClose = useCallback(() => {
    if (!isSubmitting) {
      setFormData({
        student_id: 0,
        schedule_id: 0,
        location: "",
        note: "",
      });
      setSelectedClass("");
      setStudentSearch("");
      setError(null);
      setValidationErrors({});
      onClose();
    }
  }, [isSubmitting, onClose]);

  // Utility function to remove Vietnamese diacritics for fuzzy search
  const removeDiacritics = useCallback((str: string): string => {
    return str
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
      .replace(/đ/g, 'd')
      .replace(/Đ/g, 'D');
  }, []);

  // Enhanced fuzzy search function
  const fuzzyMatch = useCallback((searchTerm: string, targetText: string): boolean => {
    if (!searchTerm || !targetText) return false;

    const search = removeDiacritics(searchTerm.toLowerCase().trim());
    const target = removeDiacritics(targetText.toLowerCase());

    // Direct substring match (highest priority)
    if (target.includes(search)) return true;

    // Fuzzy matching: check if all characters in search term exist in target in order
    let searchIndex = 0;
    for (let i = 0; i < target.length && searchIndex < search.length; i++) {
      if (target[i] === search[searchIndex]) {
        searchIndex++;
      }
    }

    return searchIndex === search.length;
  }, [removeDiacritics]);

  // Filter students based on enhanced search with fuzzy matching
  const filteredStudents = students.filter(student => {
    if (!studentSearch.trim()) return true; // Show all students if no search query

    const searchTerm = studentSearch.trim();
    const fullName = student.full_name || '';
    const codeStudent = student.code_student || '';
    const phone = student.phone || '';

    return fuzzyMatch(searchTerm, fullName) ||
           fuzzyMatch(searchTerm, codeStudent) ||
           fuzzyMatch(searchTerm, phone);
  });

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserCheck className="h-5 w-5" />
            Điểm danh thủ công
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Error display */}
          {error && (
            <Alert className="border-red-200 bg-red-50">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-700">{error}</AlertDescription>
            </Alert>
          )}

          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2 text-blue-600" />
                <p className="text-gray-500">Đang tải dữ liệu...</p>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Class Selection */}
              <div className="space-y-2">
                <Label>Lớp học</Label>
                <Select value={selectedClass} onValueChange={handleClassChange}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Chọn lớp học" />
                  </SelectTrigger>
                  <SelectContent>
                    {classes.map((classItem) => (
                      <SelectItem key={classItem.id} value={classItem.id.toString()}>
                        {classItem.name} ({classItem.code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Student Selection */}
              <div className="space-y-2">
                <Label>Học sinh *</Label>
                <div className="space-y-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder={selectedClass ? "Tìm kiếm học sinh trong lớp..." : "Chọn lớp học trước để tìm kiếm học sinh"}
                      value={studentSearch}
                      onChange={(e) => setStudentSearch(e.target.value)}
                      className="pl-10"
                      disabled={!selectedClass}
                    />
                  </div>

                  {/* Show validation message when no class is selected */}
                  {!selectedClass && studentSearch && (
                    <div className="flex items-center gap-2 p-2 bg-amber-50 border border-amber-200 rounded text-sm text-amber-700">
                      <AlertTriangle className="h-4 w-4" />
                      Vui lòng chọn lớp học trước khi tìm kiếm học sinh
                    </div>
                  )}

                  {/* Live Search Results */}
                  {selectedClass && studentSearch && (
                    <div className="border rounded-md bg-white shadow-sm max-h-60 overflow-y-auto">
                      {filteredStudents.length === 0 ? (
                        <div className="p-3 text-center text-gray-500">
                          <Search className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                          <p className="text-sm">Không tìm thấy học sinh phù hợp với `{studentSearch}`</p>
                          <p className="text-xs text-gray-400 mt-1">Thử tìm kiếm với từ khóa khác</p>
                        </div>
                      ) : (
                        <div className="divide-y">
                          <div className="px-3 py-2 bg-gray-50 border-b">
                            <p className="text-xs font-medium text-gray-600">
                              Tìm thấy {filteredStudents.length} học sinh phù hợp
                            </p>
                          </div>
                          {filteredStudents.slice(0, 10).map((student) => (
                            <button
                              key={student.id}
                              type="button"
                              onClick={() => handleFieldChange("student_id", student.id)}
                              className={`w-full p-3 text-left hover:bg-blue-50 transition-colors ${
                                formData.student_id === student.id ? 'bg-blue-100 border-l-4 border-blue-500' : ''
                              }`}
                            >
                              <div className="flex items-center gap-3">
                                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                                  <User className="h-4 w-4 text-blue-600" />
                                </div>
                                <div className="min-w-0 flex-1">
                                  <div className="font-medium text-gray-900">{student.full_name || 'N/A'}</div>
                                  <div className="text-sm text-gray-500">
                                    {student.code_student || 'N/A'} • {student.phone || 'N/A'}
                                  </div>
                                </div>
                                {formData.student_id === student.id && (
                                  <div className="text-blue-600">
                                    <UserCheck className="h-4 w-4" />
                                  </div>
                                )}
                              </div>
                            </button>
                          ))}
                          {filteredStudents.length > 10 && (
                            <div className="p-2 text-center text-xs text-gray-500 bg-gray-50">
                              Và {filteredStudents.length - 10} học sinh khác...
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  )}

                  {/* Fallback Select for when no search is active */}
                  {selectedClass && !studentSearch && (
                    <Select
                      value={formData.student_id === 0 ? "" : formData.student_id.toString()}
                      onValueChange={(value) => handleFieldChange("student_id", value ? parseInt(value) : 0)}
                    >
                      <SelectTrigger className={validationErrors.student_id ? "border-red-500 w-full" : "w-full"}>
                        <SelectValue placeholder={
                          students.length === 0 ? "Không có học sinh nào trong lớp" : "Chọn học sinh hoặc tìm kiếm ở trên"
                        } />
                      </SelectTrigger>
                      <SelectContent>
                        {students.length === 0 ? (
                          <SelectItem value="no-students" disabled>
                            <div className="flex items-center gap-2 text-gray-500">
                              <User className="h-4 w-4" />
                              Lớp học chưa có học sinh nào
                            </div>
                          </SelectItem>
                        ) : (
                          students.map((student) => (
                            <SelectItem key={student.id} value={student.id.toString()}>
                              <div className="flex items-center gap-2">
                                <User className="h-4 w-4" />
                                <div>
                                  <div className="font-medium">{student.full_name || 'N/A'}</div>
                                  <div className="text-sm text-gray-500">
                                    {student.code_student || 'N/A'} • {student.phone || 'N/A'}
                                  </div>
                                </div>
                              </div>
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                  )}

                  {/* Selected student display */}
                  {formData.student_id !== 0 && (
                    <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                          <UserCheck className="h-4 w-4 text-green-600" />
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-green-800">Đã chọn học sinh:</p>
                          {(() => {
                            const selectedStudent = students.find(s => s.id === formData.student_id);
                            return selectedStudent ? (
                              <div>
                                <p className="font-medium text-green-900">{selectedStudent.full_name}</p>
                                <p className="text-sm text-green-700">
                                  {selectedStudent.code_student} • {selectedStudent.phone}
                                </p>
                              </div>
                            ) : (
                              <p className="text-sm text-green-700">Học sinh đã chọn</p>
                            );
                          })()}
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => handleFieldChange("student_id", 0)}
                          className="text-green-600 hover:text-green-700 hover:bg-green-100"
                        >
                          Bỏ chọn
                        </Button>
                      </div>
                    </div>
                  )}

                  {validationErrors.student_id && (
                    <p className="text-sm text-red-600">{validationErrors.student_id}</p>
                  )}
                </div>
              </div>

              {/* Schedule Selection */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Ca học *
                  {isLoadingSchedules && (
                    <Loader2 className="h-3 w-3 animate-spin text-blue-600" />
                  )}
                </Label>
                <Select
                  value={formData.schedule_id === 0 ? "" : formData.schedule_id.toString()}
                  onValueChange={(value) => handleFieldChange("schedule_id", value ? parseInt(value) : 0)}
                  disabled={isLoadingSchedules}
                >
                  <SelectTrigger className={validationErrors.schedule_id ? "border-red-500 w-full" : "w-full"}>
                    <SelectValue placeholder={
                      isLoadingSchedules ? "Đang tải ca học..." :
                      schedules.length === 0 ? "Không có ca học nào" :
                      "Chọn ca học"
                    } />
                  </SelectTrigger>
                  <SelectContent>
                    {schedules.length === 0 && !isLoadingSchedules ? (
                      <SelectItem value="no-schedules" disabled>
                        <div className="flex items-center gap-2 text-gray-500">
                          <AlertTriangle className="h-4 w-4" />
                          Không có ca học nào
                        </div>
                      </SelectItem>
                    ) : (
                      schedules.map((schedule) => (
                        <SelectItem key={schedule.id} value={schedule.id.toString()}>
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4 text-blue-600" />
                            <div className="flex items-center justify-center">
                              <div className="font-medium">{schedule.schedule_name}</div>
                              <div className="text-sm text-gray-500 ml-2">
                                {schedule.start_time} - {schedule.end_time}
                              </div>
                            </div>
                          </div>
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                {validationErrors.schedule_id && (
                  <p className="text-sm text-red-600">{validationErrors.schedule_id}</p>
                )}
                {schedules.length === 0 && !isLoadingSchedules && (
                  <div className="flex items-center justify-between p-2 bg-amber-50 border border-amber-200 rounded">
                    <p className="text-sm text-amber-700 flex items-center gap-1">
                      <AlertTriangle className="h-3 w-3" />
                      Không tìm thấy ca học nào
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={loadSchedules}
                      className="text-amber-700 border-amber-300 hover:bg-amber-100"
                    >
                      <RefreshCw className="h-3 w-3 mr-1" />
                      Thử lại
                    </Button>
                  </div>
                )}
              </div>

              {/* Location */}
              <div className="space-y-2">
                <Label>Vị trí</Label>
                <div className="relative">
                  <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Vị trí điểm danh (tự động điền theo lớp)"
                    value={formData.location}
                    onChange={(e) => handleFieldChange("location", e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Notes */}
              <div className="space-y-2">
                <Label>Ghi chú</Label>
                <div className="relative">
                  <FileText className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Textarea
                    placeholder="Ghi chú thêm (ví dụ: đến muộn, xin phép...)"
                    value={formData.note}
                    onChange={(e) => handleFieldChange("note", e.target.value)}
                    className="pl-10 min-h-[80px]"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button variant="outline" onClick={handleClose} disabled={isSubmitting}>
              Hủy
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isLoading || isSubmitting || isLoadingSchedules || schedules.length === 0}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Đang tạo...
                </>
              ) : isLoadingSchedules ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Đang tải ca học...
                </>
              ) : schedules.length === 0 ? (
                <>
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Không có ca học
                </>
              ) : (
                <>
                  <UserCheck className="h-4 w-4 mr-2" />
                  Tạo điểm danh
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
});

ManualAttendanceDialog.displayName = 'ManualAttendanceDialog';
