"use client";

import { memo } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Eye, 
  Calendar, 
  Clock, 
  Users,
  School,
  CheckCircle,
  XCircle,
  Edit,
  Trash2
} from "lucide-react";
import { AttendanceSchedule } from "@/api/attendance";

interface ScheduleDetailDialogProps {
  isOpen: boolean;
  onClose: () => void;
  schedule: AttendanceSchedule | null;
  onEdit: (schedule: AttendanceSchedule) => void;
  onDelete: (schedule: AttendanceSchedule) => void;
}

export const ScheduleDetailDialog = memo(({
  isOpen,
  onClose,
  schedule,
  onEdit,
  onDelete,
}: ScheduleDetailDialogProps) => {
  if (!schedule) return null;

  const formatTime = (timeString: string) => {
    try {
      const [hours, minutes] = timeString.split(':');
      return `${hours}:${minutes}`;
    } catch {
      return timeString;
    }
  };

  const formatDaysOfWeek = (daysString: string) => {
    const dayNames = ['Ch<PERSON> nhật', 'Thứ hai', 'Thứ ba', 'Thứ tư', 'Thứ năm', 'Thứ sáu', 'Thứ bảy'];
    const dayShorts = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];
    const days = daysString.split(',').map(d => parseInt(d.trim()));
    
    return {
      full: days.map(day => dayNames[day]).join(', '),
      short: days.map(day => dayShorts[day]).join(', ')
    };
  };

  const getAttendanceTypeBadge = (type: "check_in" | "check_out" | "both") => {
    switch (type) {
      case "check_in":
        return (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
            Điểm danh vào
          </Badge>
        );
      case "check_out":
        return (
          <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
            Điểm danh ra
          </Badge>
        );
      case "both":
        return (
          <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">
            Cả hai (vào và ra)
          </Badge>
        );
      default:
        return <Badge variant="outline">{type}</Badge>;
    }
  };

  const formatDateTime = (dateTimeString: string) => {
    try {
      const date = new Date(dateTimeString);
      return {
        date: date.toLocaleDateString('vi-VN'),
        time: date.toLocaleTimeString('vi-VN', { 
          hour: '2-digit', 
          minute: '2-digit' 
        })
      };
    } catch {
      return { date: 'N/A', time: 'N/A' };
    }
  };

  const daysInfo = formatDaysOfWeek(schedule.days_of_week);
  const createdAt = formatDateTime(schedule.created_at);
  const updatedAt = formatDateTime(schedule.updated_at);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Chi tiết lịch điểm danh
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Schedule Header */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                  <Calendar className="h-8 w-8 text-blue-600" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">
                    {schedule.schedule_name}
                  </h2>
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <span className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      {formatTime(schedule.start_time)} - {formatTime(schedule.end_time)}
                    </span>
                    <span className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      {daysInfo.short}
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                {schedule.is_active ? (
                  <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
                    <CheckCircle className="h-4 w-4 mr-1" />
                    Hoạt động
                  </Badge>
                ) : (
                  <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">
                    <XCircle className="h-4 w-4 mr-1" />
                    Không hoạt động
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {/* Schedule Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">
                Thông tin cơ bản
              </h3>
              
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <Clock className="h-5 w-5 text-gray-400 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-900">Thời gian</p>
                    <p className="text-gray-600">
                      {formatTime(schedule.start_time)} - {formatTime(schedule.end_time)}
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <Calendar className="h-5 w-5 text-gray-400 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-900">Ngày trong tuần</p>
                    <p className="text-gray-600">{daysInfo.full}</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="h-5 w-5 mt-0.5">
                    {getAttendanceTypeBadge(schedule.attendance_type)}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Loại điểm danh</p>
                    <p className="text-gray-600">
                      {schedule.attendance_type === 'check_in' ? 'Chỉ điểm danh vào' :
                       schedule.attendance_type === 'check_out' ? 'Chỉ điểm danh ra' :
                       'Điểm danh cả vào và ra'}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Application Scope */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">
                Phạm vi áp dụng
              </h3>
              
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  {schedule.class_name ? (
                    <Users className="h-5 w-5 text-gray-400 mt-0.5" />
                  ) : (
                    <School className="h-5 w-5 text-gray-400 mt-0.5" />
                  )}
                  <div>
                    <p className="font-medium text-gray-900">Áp dụng cho</p>
                    <p className="text-gray-600">
                      {schedule.class_name ? (
                        `${schedule.class_name} (${schedule.class_code})`
                      ) : (
                        "Toàn trường"
                      )}
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-gray-400 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-900">Trạng thái</p>
                    <p className="text-gray-600">
                      {schedule.is_active ? 
                        "Đang hoạt động - Lịch này đang được sử dụng cho điểm danh" :
                        "Không hoạt động - Lịch này đã bị tạm dừng"
                      }
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Timestamps */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              Thông tin hệ thống
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <p className="font-medium text-gray-700">Ngày tạo</p>
                <p className="text-gray-600">
                  {createdAt.date} lúc {createdAt.time}
                </p>
              </div>
              <div>
                <p className="font-medium text-gray-700">Cập nhật lần cuối</p>
                <p className="text-gray-600">
                  {updatedAt.date} lúc {updatedAt.time}
                </p>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-between pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Đóng
            </Button>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  onEdit(schedule);
                  onClose();
                }}
                className="text-green-600 hover:text-green-700 hover:bg-green-50"
              >
                <Edit className="h-4 w-4 mr-2" />
                Chỉnh sửa
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  onDelete(schedule);
                  onClose();
                }}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Xóa
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
});

ScheduleDetailDialog.displayName = 'ScheduleDetailDialog';
