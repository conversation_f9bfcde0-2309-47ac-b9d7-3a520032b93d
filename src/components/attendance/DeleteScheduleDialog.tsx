"use client";

import { memo } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { 
  Trash2, 
  AlertTriangle, 
  Calendar, 
  Loader2, 
  Clock, 
  Users,
  School,
  CheckCircle,
  XCircle
} from "lucide-react";
import { AttendanceSchedule } from "@/api/attendance";

interface DeleteScheduleDialogProps {
  isOpen: boolean;
  onClose: () => void;
  schedule: AttendanceSchedule | null;
  onConfirm: (schedule: AttendanceSchedule) => void;
  isDeleting: boolean;
}

export const DeleteScheduleDialog = memo(({
  isOpen,
  onClose,
  schedule,
  onConfirm,
  isDeleting,
}: DeleteScheduleDialogProps) => {
  if (!schedule) return null;

  const handleConfirm = () => {
    onConfirm(schedule);
  };

  const formatTime = (timeString: string) => {
    try {
      const [hours, minutes] = timeString.split(':');
      return `${hours}:${minutes}`;
    } catch {
      return timeString;
    }
  };

  const formatDaysOfWeek = (daysString: string) => {
    const dayNames = ['Chủ nhật', 'Thứ hai', 'Thứ ba', 'Thứ tư', 'Thứ năm', 'Thứ sáu', 'Thứ bảy'];
    const days = daysString.split(',').map(d => parseInt(d.trim()));
    return days.map(day => dayNames[day]).join(', ');
  };

  const getAttendanceTypeBadge = (type: "check_in" | "check_out" | "both") => {
    switch (type) {
      case "check_in":
        return (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
            Điểm danh vào
          </Badge>
        );
      case "check_out":
        return (
          <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
            Điểm danh ra
          </Badge>
        );
      case "both":
        return (
          <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">
            Cả hai
          </Badge>
        );
      default:
        return <Badge variant="outline">{type}</Badge>;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5 text-red-600" />
            Xóa lịch điểm danh
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Warning */}
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-700">
              <strong>Cảnh báo:</strong> Bạn có chắc chắn muốn xóa lịch điểm danh này? 
              Thao tác này không thể hoàn tác và có thể ảnh hưởng đến hệ thống điểm danh.
            </AlertDescription>
          </Alert>

          {/* Schedule Details */}
          <div className="bg-gray-50 rounded-lg p-6 space-y-4">
            <h3 className="font-medium text-lg mb-4">Thông tin lịch điểm danh</h3>
            
            {/* Schedule Header */}
            <div className="flex items-center gap-3 pb-3 border-b">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <Calendar className="h-6 w-6 text-blue-600" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-lg">{schedule.schedule_name}</h4>
                <p className="text-sm text-gray-600">
                  {formatTime(schedule.start_time)} - {formatTime(schedule.end_time)}
                </p>
              </div>
              <div className="flex items-center gap-2">
                {schedule.is_active ? (
                  <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Hoạt động
                  </Badge>
                ) : (
                  <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">
                    <XCircle className="h-3 w-3 mr-1" />
                    Không hoạt động
                  </Badge>
                )}
              </div>
            </div>

            {/* Schedule Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">Ngày áp dụng:</span>
                  <span className="font-medium">
                    {formatDaysOfWeek(schedule.days_of_week)}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Clock className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">Loại điểm danh:</span>
                  {getAttendanceTypeBadge(schedule.attendance_type)}
                </div>
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm">
                  <Users className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">Áp dụng cho:</span>
                  <span className="font-medium">
                    {schedule.class_name ? (
                      `${schedule.class_name} (${schedule.class_code})`
                    ) : (
                      <span className="flex items-center gap-1">
                        <School className="h-3 w-3" />
                        Toàn trường
                      </span>
                    )}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">Tạo lúc:</span>
                  <span>
                    {new Date(schedule.created_at).toLocaleDateString('vi-VN')}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Confirmation Text */}
          <div className="text-sm text-gray-700 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <p>
              <strong>Lưu ý:</strong> Việc xóa lịch điểm danh{" "}
              <strong>`{schedule.schedule_name}`</strong> sẽ:
            </p>
            <ul className="mt-2 list-disc list-inside space-y-1">
              <li>Xóa vĩnh viễn lịch điểm danh này khỏi hệ thống</li>
              <li>Có thể ảnh hưởng đến các quy trình điểm danh tự động</li>
              <li>Không thể khôi phục sau khi xóa</li>
            </ul>
            <p className="mt-2">
              Hãy chắc chắn rằng đây là thao tác bạn muốn thực hiện.
            </p>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button 
              variant="outline" 
              onClick={onClose}
              disabled={isDeleting}
            >
              Hủy
            </Button>
            <Button 
              variant="destructive"
              onClick={handleConfirm}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Đang xóa...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Xóa lịch điểm danh
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
});

DeleteScheduleDialog.displayName = 'DeleteScheduleDialog';
