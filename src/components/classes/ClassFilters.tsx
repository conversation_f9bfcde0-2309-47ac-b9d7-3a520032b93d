"use client";

import { useState, memo } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Search, Filter, X, Plus } from "lucide-react";

interface ClassFiltersProps {
  searchName: string;
  searchCode: string;
  searchSemester: string;
  onSearchChange: (field: string, value: string) => void;
  onSearch: () => void;
  onClearFilters: () => void;
  onAddClass: () => void;
}

export const ClassFilters = memo(
  ({
    searchName,
    searchCode,
    searchSemester,
    onSearchChange,
    onSearch,
    onClearFilters,
    onAddClass,
  }: ClassFiltersProps) => {

    return (
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              <Filter className="h-5 w-5" />
              <PERSON><PERSON> lọc tìm kiếm
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                onClick={onAddClass}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                Thêm lớp học
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">


          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t">
             <div className="space-y-2">
              <Label htmlFor="search-name">Tên lớp học</Label>
              <Input
                id="search-name"
                placeholder="Tìm kiếm theo tên lớp học..."
                value={searchName}
                onChange={(e) => onSearchChange("name", e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="class-code">Mã lớp học</Label>
              <Input
                id="class-code"
                placeholder="Ví dụ: CNTTA, 10A1"
                value={searchCode}
                onChange={(e) => onSearchChange("code", e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="semester">Học kỳ</Label>
              <Input
                id="semester"
                placeholder="Ví dụ: 2025-2026"
                value={searchSemester}
                onChange={(e) => onSearchChange("semester", e.target.value)}
              />
            </div>
          </div>

          <div className="flex items-center gap-4 pt-4 border-t">
            <Button onClick={onSearch} >
              <Search className="h-4 w-4 mr-2" />
              Tìm kiếm
            </Button>
            <Button onClick={onClearFilters} variant="outline">
              <X className="h-4 w-4 mr-2" />
              Xóa bộ lọc
            </Button>
          </div>

      
        </CardContent>
      </Card>
    );
  }
);

ClassFilters.displayName = "ClassFilters";
