"use client";

import { useState, useEffect, memo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Class, ClassFormData } from "@/api/class";
import { Semester } from "@/api/semester";
import { Teacher } from "@/api/teacher";
import { Room } from "@/api/classroom";

interface ClassFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  editingClass: Class | null;
  onSubmit: (formData: ClassFormData) => Promise<void>;
  isSubmitting: boolean;
  semesters: Semester[];
  teachers: Teacher[];
  rooms: Room[];
}

export const ClassFormDialog = memo(({
  isOpen,
  onClose,
  editingClass,
  onSubmit,
  isSubmitting,
  semesters,
  teachers,
  rooms,
}: ClassFormDialogProps) => {
  // Form state
  const [formData, setFormData] = useState<ClassFormData>({
    name: "",
    code: "",
    id_semester: 0,
    teacher_id: 0,
    room_id: 0,
    school_id: 1, // Default school ID
  });
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Initialize form data when dialog opens or editing class changes
  useEffect(() => {
    if (isOpen) {
      if (editingClass) {
        setFormData({
          name: editingClass.name,
          code: editingClass.code,
          id_semester: editingClass.semester_info?.id || 0,
          teacher_id: editingClass.teacher_info?.id || 0,
          room_id: editingClass.room_id,
          school_id: editingClass.school_id,
        });
      } else {
        setFormData({
          name: "",
          code: "",
          id_semester: 0,
          teacher_id: 0,
          room_id: 0,
          school_id: 1,
        });
      }
      setValidationErrors({});
    }
  }, [isOpen, editingClass]);

  // Validation function
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    // Required fields
    if (!formData.name.trim()) {
      errors.name = "Tên lớp học là bắt buộc";
    }

    if (!formData.code.trim()) {
      errors.code = "Mã lớp học là bắt buộc";
    }

    if (!formData.id_semester || formData.id_semester === 0) {
      errors.id_semester = "Học kỳ là bắt buộc";
    }

    if (!formData.teacher_id || formData.teacher_id === 0) {
      errors.teacher_id = "Giáo viên là bắt buộc";
    }

    if (!formData.room_id || formData.room_id === 0) {
      errors.room_id = "Phòng học là bắt buộc";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Form handlers
  const handleInputChange = (field: keyof ClassFormData, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    await onSubmit(formData);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>
            {editingClass ? "Chỉnh sửa lớp học" : "Thêm lớp học mới"}
          </DialogTitle>
          <DialogDescription>
            {editingClass 
              ? "Cập nhật thông tin lớp học trong hệ thống." 
              : "Thêm lớp học mới vào hệ thống quản lý."
            }
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Tên lớp học *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                placeholder="Nhập tên lớp học"
                className={validationErrors.name ? "border-red-500" : ""}
                disabled={isSubmitting}
              />
              {validationErrors.name && (
                <p className="text-sm text-red-600">{validationErrors.name}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="code">Mã lớp học *</Label>
              <Input
                id="code"
                value={formData.code}
                onChange={(e) => handleInputChange("code", e.target.value)}
                placeholder="Ví dụ: CNTTA, 10A1"
                className={validationErrors.code ? "border-red-500" : ""}
                disabled={isSubmitting}
              />
              {validationErrors.code && (
                <p className="text-sm text-red-600">{validationErrors.code}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="semester">Học kỳ *</Label>
            <Select
              value={formData.id_semester.toString()}
              onValueChange={(value) => handleInputChange("id_semester", parseInt(value))}
              disabled={isSubmitting}
            >
              <SelectTrigger className={validationErrors.id_semester ? "border-red-500" : ""}>
                <SelectValue placeholder="Chọn học kỳ" />
              </SelectTrigger>
              <SelectContent>
                {semesters.map((semester) => (
                  <SelectItem key={semester.id} value={semester.id.toString()}>
                    {semester.name} ({semester.code})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {validationErrors.id_semester && (
              <p className="text-sm text-red-600">{validationErrors.id_semester}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="teacher">Giáo viên chủ nhiệm *</Label>
            <Select
              value={formData.teacher_id.toString()}
              onValueChange={(value) => handleInputChange("teacher_id", parseInt(value))}
              disabled={isSubmitting}
            >
              <SelectTrigger className={validationErrors.teacher_id ? "border-red-500" : ""}>
                <SelectValue placeholder="Chọn giáo viên" />
              </SelectTrigger>
              <SelectContent>
                {teachers.map((teacher) => (
                  <SelectItem key={teacher.id} value={teacher.id.toString()}>
                    {teacher.name} - {teacher.email}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {validationErrors.teacher_id && (
              <p className="text-sm text-red-600">{validationErrors.teacher_id}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="room">Phòng học *</Label>
            <Select
              value={formData.room_id.toString()}
              onValueChange={(value) => handleInputChange("room_id", parseInt(value))}
              disabled={isSubmitting}
            >
              <SelectTrigger className={validationErrors.room_id ? "border-red-500" : ""}>
                <SelectValue placeholder="Chọn phòng học" />
              </SelectTrigger>
              <SelectContent>
                {rooms.map((room) => (
                  <SelectItem key={room.id} value={room.id.toString()}>
                    {room.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {validationErrors.room_id && (
              <p className="text-sm text-red-600">{validationErrors.room_id}</p>
            )}
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Hủy
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {editingClass ? "Cập nhật" : "Thêm lớp học"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
});

ClassFormDialog.displayName = 'ClassFormDialog';
