"use client";

import { useRouter } from "next/navigation";
import { memo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  School,
  Edit,
  Trash2,
  User,
  Users,
  Calendar,
  MapPin
} from "lucide-react";
import { Class } from "@/api/class";
import { Room } from "@/api/classroom";

interface ClassTableProps {
  classes: Class[];
  rooms: Room[];
  onEdit: (classItem: Class) => void;
  onDelete: (classItem: Class) => void;
  isLoading?: boolean;
  totalCount: number;
}

export const ClassTable = memo(({
  classes,
  rooms,
  onEdit,
  onDelete,
  isLoading = false,
  totalCount,
}: ClassTableProps) => {
  const router = useRouter();
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg"><PERSON>h sách lớp học</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
              <p className="text-gray-500">Đang tải dữ liệu...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <School className="h-5 w-5" />
          Danh sách lớp học ({totalCount})
        </CardTitle>
      </CardHeader>
      <CardContent>
        {classes.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <School className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p className="text-lg font-medium mb-2">Không có lớp học nào</p>
            <p className="text-sm">Chưa có lớp học nào được thêm vào hệ thống</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[50px]">STT</TableHead>
                  <TableHead>Tên lớp học</TableHead>
                  <TableHead>Mã lớp</TableHead>
                  <TableHead>Học kỳ</TableHead>
                  <TableHead>Giáo viên</TableHead>
                  <TableHead>Phòng học</TableHead>
                  <TableHead className="text-right">Thao tác</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {classes.map((classItem, index) => (
                  <TableRow key={classItem.id} className="hover:bg-gray-50">
                    <TableCell className="font-medium">
                      {index + 1}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <School className="h-4 w-4 text-blue-600" />
                        <span className="font-medium">{classItem.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="font-mono">
                        {classItem.code}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-500" />
                        <div>
                          {classItem.semester_info ? (
                            <div>
                              <div className="font-medium">{classItem.semester_info.name}</div>
                             
                            </div>
                          ) : (
                            <span className="text-gray-400">Chưa có thông tin</span>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-gray-500" />
                        <div>
                          {classItem.teacher_info ? (
                            <div>
                              <div className="font-medium">{classItem.teacher_info.name}</div>
                              <div className="text-xs text-gray-500">
                                {classItem.teacher_info.email}
                              </div>
                            </div>
                          ) : (
                            <span className="text-gray-400">Chưa phân công</span>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-gray-500" />
                        {classItem.room_id ? (
                          <span className="font-medium">{rooms.find(r => r.id === classItem.room_id)?.name}</span>
                        ) : (
                          <span className="text-gray-400">Chưa phân phòng</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => router.push(`/classes/${classItem.id}`)}
                          className="h-8 w-8 p-0"
                          title="Xem học sinh"
                        >
                          <Users className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => onEdit(classItem)}
                          className="h-8 w-8 p-0"
                          title="Chỉnh sửa"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => onDelete(classItem)}
                          className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                          title="Xóa"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
});

ClassTable.displayName = 'ClassTable';
