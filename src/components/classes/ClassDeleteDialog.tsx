"use client";

import { memo } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Loader2, AlertTriangle } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Class } from "@/api/class";

interface ClassDeleteDialogProps {
  isOpen: boolean;
  onClose: () => void;
  classItem: Class | null;
  onConfirm: (classItem: Class) => Promise<void>;
  isDeleting: boolean;
}

export const ClassDeleteDialog = memo(({
  isOpen,
  onClose,
  classItem,
  onConfirm,
  isDeleting,
}: ClassDeleteDialogProps) => {
  if (!classItem) return null;

  const handleConfirm = async () => {
    await onConfirm(classItem);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Xác nhận xóa lớp học
          </DialogTitle>
          <DialogDescription>
            Bạn có chắc chắn muốn xóa lớp học này? Hành động này không thể hoàn tác.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-700">
              <strong>Cảnh báo:</strong> Việc xóa lớp học sẽ:
              <ul className="mt-2 ml-4 list-disc space-y-1">
                <li>Xóa vĩnh viễn thông tin lớp học khỏi hệ thống</li>
                <li>Ảnh hưởng đến dữ liệu học sinh trong lớp</li>
                <li>Có thể ảnh hưởng đến các báo cáo và thống kê liên quan</li>
                <li>Giải phóng phòng học và giáo viên được phân công</li>
              </ul>
            </AlertDescription>
          </Alert>

          <div className="bg-gray-50 p-4 rounded-lg space-y-2">
            <h4 className="font-medium text-gray-900">Thông tin lớp học sẽ bị xóa:</h4>
            <div className="space-y-1 text-sm">
              <p><span className="font-medium">Tên lớp:</span> {classItem.name}</p>
              <p><span className="font-medium">Mã lớp:</span> {classItem.code}</p>
              {classItem.semester_info && (
                <p><span className="font-medium">Học kỳ:</span> {classItem.semester_info.name}</p>
              )}
              {classItem.teacher_info && (
                <p><span className="font-medium">Giáo viên:</span> {classItem.teacher_info.name}</p>
              )}
              {classItem.room_info && (
                <p><span className="font-medium">Phòng học:</span> {classItem.room_info.name}</p>
              )}
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isDeleting}
          >
            Hủy
          </Button>
          <Button
            type="button"
            variant="destructive"
            onClick={handleConfirm}
            disabled={isDeleting}
          >
            {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Xóa lớp học
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
});

ClassDeleteDialog.displayName = 'ClassDeleteDialog';
