"use client";

import { memo } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { User<PERSON><PERSON>, Alert<PERSON><PERSON>gle, User, Loader2 } from "lucide-react";
import { Student } from "@/api/student";

interface RemoveStudentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  student: Student | null;
  className: string;
  onConfirm: (student: Student) => void;
  isRemoving: boolean;
}

export const RemoveStudentDialog = memo(({
  isOpen,
  onClose,
  student,
  className,
  onConfirm,
  isRemoving,
}: RemoveStudentDialogProps) => {
  if (!student) return null;

  const handleConfirm = () => {
    onConfirm(student);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserMinus className="h-5 w-5 text-red-600" />
            Xóa sinh viên khỏi lớp
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Warning */}
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-700">
              Bạn có chắc chắn muốn xóa sinh viên này khỏi lớp?
            </AlertDescription>
          </Alert>

          {/* Student info */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                {student.avatar ? (
                  <img
                    src={student.avatar}
                    alt={student.full_name}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                ) : (
                  <User className="h-6 w-6 text-blue-600" />
                )}
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-lg">{student.full_name}</h3>
                <p className="text-sm text-gray-600">
                  Mã SV: {student.code_student}
                </p>
                <p className="text-sm text-gray-600">
                  SĐT: {student.phone || 'Chưa cập nhật'}
                </p>
              </div>
            </div>
          </div>

          {/* Class info */}
          <div className="text-sm text-gray-600">
            <p><strong>Lớp:</strong> {className}</p>
          </div>

          {/* Confirmation text */}
          <div className="text-sm text-gray-700">
            <p>
              Sinh viên <strong>{student.full_name}</strong> sẽ được xóa khỏi lớp <strong>{className}</strong>.
              Thao tác này có thể được hoàn tác bằng cách thêm lại sinh viên vào lớp.
            </p>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4">
            <Button 
              variant="outline" 
              onClick={onClose}
              disabled={isRemoving}
            >
              Hủy
            </Button>
            <Button 
              variant="destructive"
              onClick={handleConfirm}
              disabled={isRemoving}
            >
              {isRemoving ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Đang xóa...
                </>
              ) : (
                <>
                  <UserMinus className="h-4 w-4 mr-2" />
                  Xóa khỏi lớp
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
});

RemoveStudentDialog.displayName = 'RemoveStudentDialog';
