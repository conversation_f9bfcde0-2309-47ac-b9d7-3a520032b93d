"use client";

import { useState, useEffect, memo, useCallback } from "react";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Search, 
  UserPlus, 
  Users, 
  Loader2, 
  CheckCircle, 
  AlertTriangle,
  User
} from "lucide-react";
import { listStudentAPI, Student, addMultipleStudentsToClassAPI, AddMultipleStudentsResponse } from "@/api/student";
import { getAuthToken } from "@/utils/getAuthToken";

interface AddStudentsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  classId: number;
  className: string;
  onSuccess: (result: AddMultipleStudentsResponse) => void;
}

export const AddStudentsDialog = memo(({
  isOpen,
  onClose,
  classId,
  className,
  onSuccess,
}: AddStudentsDialogProps) => {
  const [students, setStudents] = useState<Student[]>([]);
  const [selectedStudentIds, setSelectedStudentIds] = useState<Set<number>>(new Set());
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load available students
  const loadStudents = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const params = {
        token,
        page: 1,
        limit: 100, // Load more students for selection
        ...(searchTerm && { search: searchTerm }),
      };

      const response = await listStudentAPI(params);

      if (response.code === 1) {
        setStudents(response.data);
      } else {
        throw new Error(response.mess || "Lỗi khi tải danh sách sinh viên");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
      setStudents([]);
    } finally {
      setIsLoading(false);
    }
  }, [searchTerm]);

  // Load students when dialog opens or search term changes
  useEffect(() => {
    if (isOpen) {
      loadStudents();
    }
  }, [isOpen, loadStudents]);

  // Handle student selection
  const handleStudentToggle = useCallback((studentId: number) => {
    setSelectedStudentIds(prev => {
      const newSelected = new Set(prev);
      if (newSelected.has(studentId)) {
        newSelected.delete(studentId);
      } else {
        newSelected.add(studentId);
      }
      return newSelected;
    });
  }, []);

  // Handle select all
  const handleSelectAll = useCallback(() => {
    if (selectedStudentIds.size === students.length) {
      setSelectedStudentIds(new Set());
    } else {
      setSelectedStudentIds(new Set(students.map(s => s.id)));
    }
  }, [students, selectedStudentIds.size]);

  // Handle form submission
  const handleSubmit = useCallback(async () => {
    if (selectedStudentIds.size === 0) {
      setError("Vui lòng chọn ít nhất một sinh viên");
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await addMultipleStudentsToClassAPI(
        token,
        classId,
        Array.from(selectedStudentIds)
      );

      if (response.code === 1) {
        onSuccess(response.data);
        onClose();
        // Reset form
        setSelectedStudentIds(new Set());
        setSearchTerm("");
      } else {
        throw new Error(response.mess || "Lỗi khi thêm sinh viên vào lớp");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsSubmitting(false);
    }
  }, [selectedStudentIds, classId, onSuccess, onClose]);

  // Handle dialog close
  const handleClose = useCallback(() => {
    if (!isSubmitting) {
      setSelectedStudentIds(new Set());
      setSearchTerm("");
      setError(null);
      onClose();
    }
  }, [isSubmitting, onClose]);

  // Filter students based on search term
  const filteredStudents = students.filter(student =>
    student.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.code_student.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            Thêm sinh viên vào lớp: {className}
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-hidden flex flex-col space-y-4">
          {/* Search */}
          <div className="space-y-2">
            <Label>Tìm kiếm sinh viên</Label>
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Tìm theo tên hoặc mã sinh viên..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Error display */}
          {error && (
            <Alert className="border-red-200 bg-red-50">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-700">{error}</AlertDescription>
            </Alert>
          )}

          {/* Selection summary */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Checkbox
                checked={selectedStudentIds.size === filteredStudents.length && filteredStudents.length > 0}
                onCheckedChange={handleSelectAll}
                disabled={isLoading || filteredStudents.length === 0}
              />
              <Label>Chọn tất cả ({filteredStudents.length})</Label>
            </div>
            <Badge variant="outline">
              Đã chọn: {selectedStudentIds.size}
            </Badge>
          </div>

          {/* Student list */}
          <div className="flex-1 overflow-y-auto space-y-2">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-center">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2 text-blue-600" />
                  <p className="text-gray-500">Đang tải danh sách sinh viên...</p>
                </div>
              </div>
            ) : filteredStudents.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Users className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p className="text-lg font-medium mb-2">Không tìm thấy sinh viên</p>
                <p className="text-sm">Thử thay đổi từ khóa tìm kiếm</p>
              </div>
            ) : (
              filteredStudents.map((student) => (
                <Card key={student.id} className="hover:bg-gray-50 transition-colors">
                  <CardContent className="p-3">
                    <div className="flex items-center gap-3">
                      <Checkbox
                        checked={selectedStudentIds.has(student.id)}
                        onCheckedChange={() => handleStudentToggle(student.id)}
                      />
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        {student.avatar ? (
                          <img
                            src={student.avatar}
                            alt={student.full_name}
                            className="w-10 h-10 rounded-full object-cover"
                          />
                        ) : (
                          <User className="h-5 w-5 text-blue-600" />
                        )}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium">{student.full_name}</h4>
                        <p className="text-sm text-gray-500">
                          {student.code_student} • {student.phone}
                        </p>
                      </div>
                      <Badge variant={student.status === 'active' ? 'default' : 'secondary'}>
                        {student.status === 'active' ? 'Hoạt động' : 'Không hoạt động'}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button variant="outline" onClick={handleClose} disabled={isSubmitting}>
              Hủy
            </Button>
            <Button 
              onClick={handleSubmit} 
              disabled={selectedStudentIds.size === 0 || isSubmitting}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Đang thêm...
                </>
              ) : (
                <>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Thêm {selectedStudentIds.size} sinh viên
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
});

AddStudentsDialog.displayName = 'AddStudentsDialog';
