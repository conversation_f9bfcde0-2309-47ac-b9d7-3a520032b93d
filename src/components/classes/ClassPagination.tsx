"use client";

import { memo, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from "lucide-react";
import { ClassPaginationData } from "@/api/class";

interface ClassPaginationProps {
  pagination: ClassPaginationData;
  onPageChange: (page: number) => void;
}

export const ClassPagination = memo(({
  pagination,
  onPageChange,
}: ClassPaginationProps) => {
  const { page, totalData, totalPage } = pagination;
  const limit = 20; // Fixed page size

  // Memoize page numbers calculation to prevent unnecessary recalculations
  // This must be called before any early returns to follow Rules of Hooks
  const pageNumbers = useMemo(() => {
    const pages = [];
    const maxPagesToShow = 5;

    if (totalPage <= maxPagesToShow) {
      // Show all pages if total pages is small
      for (let i = 1; i <= totalPage; i++) {
        pages.push(i);
      }
    } else {
      // Show pages around current page
      const startPage = Math.max(1, page - 2);
      const endPage = Math.min(totalPage, page + 2);

      if (startPage > 1) {
        pages.push(1);
        if (startPage > 2) {
          pages.push('...');
        }
      }

      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }

      if (endPage < totalPage) {
        if (endPage < totalPage - 1) {
          pages.push('...');
        }
        pages.push(totalPage);
      }
    }

    return pages;
  }, [page, totalPage]);

  // Only show pagination if there are more than 20 items
  if (totalData <= 20) {
    return null;
  }

  // Calculate the range of items being displayed
  const startItem = (page - 1) * limit + 1;
  const endItem = Math.min(page * limit, totalData);

  return (
    <Card>
      <CardContent className="py-4">
        <div className="flex items-center justify-between">
          {/* Page info */}
          <div className="text-sm text-gray-600">
            Hiển thị {startItem} - {endItem} trong tổng số {totalData} lớp học
          </div>

          {/* Empty div for spacing */}
          <div></div>

          {/* Pagination controls */}
          <div className="flex items-center gap-1">
            {/* First page */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(1)}
              disabled={page === 1}
              className="h-8 w-8 p-0"
              title="Trang đầu"
            >
              <ChevronsLeft className="h-4 w-4" />
            </Button>

            {/* Previous page */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(page - 1)}
              disabled={page === 1}
              className="h-8 w-8 p-0"
              title="Trang trước"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            {/* Page numbers */}
            {pageNumbers.map((pageNum, index) => (
              <Button
                key={index}
                variant={pageNum === page ? "default" : "outline"}
                size="sm"
                onClick={() => typeof pageNum === 'number' && onPageChange(pageNum)}
                disabled={pageNum === '...'}
                className="h-8 min-w-8 px-2"
              >
                {pageNum}
              </Button>
            ))}

            {/* Next page */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(page + 1)}
              disabled={page === totalPage}
              className="h-8 w-8 p-0"
              title="Trang sau"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>

            {/* Last page */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(totalPage)}
              disabled={page === totalPage}
              className="h-8 w-8 p-0"
              title="Trang cuối"
            >
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

ClassPagination.displayName = 'ClassPagination';
