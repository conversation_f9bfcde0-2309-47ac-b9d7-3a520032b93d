'use client';

import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useMutation } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useDispatch } from 'react-redux';
import { setUserAndToken } from '@/redux/features/user/userSlice';
import { loginSchoolAPI, } from '@/api/auth';
import { useState } from 'react';
import { User } from '@/types/auth';
import Link from 'next/link';

// Định nghĩa Zod schema để validate input
const loginSchema = z.object({
  phone: z.string()
    .min(10, 'Số điện thoại phải có ít nhất 10 ký tự.')
    .regex(/^[0-9]+$/, 'Số điện thoại chỉ được chứa chữ số.'),
  password: z.string()
    .min(6, '<PERSON><PERSON><PERSON> khẩu phải có ít nhất 6 ký tự.'),
  rememberMe: z.boolean().optional(),
});

// Ableitung des Typings aus dem Schema
type LoginFormInputs = z.infer<typeof loginSchema>;

export default function LoginForm() {
  const router = useRouter();
  const dispatch = useDispatch();
  const [apiError, setApiError] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting: isFormSubmitting }, // isSubmitting từ react-hook-form
  } = useForm<LoginFormInputs>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      phone: '',
      password: '',
      rememberMe: false,
    },
  });

  const loginMutation = useMutation<
    { code: number, data: User, mess: string }, // Kiểu dữ liệu trả về khi thành công
    Error,                                   // Kiểu dữ liệu lỗi
    LoginFormInputs                          // Kiểu dữ liệu của biến đầu vào cho mutationFn
  >({
    mutationFn: (credentials) => loginSchoolAPI({ phone: credentials.phone, password: credentials.password }),
    onSuccess: (response) => {
        if (response.code === 1 && response.data) {
          // Đăng nhập thành công
          dispatch(setUserAndToken({ user: response.data, accessToken: response.data.token }));
          router.push('/school');
        } else {
          // Các trường hợp code khác (0, 2, 3, 4) -> hiển thị lỗi từ API
          setApiError(response.mess || 'Đăng nhập không thành công.');
        }
      },
    onError: (error: any) => {
      // Xử lý lỗi từ API (hoặc lỗi mạng)
      const message = error.response?.data?.message || error.message || 'Đăng nhập thất bại. Vui lòng thử lại.';
      setApiError(message);
    }
  });

  const onSubmit: SubmitHandler<LoginFormInputs> = (data) => {
    setApiError(null); // Xóa lỗi API cũ trước khi thử lại
    loginMutation.mutate(data);
  };
  
  // Kết hợp isFormSubmitting (từ react-hook-form) và loginMutation.isPending (từ react-query)
  const isLoading = isFormSubmitting || loginMutation.isPending;

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {apiError && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
          <span className="block sm:inline">{apiError}</span>
        </div>
      )}

      <div>
        <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
          Số điện thoại
        </label>
        <input
          id="phone"
          type="tel"
          autoComplete="tel"
          {...register('phone')}
          className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none sm:text-sm ${
            errors.phone ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
          }`}
          placeholder="Nhập số điện thoại"
          disabled={isLoading}
        />
        {errors.phone && <p className="mt-1 text-sm text-red-600">{errors.phone.message}</p>}
      </div>

      <div>
        <label htmlFor="password"className="block text-sm font-medium text-gray-700">
          Mật khẩu
        </label>
        <div className="relative mt-1">
    <input
      id="password"
      type={showPassword ? "text" : "password"}
      autoComplete="current-password"
      {...register('password')}
      className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none sm:text-sm ${
        errors.password ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
      }`}
      placeholder="Nhập mật khẩu"
      disabled={isLoading}
    />
    <button
      type="button"
      className="absolute inset-y-0 right-0 pr-3 flex items-center"
      onClick={() => setShowPassword(!showPassword)}
      disabled={isLoading}
    >
      {showPassword ? (
        <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
        </svg>
      ) : (
        <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
        </svg>
      )}
    </button>
  </div>
        {errors.password && <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>}
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <input
            id="rememberMe"
            type="checkbox"
            {...register('rememberMe')}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            disabled={isLoading}
          />
          <label htmlFor="rememberMe" className="ml-2 block text-sm text-gray-900">
            Ghi nhớ đăng nhập
          </label>
        </div>
        {/* Bạn có thể thêm link "Quên mật khẩu?" ở đây nếu cần */}
        <div className="text-sm">
          <Link href="/ForgotPassword" className="font-medium text-blue-600 hover:text-blue-500">
            Quên mật khẩu?
          </Link>
        </div>
      </div>

      <div>
        <button
          type="submit"
          disabled={isLoading}
          className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-70 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <span className="flex items-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Đang xử lý...
            </span>
          ) : (
            'Đăng nhập'
          )}
        </button>
      </div>
    </form>
  );
}