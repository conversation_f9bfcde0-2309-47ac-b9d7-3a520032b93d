"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Room, RoomFormData } from "@/api/classroom";
import { Building } from "@/api/building";
import { Floor } from "@/api/floor";

interface ClassroomFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  editingRoom: Room | null;
  buildings: Building[];
  floors: Floor[];
  onSubmit: (formData: RoomFormData) => Promise<void>;
  isSubmitting: boolean;
}

export const ClassroomFormDialog = ({
  isOpen,
  onClose,
  editingRoom,
  buildings,
  floors,
  onSubmit,
  isSubmitting,
}: ClassroomFormDialogProps) => {
  // Form state
  const [formData, setFormData] = useState<RoomFormData>({
    name: "",
    floor_id: 0,
    school_id: 1, // Default school ID
  });
  const [selectedBuildingId, setSelectedBuildingId] = useState<string>("");
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Initialize form data when dialog opens or editing room changes
  useEffect(() => {
    if (isOpen) {
      if (editingRoom) {
        setFormData({
          name: editingRoom.name,
          floor_id: editingRoom.floor_info?.id || 0,
          school_id: editingRoom.school_id,
        });
        // Find the building for this floor
        const floor = floors.find(f => f?.id === editingRoom.floor_info?.id);
        if (floor && floor.building_info?.id) {
          setSelectedBuildingId(floor.building_info.id.toString());
        }
      } else {
        setFormData({
          name: "",
          floor_id: 0,
          school_id: 1,
        });
        setSelectedBuildingId("all");
      }
      setValidationErrors({});
    }
  }, [isOpen, editingRoom, floors]);

  // Filter floors based on selected building
  const filteredFloors = (selectedBuildingId && selectedBuildingId !== "all")
    ? floors.filter(floor => floor?.building_info?.id.toString() === selectedBuildingId)
    : floors;

  // Form validation
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = "Tên phòng học là bắt buộc";
    } else if (formData.name.trim().length < 2) {
      errors.name = "Tên phòng học phải có ít nhất 2 ký tự";
    } else if (formData.name.trim().length > 100) {
      errors.name = "Tên phòng học không được vượt quá 100 ký tự";
    }

    if (!selectedBuildingId || selectedBuildingId === "all") {
      errors.building_id = "Vui lòng chọn tòa nhà";
    }

    if (!formData.floor_id || formData.floor_id === 0) {
      errors.floor_id = "Vui lòng chọn tầng";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Form handlers
  const handleInputChange = (field: keyof RoomFormData, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleBuildingChange = (buildingId: string) => {
    setSelectedBuildingId(buildingId);
    // Reset floor selection when building changes
    setFormData(prev => ({ ...prev, floor_id: 0 }));
    // Clear building validation error
    if (validationErrors.building_id) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.building_id;
        return newErrors;
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    await onSubmit(formData);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[400px]">
        <DialogHeader>
          <DialogTitle>
            {editingRoom ? "Chỉnh sửa phòng học" : "Thêm phòng học mới"}
          </DialogTitle>
          <DialogDescription>
            {editingRoom 
              ? "Cập nhật thông tin phòng học dưới đây." 
              : "Nhập thông tin phòng học mới dưới đây."
            }
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Tên phòng học *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              placeholder="Nhập tên phòng học"
              className={validationErrors.name ? "border-red-500" : ""}
              disabled={isSubmitting}
            />
            {validationErrors.name && (
              <p className="text-sm text-red-600">{validationErrors.name}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="building">Tòa nhà *</Label>
            <Select
              value={selectedBuildingId}
              onValueChange={handleBuildingChange}
              disabled={isSubmitting}
            >
              <SelectTrigger className={validationErrors.building_id ? "border-red-500" : ""}>
                <SelectValue placeholder="Chọn tòa nhà" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Chọn tòa nhà</SelectItem>
                {buildings.map((building) => (
                  <SelectItem key={building.id} value={building.id.toString()}>
                    {building.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {validationErrors.building_id && (
              <p className="text-sm text-red-600">{validationErrors.building_id}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="floor">Tầng *</Label>
            <Select
              value={formData.floor_id.toString()}
              onValueChange={(value) => handleInputChange("floor_id", parseInt(value))}
              disabled={isSubmitting || !selectedBuildingId || selectedBuildingId === "all"}
            >
              <SelectTrigger className={validationErrors.floor_id ? "border-red-500" : ""}>
                <SelectValue placeholder="Chọn tầng" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0">Chọn tầng</SelectItem>
                {filteredFloors.map((floor) => (
                  <SelectItem key={floor.id} value={floor.id.toString()}>
                    {floor.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {validationErrors.floor_id && (
              <p className="text-sm text-red-600">{validationErrors.floor_id}</p>
            )}
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
              Hủy
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Đang lưu...
                </>
              ) : (
                editingRoom ? "Cập nhật" : "Thêm mới"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
