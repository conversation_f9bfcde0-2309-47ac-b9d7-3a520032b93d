"use client";

import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  DoorO<PERSON>, 
  Edit, 
  Trash2, 
  MoreHorizontal,
  ChevronDown,
  ChevronRight,
  Building2,
  Layers,
  Users
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Room, Class, FloorInfo, BuildingInfo } from "@/api/classroom";

interface ClassroomTableProps {
  rooms: Room[];
  roomDetails: Record<number, { 
    floor_info: FloorInfo; 
    building_info: BuildingInfo; 
    classes: Class[] 
  }>;
  onEdit: (room: Room) => void;
  onDelete: (room: Room) => void;
  onLoadRoomDetails: (roomId: number) => void;
  isLoading?: boolean;
  totalCount: number;
}

export const ClassroomTable = ({
  rooms,
  roomDetails,
  onEdit,
  onDelete,
  onLoadRoomDetails,
  isLoading = false,
  totalCount,
}: ClassroomTableProps) => {
  const [expandedRooms, setExpandedRooms] = useState<Set<number>>(new Set());

  const toggleRoomExpansion = (roomId: number) => {
    const newExpanded = new Set(expandedRooms);
    if (newExpanded.has(roomId)) {
      newExpanded.delete(roomId);
    } else {
      newExpanded.add(roomId);
      // Load room details if not already loaded
      if (!roomDetails[roomId]) {
        onLoadRoomDetails(roomId);
      }
    }
    setExpandedRooms(newExpanded);
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center text-gray-500">
            Đang tải dữ liệu...
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">
          Danh sách phòng học ({totalCount})
        </CardTitle>
      </CardHeader>
      <CardContent>
        {rooms.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            Không có phòng học nào được tìm thấy
          </div>
        ) : (
          <div className="space-y-2">
            {rooms.map((room) => (
              <Collapsible
                key={room.id}
                open={expandedRooms.has(room.id)}
                onOpenChange={() => toggleRoomExpansion(room.id)}
              >
                <div className="border rounded-lg">
                  <div className="flex items-center justify-between p-4 hover:bg-gray-50">
                    <div className="flex items-center gap-4 flex-1">
                      <CollapsibleTrigger asChild>
                        <Button variant="ghost" size="sm" className="p-0 h-auto">
                          {expandedRooms.has(room.id) ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                        </Button>
                      </CollapsibleTrigger>
                      
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center">
                          <DoorOpen className="h-5 w-5 text-purple-600" />
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900">{room.name}</h3>
                          <p className="text-sm text-gray-500">ID: {room.id}</p>
                          {roomDetails[room.id] && (
                            <div className="flex items-center gap-2 text-sm">
                              <span className="text-blue-600 flex items-center gap-1">
                                <Building2 className="h-3 w-3" />
                                {roomDetails[room.id].building_info.name}
                              </span>
                              <span className="text-gray-400">•</span>
                              <span className="text-green-600 flex items-center gap-1">
                                <Layers className="h-3 w-3" />
                                {roomDetails[room.id].floor_info.name}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <div className="ml-auto flex items-center gap-2">
                        <Badge 
                          variant={room.status === "active" ? "default" : "secondary"}
                        >
                          {room.status === "active" ? "Hoạt động" : "Không hoạt động"}
                        </Badge>
                        <Badge variant="outline">
                          {roomDetails[room.id]?.classes?.length || 0} lớp học
                        </Badge>
                      </div>
                    </div>
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Mở menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => onEdit(room)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Chỉnh sửa
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => onDelete(room)}
                          className="text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Xóa
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  
                  <CollapsibleContent>
                    <div className="border-t bg-gray-50 p-4">
                      <h4 className="font-medium text-gray-900 mb-3">Danh sách lớp học</h4>
                      {roomDetails[room.id] ? (
                        roomDetails[room.id].classes.length > 0 ? (
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                            {roomDetails[room.id].classes.map((classItem) => (
                              <div key={classItem.id} className="flex items-center gap-3 p-3 bg-white rounded-lg border">
                                <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                                  <Users className="h-4 w-4 text-gray-600" />
                                </div>
                                <div className="flex-1 min-w-0">
                                  <p className="font-medium text-sm text-gray-900 truncate">{classItem.name}</p>
                                  <p className="text-xs text-gray-500">ID: {classItem.id}</p>
                                </div>
                                <Badge 
                                  variant="default"
                                  className="text-xs"
                                >
                                  Hoạt động
                                </Badge>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <p className="text-gray-500 text-sm">Chưa có lớp học nào trong phòng này</p>
                        )
                      ) : (
                        <div className="text-center py-4">
                          <div className="text-gray-500 text-sm">Đang tải danh sách lớp học...</div>
                        </div>
                      )}
                    </div>
                  </CollapsibleContent>
                </div>
              </Collapsible>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
