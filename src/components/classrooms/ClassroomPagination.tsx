"use client";

import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { PaginationMetaOptional } from "@/api/classroom";

interface ClassroomPaginationProps {
  pagination: PaginationMetaOptional;
  onPageChange: (page: number) => void;
  onLimitChange: (limit: number) => void;
}

export const ClassroomPagination = ({
  pagination,
  onPageChange,
  onLimitChange,
}: ClassroomPaginationProps) => {
  // Add null checks and default values
  if (!pagination || (pagination.totalPage || 0) <= 1) {
    return null;
  }

  return (
    <div className="space-y-4">
      {/* Page Size Selector */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-500">
          <PERSON><PERSON><PERSON> thị {((pagination?.page || 1) - 1) * (pagination?.limit || 10) + 1} đến{' '}
          {Math.min((pagination?.page || 1) * (pagination?.limit || 10), pagination?.totalData || 0)} trong tổng số{' '}
          {pagination?.totalData || 0} phòng học
        </div>
        <div className="flex items-center gap-2">
          <Label htmlFor="page-size">Hiển thị:</Label>
          <Select 
            value={pagination?.limit?.toString() || "10"} 
            onValueChange={(value) => onLimitChange(parseInt(value))}
          >
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="20">20</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Pagination Controls */}
      <Pagination>
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious 
              onClick={() => onPageChange((pagination?.page || 1) - 1)}
              className={(pagination?.page || 1) <= 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
            />
          </PaginationItem>
          
          {Array.from({ length: pagination?.totalPage || 1 }, (_, i) => i + 1)
            .filter(page => {
              const current = pagination?.page || 1;
              const totalPage = pagination?.totalPage || 1;
              return page === 1 || page === totalPage || 
                     (page >= current - 1 && page <= current + 1);
            })
            .map((page, index, array) => (
              <PaginationItem key={page}>
                {index > 0 && array[index - 1] !== page - 1 && (
                  <span className="px-2">...</span>
                )}
                <PaginationLink
                  onClick={() => onPageChange(page)}
                  isActive={page === (pagination?.page || 1)}
                  className="cursor-pointer"
                >
                  {page}
                </PaginationLink>
              </PaginationItem>
            ))}
          
          <PaginationItem>
            <PaginationNext 
              onClick={() => onPageChange((pagination?.page || 1) + 1)}
              className={(pagination?.page || 1) >= (pagination?.totalPage || 1) ? "pointer-events-none opacity-50" : "cursor-pointer"}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  );
};
