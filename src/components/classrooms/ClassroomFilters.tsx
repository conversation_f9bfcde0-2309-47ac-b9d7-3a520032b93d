"use client";

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Search } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Building } from "@/api/building";
import { Floor } from "@/api/floor";

interface ClassroomFiltersProps {
  searchName: string;
  selectedBuildingId: string;
  selectedFloorId: string;
  buildings: Building[];
  floors: Floor[];
  onSearchChange: (field: 'name', value: string) => void;
  onBuildingChange: (buildingId: string) => void;
  onFloorChange: (floorId: string) => void;
  onSearch: () => void;
  onClearFilters: () => void;
}

export const ClassroomFilters = ({
  searchName,
  selectedBuildingId,
  selectedFloorId,
  buildings,
  floors,
  onSearchChange,
  onBuildingChange,
  onFloorChange,
  onSearch,
  onClearFilters,
}: ClassroomFiltersProps) => {
  // Filter floors based on selected building
  const filteredFloors = (selectedBuildingId && selectedBuildingId !== "all")
    ? floors.filter(floor => floor.building_id.toString() === selectedBuildingId)
    : floors;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Tìm kiếm và lọc</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="space-y-2">
            <Label htmlFor="search-name">Tên phòng học</Label>
            <Input
              id="search-name"
              placeholder="Nhập tên phòng học..."
              value={searchName}
              onChange={(e) => onSearchChange('name', e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="building-select">Tòa nhà</Label>
            <Select value={selectedBuildingId} onValueChange={onBuildingChange}>
              <SelectTrigger>
                <SelectValue placeholder="Chọn tòa nhà" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả tòa nhà</SelectItem>
                {buildings.map((building) => (
                  <SelectItem key={building.id} value={building.id.toString()}>
                    {building.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="floor-select">Tầng</Label>
            <Select value={selectedFloorId} onValueChange={onFloorChange}>
              <SelectTrigger>
                <SelectValue placeholder="Chọn tầng" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả tầng</SelectItem>
                {filteredFloors.map((floor) => (
                  <SelectItem key={floor.id} value={floor.id.toString()}>
                    {floor.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex items-end gap-2">
            <Button onClick={onSearch} className="flex-1">
              <Search className="h-4 w-4 mr-2" />
              Tìm kiếm
            </Button>
            <Button onClick={onClearFilters} variant="outline">
              Xóa bộ lọc
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
