"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Search } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface TeacherFiltersProps {
  searchName: string;
  searchEmail: string;
  searchPhone: string;
  filterGender: string;
  filterStatus: string;
  onSearchChange: (field: 'name' | 'email' | 'phone', value: string) => void;
  onFilterChange: (field: 'gender' | 'status', value: string) => void;
  onSearch: () => void;
  onClearFilters: () => void;
}

export const TeacherFilters = ({
  searchName,
  searchEmail,
  searchPhone,
  filterGender,
  filterStatus,
  onSearchChange,
  onFilterChange,
  onSearch,
  onClearFilters,
}: TeacherFiltersProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Tìm kiếm và lọc</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="space-y-2">
            <Label htmlFor="search-name">Tên giáo viên</Label>
            <Input
              id="search-name"
              placeholder="Nhập tên giáo viên..."
              value={searchName}
              onChange={(e) => onSearchChange('name', e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="search-email">Email</Label>
            <Input
              id="search-email"
              placeholder="Nhập email..."
              value={searchEmail}
              onChange={(e) => onSearchChange('email', e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="search-phone">Số điện thoại</Label>
            <Input
              id="search-phone"
              placeholder="Nhập số điện thoại..."
              value={searchPhone}
              onChange={(e) => onSearchChange('phone', e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="filter-gender">Giới tính</Label>
            <Select value={filterGender} onValueChange={(value) => onFilterChange('gender', value)}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Chọn giới tính" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả</SelectItem>
                <SelectItem value="male">Nam</SelectItem>
                <SelectItem value="female">Nữ</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
          <div className="space-y-2">
            <Label htmlFor="filter-status">Trạng thái</Label>
            <Select value={filterStatus} onValueChange={(value) => onFilterChange('status', value)}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Chọn trạng thái" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả</SelectItem>
                <SelectItem value="active">Hoạt động</SelectItem>
                <SelectItem value="inactive">Không hoạt động</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex items-end gap-2">
            <Button onClick={onSearch} className="flex-1">
              <Search className="h-4 w-4 mr-2" />
              Tìm kiếm
            </Button>
            <Button onClick={onClearFilters} variant="outline">
              Xóa bộ lọc
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
