export interface AccountInfo {
  id: string;
  name: string;
  avatar?: string;
  phone: string;
  email: string;
  status: string;
  school_id: string;
  created_at: string;
  last_login: string;
  grant_permission: string[];
}

export interface UpdateAccountData {
  name: string;
  email: string;
  phone: string;
  password?: string;
}

export interface AccountFormData {
  name: string;
  email: string;
  phone: string;
}

export interface ChangePasswordData {
  current_password: string;
  new_password: string;
  password_confirmation: string;
}

export interface PasswordFormData {
  current_password: string;
  new_password: string;
  password_confirmation: string;
}

export interface ContractInfo {
  school_id: string;
  school_name: string;
  contract_expiry_date: string;
  days_remaining: number;
  status: 'active' | 'expired' | 'expiring_soon' | string;
}
