export interface LoginCredentials {
  phone: string;
  password: string;
}

export interface User {
  id: number;
  full_name: string;
  name: string;
  avatar: string;
  image: string;
  phone: string;
  email: string;
  password: string;
  status: "active" | "inactive" | string;
  created_at: number; // Unix timestamp
  code_otp: string | null;
  address: string;
  token: string;
  token_device: string;
  birthday: string; // định dạng: "dd/mm/yyyy"
  birthplace: string;
  gender: "Nam" | "Nữ" | string;
  ethnicity: string;
  id_card_number: string;
  last_login: number; // Unix timestamp
  type: string | null;
  permission: string; // JSON string dạng: '["x", "y"]' – cần parse nếu dùng
  id_position: number;
  grant_permission: number; // 0 hoặc 1
  logo: string;
}
