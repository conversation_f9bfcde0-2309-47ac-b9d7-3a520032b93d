"use client";

import { useState, useEffect, useCallback } from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle, CheckCircle } from "lucide-react";

// Import API functions and types
import {
  listFaceRecognitionLogsAPI,
  FaceRecognitionLog,
  FaceRecognitionFilters,
  FaceRecognitionPaginationData,
} from "@/api/faceRecognition";
import { listClassAPI, Class } from "@/api/class";
import { listCameraAPI, Camera } from "@/api/camera";

// Import components
import { HistoryFilters } from "@/components/history/HistoryFilters";
import { HistoryTable } from "@/components/history/HistoryTable";
import { HistoryPagination } from "@/components/history/HistoryPagination";

// Import auth utility
import { getAuthToken } from "@/utils/getAuthToken";

export default function HistoryPage() {
  // State management
  const [logs, setLogs] = useState<FaceRecognitionLog[]>([]);
  const [classes, setClasses] = useState<Class[]>([]);
  const [cameras, setCameras] = useState<Camera[]>([]);
  const [pagination, setPagination] = useState<FaceRecognitionPaginationData>({
    page: 1,
    limit: 20,
    totalData: 0,
    totalPage: 0
  });

  // Filter state
  const [filters, setFilters] = useState<FaceRecognitionFilters>({
    page: 1,
    limit: 20
  });

  // Loading and error states
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingInitial, setIsLoadingInitial] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Load initial data (classes and cameras)
  const loadInitialData = useCallback(async () => {
    try {
      setIsLoadingInitial(true);
      setError(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      // Load classes and cameras in parallel
      const [classResponse, cameraResponse] = await Promise.all([
        listClassAPI({ token }),
        listCameraAPI({ token })
      ]);

      if (classResponse.code === 1) {
        setClasses(classResponse.data);
      }

      if (cameraResponse.code === 1) {
        setCameras(cameraResponse.data);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi khi tải dữ liệu ban đầu");
    } finally {
      setIsLoadingInitial(false);
    }
  }, []);

  // Load face recognition logs
  const loadLogs = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await listFaceRecognitionLogsAPI(token, filters);

      if (response.code === 1) {
        setLogs(response.data);
        setPagination(response.totalData);
      } else {
        throw new Error(response.mess || "Lỗi khi tải lịch sử nhận diện");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
      setLogs([]);
    } finally {
      setIsLoading(false);
    }
  }, [filters]);

  // Load initial data on component mount
  useEffect(() => {
    loadInitialData();
  }, [loadInitialData]);

  // Load logs when filters change
  useEffect(() => {
    if (!isLoadingInitial) {
      loadLogs();
    }
  }, [loadLogs, isLoadingInitial]);

  // Handle filter changes
  const handleFiltersChange = useCallback((newFilters: FaceRecognitionFilters) => {
    setFilters({ ...newFilters, page: 1 }); // Reset to first page when filters change
  }, []);

  // Handle search
  const handleSearch = useCallback(() => {
    setFilters(prev => ({ ...prev, page: 1 }));
    loadLogs();
  }, [loadLogs]);

  // Handle clear filters
  const handleClearFilters = useCallback(() => {
    const clearedFilters: FaceRecognitionFilters = {
      page: 1,
      limit: filters.limit || 20
    };
    setFilters(clearedFilters);
  }, [filters.limit]);

  // Handle pagination
  const handlePageChange = useCallback((page: number) => {
    setFilters(prev => ({ ...prev, page }));
  }, []);

  const handleLimitChange = useCallback((limit: number) => {
    setFilters(prev => ({ ...prev, limit, page: 1 }));
  }, []);

  // Clear success message after 5 seconds
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => setSuccess(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [success]);

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-800">Lịch sử nhận diện khuôn mặt</h1>
        <p className="text-gray-600 mt-1">
          Theo dõi và quản lý lịch sử nhận diện khuôn mặt của học sinh
        </p>
      </div>

      {/* Error and Success Messages */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-700">{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-700">{success}</AlertDescription>
        </Alert>
      )}

      {/* Filters Component */}
      <HistoryFilters
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onSearch={handleSearch}
        onClearFilters={handleClearFilters}
        classes={classes}
        cameras={cameras}
        isLoading={isLoading || isLoadingInitial}
      />

      {/* History Table Component */}
      <HistoryTable
        logs={logs}
        isLoading={isLoading || isLoadingInitial}
        totalCount={pagination.totalData}
      />

      {/* Pagination Component */}
      {!isLoading && !isLoadingInitial && logs.length > 0 && (
        <HistoryPagination
          pagination={pagination}
          onPageChange={handlePageChange}
          onLimitChange={handleLimitChange}
          isLoading={isLoading}
        />
      )}
    </div>
  );
}
