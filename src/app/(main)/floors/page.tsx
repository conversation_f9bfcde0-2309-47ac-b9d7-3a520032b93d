"use client";

import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Layers, 
  Plus, 
  Loader2, 
  AlertTriangle, 
  CheckCircle
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  listFloorAPI, 
  detailFloorAPI, 
  addFloorAPI, 
  deleteFloorAPI,
  Floor,
  FloorFormData,
  PaginationMeta,
  Room,
  BuildingInfo
} from "@/api/floor";
import { listBuildingAPI, Building } from "@/api/building";
import { getAuthToken } from "@/utils/getAuthToken";

// Import the new components
import { FloorFilters } from "@/components/floors/FloorFilters";
import { FloorTable } from "@/components/floors/FloorTable";
import { FloorPagination } from "@/components/floors/FloorPagination";
import { FloorFormDialog } from "@/components/floors/FloorFormDialog";
import { FloorDeleteDialog } from "@/components/floors/FloorDeleteDialog";

export default function FloorManagementPage() {
  // State management
  const [floors, setFloors] = useState<Floor[]>([]);
  const [buildings, setBuildings] = useState<Building[]>([]);
  const [floorDetails, setFloorDetails] = useState<Record<number, { building_info: BuildingInfo; rooms: Room[] }>>({});
  const [pagination, setPagination] = useState<PaginationMeta>({
    page: 1,
    limit: 10,
    totalData: 0,
    totalPage: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Search and filter state
  const [searchName, setSearchName] = useState("");
  const [selectedBuildingId, setSelectedBuildingId] = useState("all");
  
  // Modal state
  const [isAddEditModalOpen, setIsAddEditModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [editingFloor, setEditingFloor] = useState<Floor | null>(null);
  const [deletingFloor, setDeletingFloor] = useState<Floor | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load floors and buildings on component mount
  useEffect(() => {
    fetchBuildings();
    fetchFloors();
  }, [pagination.page, pagination.limit]);

  const fetchBuildings = async () => {
    try {
      const token = getAuthToken();
      if (!token) return;

      const response = await listBuildingAPI({ 
        token,
        limit: 1000 // Get all buildings for dropdown
      });
      
      if (response.code === 1 && response.data) {
        setBuildings(response.data);
      }
    } catch (err) {
      console.error("Error loading buildings:", err);
    }
  };

  const fetchFloors = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await listFloorAPI({
        token,
        name: searchName || undefined,
        building_id: (selectedBuildingId && selectedBuildingId !== "all") ? parseInt(selectedBuildingId) : undefined,
        page: pagination.page,
        limit: pagination.limit
      });
      
      // Note: API returns code: 1 for success
      if (response.code === 1 && response.data) {
        setFloors(response.data);
        if (response.meta) {
          setPagination(response.meta);
        }
      } else {
        throw new Error(response.mess || "Không thể lấy danh sách tầng");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsLoading(false);
    }
  };

  // Load floor details for expanded floors
  const loadFloorDetails = async (floorId: number) => {
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await detailFloorAPI(token, floorId);
      
      if (response.code === 1 && response.data) {
        setFloorDetails(prev => ({
          ...prev,
          [floorId]: { 
            building_info: response.data.building_info,
            rooms: response.data.rooms 
          }
        }));
      }
    } catch (err) {
      console.error("Error loading floor details:", err);
    }
  };

  // Search functionality
  const handleSearch = () => {
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchFloors();
  };

  const clearSearch = () => {
    setSearchName("");
    setSelectedBuildingId("all");
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchFloors();
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  const handleLimitChange = (limit: number) => {
    setPagination(prev => ({ ...prev, limit, page: 1 }));
  };

  // Search and filter handlers
  const handleSearchChange = (_field: 'name', value: string) => {
    setSearchName(value);
  };

  const handleBuildingChange = (buildingId: string) => {
    setSelectedBuildingId(buildingId);
  };

  // Modal handlers
  const openAddModal = () => {
    setEditingFloor(null);
    setIsAddEditModalOpen(true);
  };

  const openEditModal = async (floor: Floor) => {
    try {
      setError(null);
      setEditingFloor(floor);
      setIsAddEditModalOpen(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    }
  };

  const closeModal = () => {
    setIsAddEditModalOpen(false);
    setEditingFloor(null);
  };

  const openDeleteDialog = (floor: Floor) => {
    setDeletingFloor(floor);
    setIsDeleteDialogOpen(true);
  };

  const closeDeleteDialog = () => {
    setIsDeleteDialogOpen(false);
    setDeletingFloor(null);
  };

  // Form submission handler for the dialog component
  const handleFormSubmit = async (formData: FloorFormData) => {
    try {
      setIsSubmitting(true);
      setError(null);
      setSuccess(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const submitData = {
        ...(editingFloor && { id: editingFloor.id }),
        name: formData.name,
        building_id: formData.building_id,
      };

      const response = await addFloorAPI(token, submitData);

      // Note: API returns code: 1 for success
      if (response.code === 1 && response.data) {
        setSuccess(editingFloor ? "Cập nhật tầng thành công!" : "Thêm tầng thành công!");
        closeModal();
        fetchFloors(); // Refresh the list
        loadFloorDetails(response.data.id);
      } else {
        throw new Error(response.mess || "Không thể lưu tầng");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async () => {
    if (!deletingFloor) return;

    try {
      setError(null);
      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await deleteFloorAPI(token, deletingFloor.id);

      // Note: API returns code: 1 for success
      if (response.code === 1) {
        setSuccess("Xóa tầng thành công!");
        closeDeleteDialog();
        fetchFloors(); // Refresh the list
      } else {
        throw new Error(response.mess || "Không thể xóa tầng");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
      closeDeleteDialog();
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-800 flex items-center gap-2">
            <Layers className="h-8 w-8 text-green-600" />
            Quản lý Tầng học
          </h1>
        </div>
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-green-600" />
            <span className="ml-2 text-gray-600">Đang tải dữ liệu...</span>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-800 flex items-center gap-2">
          <Layers className="h-8 w-8 text-green-600" />
          Quản lý Tầng học
        </h1>
        <Button onClick={openAddModal} className="bg-green-600 hover:bg-green-700">
          <Plus className="h-4 w-4 mr-2" />
          Thêm tầng
        </Button>
      </div>

      {/* Error/Success Messages */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-700">{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-700">{success}</AlertDescription>
        </Alert>
      )}

      {/* Floor Filters Component */}
      <FloorFilters
        searchName={searchName}
        selectedBuildingId={selectedBuildingId}
        buildings={buildings}
        onSearchChange={handleSearchChange}
        onBuildingChange={handleBuildingChange}
        onSearch={handleSearch}
        onClearFilters={clearSearch}
      />

      {/* Floor Table Component */}
      <FloorTable
        floors={floors}
        floorDetails={floorDetails}
        onEdit={openEditModal}
        onDelete={openDeleteDialog}
        onLoadFloorDetails={loadFloorDetails}
        isLoading={false}
        totalCount={pagination.totalData}
      />

      {/* Floor Pagination Component */}
      <FloorPagination
        pagination={pagination}
        onPageChange={handlePageChange}
        onLimitChange={handleLimitChange}
      />

      {/* Floor Form Dialog Component */}
      <FloorFormDialog
        isOpen={isAddEditModalOpen}
        onClose={closeModal}
        editingFloor={editingFloor}
        buildings={buildings}
        onSubmit={handleFormSubmit}
        isSubmitting={isSubmitting}
      />

      {/* Floor Delete Dialog Component */}
      <FloorDeleteDialog
        isOpen={isDeleteDialogOpen}
        onClose={closeDeleteDialog}
        floor={deletingFloor}
        onConfirm={handleDelete}
        isDeleting={false}
      />
    </div>
  );
}
