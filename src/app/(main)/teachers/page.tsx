
"use client";

import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  User,
  Plus,
  Loader2,
  AlertTriangle,
  CheckCircle
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  listTeacherAPI,
  detailsTeacherAPI,
  addTeacherAPI,
  deleteTeacherAPI,
  Teacher,
  TeacherFormData,
  PaginationMeta
} from "@/api/teacher";
import { getAuthToken } from "@/utils/getAuthToken";

// Import the new components
import { TeacherFilters } from "@/components/teachers/TeacherFilters";
import { TeacherTable } from "@/components/teachers/TeacherTable";
import { TeacherPagination } from "@/components/teachers/TeacherPagination";
import { TeacherFormDialog } from "@/components/teachers/TeacherFormDialog";
import { TeacherDeleteDialog } from "@/components/teachers/TeacherDeleteDialog";

export default function TeacherManagementPage() {
  // State management
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [pagination, setPagination] = useState<PaginationMeta>({
    page: 1,
    limit: 10,
    totalData: 0,
    totalPage: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Search and filter state
  const [searchName, setSearchName] = useState("");
  const [searchEmail, setSearchEmail] = useState("");
  const [searchPhone, setSearchPhone] = useState("");
  const [filterGender, setFilterGender] = useState("all");
  const [filterStatus, setFilterStatus] = useState("all");

  // Modal state
  const [isAddEditModalOpen, setIsAddEditModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [editingTeacher, setEditingTeacher] = useState<Teacher | null>(null);
  const [deletingTeacher, setDeletingTeacher] = useState<Teacher | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load teachers on component mount
  useEffect(() => {
    fetchTeachers();
  }, [pagination.page, pagination.limit]);

  const fetchTeachers = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await listTeacherAPI({
        token,
        name: searchName || undefined,
        email: searchEmail || undefined,
        phone: searchPhone || undefined,
        gender: filterGender === "all" ? undefined : filterGender || undefined,
        status: filterStatus === "all" ? undefined : filterStatus || undefined,
        page: pagination.page,
        limit: pagination.limit
      });

      if (response.code === 0 && response.data) {
        setTeachers(response.data);
        if (response.totalData) {
          setPagination(response.totalData);
        }
      } else {
        throw new Error(response.mess || "Không thể lấy danh sách giáo viên");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsLoading(false);
    }
  };

  // Search functionality
  const handleSearch = () => {
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchTeachers();
  };

  const clearSearch = () => {
    setSearchName("");
    setSearchEmail("");
    setSearchPhone("");
    setFilterGender("all");
    setFilterStatus("all");
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchTeachers();
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  const handleLimitChange = (limit: number) => {
    setPagination(prev => ({ ...prev, limit, page: 1 }));
  };

  // Search and filter handlers
  const handleSearchChange = (field: 'name' | 'email' | 'phone', value: string) => {
    switch (field) {
      case 'name':
        setSearchName(value);
        break;
      case 'email':
        setSearchEmail(value);
        break;
      case 'phone':
        setSearchPhone(value);
        break;
    }
  };

  const handleFilterChange = (field: 'gender' | 'status', value: string) => {
    switch (field) {
      case 'gender':
        setFilterGender(value);
        break;
      case 'status':
        setFilterStatus(value);
        break;
    }
  };

  // Modal handlers
  const openAddModal = () => {
    setEditingTeacher(null);
    setIsAddEditModalOpen(true);
  };

  const openEditModal = async (teacher: Teacher) => {
    try {
      setError(null);
      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      // Fetch detailed teacher data
      const response = await detailsTeacherAPI(token, teacher.id);

      if (response.code === 1 && response.data) {
        setEditingTeacher(response.data);
        setIsAddEditModalOpen(true);
      } else {
        throw new Error(response.mess || "Không thể lấy chi tiết giáo viên");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    }
  };

  const closeModal = () => {
    setIsAddEditModalOpen(false);
    setEditingTeacher(null);
  };

  const openDeleteDialog = (teacher: Teacher) => {
    setDeletingTeacher(teacher);
    setIsDeleteDialogOpen(true);
  };

  const closeDeleteDialog = () => {
    setIsDeleteDialogOpen(false);
    setDeletingTeacher(null);
  };

  // Form submission handler for the dialog component
  const handleFormSubmit = async (formData: TeacherFormData, avatarFile: File | null) => {
    try {
      setIsSubmitting(true);
      setError(null);
      setSuccess(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const submitData = {
        ...(editingTeacher && { id: editingTeacher.id }),
        name: formData.name,
        phone: formData.phone,
        email: formData.email,
        address: formData.address,
        gender: formData.gender,
        id_professional_team: formData.id_professional_team,
        status: formData.status,
        ...(formData.password && { password: formData.password }),
        ...(avatarFile && { avatar: avatarFile }),
      };

      const response = await addTeacherAPI(token, submitData);

      if (response.code === 0 && response.data) {
        setSuccess(editingTeacher ? "Cập nhật giáo viên thành công!" : "Thêm giáo viên thành công!");
        closeModal();
        fetchTeachers(); // Refresh the list
      } else {
        throw new Error(response.mess || "Không thể lưu giáo viên");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async () => {
    if (!deletingTeacher) return;

    try {
      setError(null);
      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await deleteTeacherAPI(token, deletingTeacher.id);

      if (response.code === 0) {
        setSuccess("Xóa giáo viên thành công!");
        closeDeleteDialog();
        fetchTeachers(); // Refresh the list
      } else {
        throw new Error(response.mess || "Không thể xóa giáo viên");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
      closeDeleteDialog();
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-800 flex items-center gap-2">
            <User className="h-8 w-8 text-blue-600" />
            Quản lý Giáo viên
          </h1>
        </div>
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">Đang tải dữ liệu...</span>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-800 flex items-center gap-2">
          <User className="h-8 w-8 text-blue-600" />
          Quản lý Giáo viên
        </h1>
        <Button onClick={openAddModal} className="bg-blue-600 hover:bg-blue-700">
          <Plus className="h-4 w-4 mr-2" />
          Thêm giáo viên
        </Button>
      </div>

      {/* Error/Success Messages */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-700">{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-700">{success}</AlertDescription>
        </Alert>
      )}

      {/* Search Filters Component */}
      <TeacherFilters
        searchName={searchName}
        searchEmail={searchEmail}
        searchPhone={searchPhone}
        filterGender={filterGender}
        filterStatus={filterStatus}
        onSearchChange={handleSearchChange}
        onFilterChange={handleFilterChange}
        onSearch={handleSearch}
        onClearFilters={clearSearch}
      />

      {/* Teacher Table Component */}
      <TeacherTable
        teachers={teachers}
        onEdit={openEditModal}
        onDelete={openDeleteDialog}
        isLoading={false}
        totalCount={pagination.totalData}
      />

      {/* Teacher Pagination Component */}
      <TeacherPagination
        pagination={pagination}
        onPageChange={handlePageChange}
        onLimitChange={handleLimitChange}
      />

      {/* Teacher Form Dialog Component */}
      <TeacherFormDialog
        isOpen={isAddEditModalOpen}
        onClose={closeModal}
        editingTeacher={editingTeacher}
        onSubmit={handleFormSubmit}
        isSubmitting={isSubmitting}
      />

      {/* Teacher Delete Dialog Component */}
      <TeacherDeleteDialog
        isOpen={isDeleteDialogOpen}
        onClose={closeDeleteDialog}
        teacher={deletingTeacher}
        onConfirm={handleDelete}
        isDeleting={false}
      />
    </div>
  );
}
