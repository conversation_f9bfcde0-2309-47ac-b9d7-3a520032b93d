import { Users, Camera, GraduationCap, User, Calendar, AlertTriangle } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function DashboardPage() {
  const stats = [
    { title: "Tổng học sinh", value: "1,247", icon: GraduationCap, color: "bg-blue-500" },
    { title: "Giáo viên", value: "89", icon: User, color: "bg-green-500" },
    { title: "Camera hoạt động", value: "24/26", icon: Camera, color: "bg-purple-500" },
    { title: "Lớ<PERSON> học", value: "32", icon: Users, color: "bg-orange-500" },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="lg:text-3xl text-xl font-bold text-gray-800">T<PERSON><PERSON> quan hệ thống</h1>
        <div className="text-right">
          <p className="text-sm text-gray-600"><PERSON>h<PERSON><PERSON> gian đăng nhập cuối: 15:30 - 05/07/2025</p>
          <p className="text-sm text-green-600 font-medium">Tài khoản hết hạn: 30/12/2025</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index} className="hover:shadow-lg transition-shadow duration-200">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                  <div className={`${stat.color} p-3 rounded-full`}>
                    <Icon className="h-6 w-6 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Hoạt động hôm nay
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <span className="text-sm">Học sinh có mặt</span>
              <span className="font-bold text-green-600">1,189/1,247</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <span className="text-sm">Giáo viên có mặt</span>
              <span className="font-bold text-blue-600">87/89</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
              <span className="text-sm">Camera hoạt động</span>
              <span className="font-bold text-purple-600">24/26</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Cảnh báo hệ thống
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-start gap-3 p-3 bg-yellow-50 rounded-lg">
              <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium">Camera sảnh A bị lỗi</p>
                <p className="text-xs text-gray-600">2 phút trước</p>
              </div>
            </div>
            <div className="flex items-start gap-3 p-3 bg-red-50 rounded-lg">
              <AlertTriangle className="h-4 w-4 text-red-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium">Dung lượng lưu trữ sắp đầy</p>
                <p className="text-xs text-gray-600">1 giờ trước</p>
              </div>
            </div>
            <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
              <AlertTriangle className="h-4 w-4 text-blue-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium">Cập nhật hệ thống khả dụng</p>
                <p className="text-xs text-gray-600">3 giờ trước</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
