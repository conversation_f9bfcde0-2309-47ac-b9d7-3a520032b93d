"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Camera, Search, Play, Eye, Video, Download, Scissors, Trash2, CheckCircle, AlertTriangle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { VideoPlayer } from "@/components/camera/VideoPlayer";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";

// Import camera API and types
import {
  listCameraAPI,
  detailCameraAPI,
  addCameraAPI,
  deleteCameraAPI,
  Camera as CameraType,
  CameraFormData,
  CameraPaginationData,
} from "@/api/camera";

// Import camera management components
import { CameraFilters } from "@/components/camera/CameraFilters";
import { CameraTable } from "@/components/camera/CameraTable";
import { CameraPagination } from "@/components/camera/CameraPagination";
import { CameraFormDialog } from "@/components/camera/CameraFormDialog";
import { CameraDeleteDialog } from "@/components/camera/CameraDeleteDialog";

export default function CameraManagementPage() {
  // Camera management state
  const [cameras, setCameras] = useState<CameraType[]>([]);
  const [pagination, setPagination] = useState<CameraPaginationData>({
    page: 1,
    limit: 20, // Fixed page size
    totalData: 0,
    totalPage: 0
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Search and filter state
  const [searchName, setSearchName] = useState("");
  const [searchMacAddress, setSearchMacAddress] = useState("");
  const [searchIpAddress, setSearchIpAddress] = useState("");
  const [generalSearch, setGeneralSearch] = useState("");
  const [filterBuildingId, setFilterBuildingId] = useState("");
  const [filterFloorId, setFilterFloorId] = useState("");
  const [filterRoomId, setFilterRoomId] = useState("");
  const [filterStatus, setFilterStatus] = useState("");

  // Modal state
  const [isAddEditModalOpen, setIsAddEditModalOpen] = useState(false);
  const [editingCamera, setEditingCamera] = useState<CameraType | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deletingCamera, setDeletingCamera] = useState<CameraType | null>(null);

  // Legacy video player state (keeping for existing functionality)
  const [activeView, setActiveView] = useState("list");
  const [selectedVideo, setSelectedVideo] = useState<any>(null);
  const [isVideoPlayerOpen, setIsVideoPlayerOpen] = useState(false);
  const [dateFilter, setDateFilter] = useState("");
  const [cameraFilter, setCameraFilter] = useState("all");

  // Load cameras on component mount and when page changes
  useEffect(() => {
    loadCameras();
  }, [pagination.page]);

  // API functions
  const loadCameras = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const params = {
        page: pagination.page,
        limit: 20, // Fixed page size
        ...(generalSearch && { search: generalSearch }),
        ...(searchName && { name: searchName }),
        ...(searchMacAddress && { mac_address: searchMacAddress }),
        ...(searchIpAddress && { ip_address: searchIpAddress }),
        ...(filterBuildingId && filterBuildingId !== "all" && { building_id: parseInt(filterBuildingId) }),
        ...(filterFloorId && filterFloorId !== "all" && { floor_id: parseInt(filterFloorId) }),
        ...(filterRoomId && filterRoomId !== "all" && { room_id: parseInt(filterRoomId) }),
        ...(filterStatus && filterStatus !== "all" && { status: filterStatus }),
      };

      const response = await listCameraAPI(params);

      if (response.code === 1) {
        setCameras(response.data);
        setPagination(response.totalData);
      } else {
        throw new Error(response.mess || "Lỗi khi tải danh sách camera");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
      setCameras([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Search and filter handlers
  const handleSearchChange = (field: string, value: string) => {
    switch (field) {
      case "search":
        setGeneralSearch(value);
        break;
      case "name":
        setSearchName(value);
        break;
      case "mac_address":
        setSearchMacAddress(value);
        break;
      case "ip_address":
        setSearchIpAddress(value);
        break;
      case "building_id":
        setFilterBuildingId(value);
        // Clear dependent filters when building changes
        if (value === "all" || !value) {
          setFilterFloorId("");
          setFilterRoomId("");
        }
        break;
      case "floor_id":
        setFilterFloorId(value);
        // Clear room filter when floor changes
        if (value === "all" || !value) {
          setFilterRoomId("");
        }
        break;
      case "room_id":
        setFilterRoomId(value);
        break;
      case "status":
        setFilterStatus(value);
        break;
    }
  };

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, page: 1 }));
    loadCameras();
  };

  const clearSearch = () => {
    setGeneralSearch("");
    setSearchName("");
    setSearchMacAddress("");
    setSearchIpAddress("");
    setFilterBuildingId("");
    setFilterFloorId("");
    setFilterRoomId("");
    setFilterStatus("");
    setPagination(prev => ({ ...prev, page: 1 }));
    setTimeout(() => loadCameras(), 100);
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  // CRUD operation handlers
  const openAddModal = () => {
    setEditingCamera(null);
    setIsAddEditModalOpen(true);
  };

  const openEditModal = (camera: CameraType) => {
    setEditingCamera(camera);
    setIsAddEditModalOpen(true);
  };

  const closeModal = () => {
    setIsAddEditModalOpen(false);
    setEditingCamera(null);
  };

  const handleFormSubmit = async (formData: CameraFormData) => {
    try {
      setIsSubmitting(true);
      setError(null);

      const cameraData = {
        ...formData,
        ...(editingCamera && { id: editingCamera.id }),
      };

      const response = await addCameraAPI(cameraData);

      if (response.code === 1) {
        setSuccess(editingCamera ? "Cập nhật camera thành công!" : "Thêm camera thành công!");
        closeModal();
        loadCameras();

        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(null), 3000);
      } else {
        throw new Error(response.mess || "Lỗi khi lưu camera");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsSubmitting(false);
    }
  };

  const openDeleteDialog = (camera: CameraType) => {
    setDeletingCamera(camera);
    setIsDeleteDialogOpen(true);
  };

  const closeDeleteDialog = () => {
    setIsDeleteDialogOpen(false);
    setDeletingCamera(null);
  };

  const handleDelete = async (camera: CameraType) => {
    try {
      setIsSubmitting(true);
      setError(null);

      const response = await deleteCameraAPI(camera.id);

      if (response.code === 1) {
        setSuccess("Xóa camera thành công!");
        closeDeleteDialog();
        loadCameras();

        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(null), 3000);
      } else {
        throw new Error(response.mess || "Lỗi khi xóa camera");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleViewCamera = async (camera: CameraType) => {
    try {
      const response = await detailCameraAPI(camera.id);
      if (response.code === 1) {
        // For now, just show an alert with camera details
        // In a real implementation, you might open a detailed view modal
        alert(`Chi tiết camera:\nTên: ${response.data.name}\nVị trí: ${response.data.location}\nMAC: ${response.data.mac_address}\nIP: ${response.data.ip_address}\nTrạng thái: ${response.data.status}`);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi khi tải chi tiết camera");
    }
  };

  // Mock data for video storage (keeping for existing functionality)
  const temporaryVideos = [
    { id: 1, name: "Recording_20240115_083012.mp4", camera: "Camera Sảnh A", date: "2024-01-15", time: "08:30:12", duration: "00:15:30", size: "245 MB", event: "Movement detected", daysLeft: 15 },
    { id: 2, name: "Recording_20240115_091545.mp4", camera: "Camera Lớp 10A1", date: "2024-01-15", time: "09:15:45", duration: "00:22:15", size: "330 MB", event: "Face recognition", daysLeft: 15 },
    { id: 3, name: "Recording_20240114_143020.mp4", camera: "Camera Cổng trường", date: "2024-01-14", time: "14:30:20", duration: "00:08:45", size: "125 MB", event: "Person entry", daysLeft: 14 },
    { id: 4, name: "Recording_20240114_161230.mp4", camera: "Camera Sân chơi", date: "2024-01-14", time: "16:12:30", duration: "00:35:20", size: "520 MB", event: "Activity detected", daysLeft: 14 },
    { id: 5, name: "Recording_20240113_102030.mp4", camera: "Camera Sảnh A", date: "2024-01-13", time: "10:20:30", duration: "00:12:00", size: "180 MB", event: "Movement detected", daysLeft: 13 },
  ];

  const filteredVideos = temporaryVideos.filter(video => {
    const matchesSearch = video.name.toLowerCase().includes(searchName.toLowerCase()) ||
                         video.camera.toLowerCase().includes(searchName.toLowerCase());
    const matchesDate = !dateFilter || video.date === dateFilter;
    const matchesCamera = cameraFilter === "all" || video.camera === cameraFilter;

    return matchesSearch && matchesDate && matchesCamera;
  });

  // Legacy video handlers (keeping for existing functionality)
  const handlePlayVideo = (video: any) => {
    setSelectedVideo(video);
    setIsVideoPlayerOpen(true);
  };

  const handleCutVideo = (videoId: number) => {
    console.log("Cutting video:", videoId);
    // Implement video cutting functionality
  };

  const handleDownloadVideo = (videoId: number) => {
    console.log("Downloading video:", videoId);
    // Implement download functionality
  };

  const handleSaveToLongTerm = (videoId: number) => {
    console.log("Saving to long-term storage:", videoId);
    // Implement save to long-term storage
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-800 flex items-center gap-2">
          <Camera className="h-8 w-8 text-blue-600" />
          Quản lý Camera AI
        </h1>
        <div className="flex gap-2">
          <Button
            variant={activeView === "list" ? "default" : "outline"}
            onClick={() => setActiveView("list")}
          >
            Danh sách
          </Button>
          <Button
            variant={activeView === "live" ? "default" : "outline"}
            onClick={() => setActiveView("live")}
          >
            Xem trực tiếp
          </Button>
          <Button
            variant={activeView === "temporary" ? "default" : "outline"}
            onClick={() => setActiveView("temporary")}
          >
            Lưu trữ tạm thời
          </Button>
          <Button
            variant={activeView === "storage" ? "default" : "outline"}
            onClick={() => setActiveView("storage")}
          >
            Kho lưu trữ
          </Button>
        </div>
      </div>

      {/* Error and Success Messages */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-700">{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-700">{success}</AlertDescription>
        </Alert>
      )}

      {activeView === "list" && (
        <>
          {/* Camera Filters Component */}
          <CameraFilters
            searchName={searchName}
            searchMacAddress={searchMacAddress}
            searchIpAddress={searchIpAddress}
            generalSearch={generalSearch}
            filterBuildingId={filterBuildingId}
            filterFloorId={filterFloorId}
            filterRoomId={filterRoomId}
            filterStatus={filterStatus}
            onSearchChange={handleSearchChange}
            onSearch={handleSearch}
            onClearFilters={clearSearch}
            onAddCamera={openAddModal}
          />

          {/* Camera Table Component */}
          <CameraTable
            cameras={cameras}
            onEdit={openEditModal}
            onDelete={openDeleteDialog}
            onView={handleViewCamera}
            isLoading={isLoading}
            totalCount={pagination.totalData}
          />

          {/* Camera Pagination Component */}
          <CameraPagination
            pagination={pagination}
            onPageChange={handlePageChange}
          />

          {/* Camera Form Dialog Component */}
          <CameraFormDialog
            isOpen={isAddEditModalOpen}
            onClose={closeModal}
            editingCamera={editingCamera}
            onSubmit={handleFormSubmit}
            isSubmitting={isSubmitting}
          />

          {/* Camera Delete Dialog Component */}
          <CameraDeleteDialog
            isOpen={isDeleteDialogOpen}
            onClose={closeDeleteDialog}
            camera={deletingCamera}
            onConfirm={handleDelete}
            isDeleting={isSubmitting}
          />
        </>
      )}

      {activeView === "live" && (
        <Card>
          <CardHeader>
            <CardTitle>Xem Camera Trực Tiếp</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {cameras.filter(c => c.status === "online").map((camera) => (
                <div key={camera.id} className="space-y-2">
                  <h3 className="font-medium">{camera.name}</h3>
                  <div className="bg-black rounded-lg h-48 flex items-center justify-center">
                    <div className="text-white text-center">
                      <Play className="h-12 w-12 mx-auto mb-2" />
                      <p>Live Stream - {camera.name}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {activeView === "temporary" && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Video className="h-5 w-5" />
                Thư viện Video Lưu trữ Tạm thời (30 ngày)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-4 flex-wrap">
                  <div className="relative flex-1 min-w-60">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Tìm kiếm video..."
                      value={searchName}
                      onChange={(e) => setSearchName(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <Input
                    type="date"
                    value={dateFilter}
                    onChange={(e) => setDateFilter(e.target.value)}
                  />
                  <Select value={cameraFilter} onValueChange={setCameraFilter}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="Chọn camera" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tất cả camera</SelectItem>
                      {cameras.map((camera) => (
                        <SelectItem key={camera.id} value={camera.name}>
                          {camera.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium">Dung lượng đã sử dụng</span>
                    <span className="font-bold">2.4TB / 5TB</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full" style={{ width: "48%" }}></div>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">Video sẽ tự động xóa sau 30 ngày hoặc khi hết dung lượng</p>
                </div>

                <div className="grid grid-cols-1 gap-4">
                  {filteredVideos.map((video) => (
                    <Card key={video.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <div className="bg-gray-100 rounded-lg p-3">
                              <Video className="h-8 w-8 text-gray-600" />
                            </div>
                            <div>
                              <h3 className="font-medium">{video.name}</h3>
                              <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                                <span>{video.camera}</span>
                                <span>{video.date} {video.time}</span>
                                <span>{video.duration}</span>
                                <span>{video.size}</span>
                              </div>
                              <div className="flex items-center gap-2 mt-1">
                                <Badge variant="secondary">{video.event}</Badge>
                                <Badge variant={video.daysLeft <= 7 ? "destructive" : "outline"}>
                                  {video.daysLeft} ngày còn lại
                                </Badge>
                              </div>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <Button size="sm" variant="outline" onClick={() => handlePlayVideo(video)}>
                              <Play className="h-4 w-4 mr-1" />
                              Phát
                            </Button>
                            <Button size="sm" variant="outline" onClick={() => handleCutVideo(video.id)}>
                              <Scissors className="h-4 w-4 mr-1" />
                              Cắt
                            </Button>
                            <Button size="sm" variant="outline" onClick={() => handleSaveToLongTerm(video.id)}>
                              <Download className="h-4 w-4 mr-1" />
                              Lưu lâu dài
                            </Button>
                            <Button size="sm" variant="outline">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {activeView === "storage" && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Lưu trữ tạm thời (30 ngày)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span>Dung lượng đã sử dụng</span>
                  <span className="font-bold">2.4TB / 5TB</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-600 h-2 rounded-full" style={{ width: "48%" }}></div>
                </div>
                <p className="text-sm text-gray-600">Video sẽ tự động xóa sau 30 ngày</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Lưu trữ lâu dài</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span>File đã lưu</span>
                  <span className="font-bold">1,247 files</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span>Dung lượng</span>
                  <span className="font-bold">850GB</span>
                </div>
                <Button className="w-full" variant="outline">
                  Quản lý file lưu trữ
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <VideoPlayer
        isOpen={isVideoPlayerOpen}
        onClose={() => setIsVideoPlayerOpen(false)}
        videoData={selectedVideo}
        onCutVideo={handleCutVideo}
      />
    </div>
  );
}
