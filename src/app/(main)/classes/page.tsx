"use client";

import { useState, useEffect, useCallback } from "react";
import {
  School,
  CheckCircle,
  AlertTriangle
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

// Import class API and types
import {
  listClassAPI,
  addClassAPI,
  deleteClassAPI,
  Class,
  ClassFormData,
  ClassPaginationData,
} from "@/api/class";

// Import semester and teacher APIs
import { listSemester<PERSON><PERSON>, Semester } from "@/api/semester";
import { listTeacher<PERSON><PERSON>, Teacher } from "@/api/teacher";
import { listRoomAPI, Room } from "@/api/classroom";

// Import class management components
import { ClassFilters } from "@/components/classes/ClassFilters";
import { ClassTable } from "@/components/classes/ClassTable";
import { ClassPagination } from "@/components/classes/ClassPagination";
import { ClassFormDialog } from "@/components/classes/ClassFormDialog";
import { ClassDeleteDialog } from "@/components/classes/ClassDeleteDialog";

// Import auth token utility
import { getAuthToken } from "@/utils/getAuthToken";


export default function ClassManagementPage() {
  // Class management state
  const [classes, setClasses] = useState<Class[]>([]);
  const [pagination, setPagination] = useState<ClassPaginationData>({
    page: 1,
    limit: 20, // Fixed page size
    totalData: 0,
    totalPage: 0
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Search and filter state
  const [searchName, setSearchName] = useState("");
  const [searchCode, setSearchCode] = useState("");
  const [searchSemester, setSearchSemester] = useState("");

  // Modal state
  const [isAddEditModalOpen, setIsAddEditModalOpen] = useState(false);
  const [editingClass, setEditingClass] = useState<Class | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deletingClass, setDeletingClass] = useState<Class | null>(null);

  // Dropdown data state
  const [semesters, setSemesters] = useState<Semester[]>([]);
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [rooms, setRooms] = useState<Room[]>([]);

  // Load classes on component mount and when page changes
  useEffect(() => {
    loadClasses();
    loadDropdownData();
  }, [pagination.page]);

  // API functions
  const loadClasses = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const token = getAuthToken();
      const params = {
        token,
        page: pagination.page,
        limit: 20, // Fixed page size
        ...(searchName && { name: searchName }),
        ...(searchSemester && { semester_name: searchSemester }),
      };

      const response = await listClassAPI(params);

      if (response.code === 1) {
        setClasses(response.data);
        setPagination(response.totalData);
      } else {
        throw new Error(response.mess || "Lỗi khi tải danh sách lớp học");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
      setClasses([]);
    } finally {
      setIsLoading(false);
    }
  }, [pagination.page, searchName, searchSemester]);

  const loadDropdownData = useCallback(async () => {
    try {
      const token = getAuthToken();
      if (!token) {
        console.error("No auth token available");
        return;
      }

      // Load semesters, teachers, and rooms in parallel
      const [semesterResponse, teacherResponse, roomResponse] = await Promise.all([
        listSemesterAPI({ token }),
        listTeacherAPI({ token }),
        listRoomAPI({ token })
      ]);

      if (semesterResponse.code === 0) setSemesters(semesterResponse.data);
      if (teacherResponse.code === 0) setTeachers(teacherResponse.data);
      if (roomResponse.code === 1) setRooms(roomResponse.data);
    } catch (err) {
      console.error("Error loading dropdown data:", err);
    }
  }, []);

  // Search and filter handlers
  const handleSearchChange = useCallback((field: string, value: string) => {
    switch (field) {
      case "name":
        setSearchName(value);
        break;
      case "code":
        setSearchCode(value);
        break;
      case "semester":
        setSearchSemester(value);
        break;
    }
  }, []);

  const handleSearch = useCallback(() => {
    setPagination(prev => ({ ...prev, page: 1 }));
    loadClasses();
  }, [loadClasses]);

  const clearSearch = useCallback(() => {
    setSearchName("");
    setSearchCode("");
    setSearchSemester("");
    setPagination(prev => ({ ...prev, page: 1 }));
    setTimeout(() => loadClasses(), 100);
  }, [loadClasses]);

  // Pagination handlers
  const handlePageChange = useCallback((page: number) => {
    setPagination(prev => ({ ...prev, page }));
  }, []);

  // CRUD operation handlers
  const openAddModal = useCallback(() => {
    setEditingClass(null);
    setIsAddEditModalOpen(true);
  }, []);

  const openEditModal = useCallback((classItem: Class) => {
    setEditingClass(classItem);
    setIsAddEditModalOpen(true);
  }, []);

  const closeModal = useCallback(() => {
    setIsAddEditModalOpen(false);
    setEditingClass(null);
  }, []);

  const handleFormSubmit = useCallback(async (formData: ClassFormData) => {
    try {
      setIsSubmitting(true);
      setError(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const classData = {
        ...formData,
        ...(editingClass && { id: editingClass.id }),
      };

      const response = await addClassAPI(token, classData);

      if (response.code === 1) {
        setSuccess(editingClass ? "Cập nhật lớp học thành công!" : "Thêm lớp học thành công!");
        closeModal();
        loadClasses();

        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(null), 3000);
      } else {
        throw new Error(response.mess || "Lỗi khi lưu lớp học");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsSubmitting(false);
    }
  }, [editingClass, closeModal, loadClasses]);

  const openDeleteDialog = useCallback((classItem: Class) => {
    setDeletingClass(classItem);
    setIsDeleteDialogOpen(true);
  }, []);

  const closeDeleteDialog = useCallback(() => {
    setIsDeleteDialogOpen(false);
    setDeletingClass(null);
  }, []);

  const handleDelete = useCallback(async (classItem: Class) => {
    try {
      setIsSubmitting(true);
      setError(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await deleteClassAPI(token, classItem.id);

      if (response.code === 1) {
        setSuccess("Xóa lớp học thành công!");
        closeDeleteDialog();
        loadClasses();

        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(null), 3000);
      } else {
        throw new Error(response.mess || "Lỗi khi xóa lớp học");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsSubmitting(false);
    }
  }, [closeDeleteDialog, loadClasses]);



  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-800 flex items-center gap-2">
          <School className="h-8 w-8 text-blue-600" />
          Quản lý Lớp học
        </h1>
      </div>

      {/* Error and Success Messages */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-700">{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-700">{success}</AlertDescription>
        </Alert>
      )}

      {/* Class Filters Component */}
      <ClassFilters
        searchName={searchName}
        searchCode={searchCode}
        searchSemester={searchSemester}
        onSearchChange={handleSearchChange}
        onSearch={handleSearch}
        onClearFilters={clearSearch}
        onAddClass={openAddModal}
      />

      {/* Class Table Component */}
      <ClassTable
        classes={classes}
        rooms={rooms}
        onEdit={openEditModal}
        onDelete={openDeleteDialog}
        isLoading={isLoading}
        totalCount={pagination.totalData}
      />

      {/* Class Pagination Component */}
      <ClassPagination
        pagination={pagination}
        onPageChange={handlePageChange}
      />

      {/* Class Form Dialog Component */}
      <ClassFormDialog
        isOpen={isAddEditModalOpen}
        onClose={closeModal}
        editingClass={editingClass}
        onSubmit={handleFormSubmit}
        isSubmitting={isSubmitting}
        semesters={semesters}
        teachers={teachers}
        rooms={rooms}
      />

      {/* Class Delete Dialog Component */}
      <ClassDeleteDialog
        isOpen={isDeleteDialogOpen}
        onClose={closeDeleteDialog}
        classItem={deletingClass}
        onConfirm={handleDelete}
        isDeleting={isSubmitting}
      />
    </div>
  );
};
