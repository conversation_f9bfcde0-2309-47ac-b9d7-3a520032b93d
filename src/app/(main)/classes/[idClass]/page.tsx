"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  ArrowLeft,
  School,
  AlertTriangle,
  CheckCircle,
  Home,
  ChevronRight,
  UserPlus
} from "lucide-react";
import { useParams, useRouter } from "next/navigation";

// Import student API and types
import {
  listStudentsInClassAPI,
  Student,
  ClassInfo,
  StudentPaginationData,
  addMultipleStudentsToClassAPI,
  deleteStudentFromClassAPI,
  AddMultipleStudentsResponse,
  RemoveStudentResponse,
} from "@/api/student";

// Import class-specific student management components
import { StudentFilters } from "@/components/classes/studentInClass/StudentFilters";
import { StudentTable } from "@/components/classes/studentInClass/StudentTable";
import { StudentPagination } from "@/components/classes/studentInClass/StudentPagination";
import { AddStudentsDialog } from "@/components/classes/studentInClass/AddStudentsDialog";
import { RemoveStudentDialog } from "@/components/classes/studentInClass/RemoveStudentDialog";

// Import auth token utility
import { getAuthToken } from "@/utils/getAuthToken";

export default function StudentListPage() {
  const router = useRouter();
  const params = useParams();
  const idClass = params?.idClass as string;

  // Student management state
  const [students, setStudents] = useState<Student[]>([]);
  const [classInfo, setClassInfo] = useState<ClassInfo | null>(null);
  const [pagination, setPagination] = useState<StudentPaginationData>({
    page: 1,
    limit: 20, // Fixed page size
    totalData: 0,
    totalPage: 0
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Dialog states
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isRemoveDialogOpen, setIsRemoveDialogOpen] = useState(false);
  const [studentToRemove, setStudentToRemove] = useState<Student | null>(null);
  const [isRemoving, setIsRemoving] = useState(false);

  // Search state
  const [searchTerm, setSearchTerm] = useState("");

  // Load students on component mount and when page or search changes
  useEffect(() => {
    if (idClass) {
      loadStudents();
    }
  }, [idClass, pagination.page]);

  // API functions
  const loadStudents = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const params = {
        token,
        id_class: parseInt(idClass),
        page: pagination.page,
        limit: 20, // Fixed page size
        ...(searchTerm && { search: searchTerm }),
      };

      const response = await listStudentsInClassAPI(params);

      if (response.code === 1) {
        setStudents(response.data.students);
        setClassInfo(response.data.class_info);
        setPagination(response.totalData);
      } else {
        throw new Error(response.mess || "Lỗi khi tải danh sách học sinh");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
      setStudents([]);
      setClassInfo(null);
    } finally {
      setIsLoading(false);
    }
  }, [idClass, pagination.page, searchTerm]);

  // Search and filter handlers
  const handleSearchChange = useCallback((value: string) => {
    setSearchTerm(value);
  }, []);

  const handleSearch = useCallback(() => {
    setPagination(prev => ({ ...prev, page: 1 }));
    loadStudents();
  }, [loadStudents]);

  const clearSearch = useCallback(() => {
    setSearchTerm("");
    setPagination(prev => ({ ...prev, page: 1 }));
    setTimeout(() => loadStudents(), 100);
  }, [loadStudents]);

  // Pagination handlers
  const handlePageChange = useCallback((page: number) => {
    setPagination(prev => ({ ...prev, page }));
  }, []);

  // Dialog handlers
  const openAddDialog = useCallback(() => {
    setIsAddDialogOpen(true);
  }, []);

  const closeAddDialog = useCallback(() => {
    setIsAddDialogOpen(false);
  }, []);

  const openRemoveDialog = useCallback((student: Student) => {
    setStudentToRemove(student);
    setIsRemoveDialogOpen(true);
  }, []);

  const closeRemoveDialog = useCallback(() => {
    setIsRemoveDialogOpen(false);
    setStudentToRemove(null);
  }, []);

  // Handle successful addition of students
  const handleAddSuccess = useCallback((result: AddMultipleStudentsResponse) => {
    const { success_count, failed_students } = result;

    let message = `Thêm ${success_count} sinh viên vào lớp thành công!`;
    if (failed_students.length > 0) {
      message += ` ${failed_students.length} sinh viên không thể thêm.`;
    }

    setSuccess(message);
    loadStudents(); // Refresh the list

    // Clear success message after 5 seconds
    setTimeout(() => setSuccess(null), 5000);
  }, [loadStudents]);

  // Handle student removal
  const handleRemoveStudent = useCallback(async (student: Student) => {
    try {
      setIsRemoving(true);
      setError(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await deleteStudentFromClassAPI(
        token,
        parseInt(idClass),
        student.id
      );

      if (response.code === 1) {
        setSuccess(`Đã xóa sinh viên ${student.full_name} khỏi lớp thành công!`);
        closeRemoveDialog();
        loadStudents(); // Refresh the list

        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(null), 3000);
      } else {
        throw new Error(response.mess || "Lỗi khi xóa sinh viên khỏi lớp");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsRemoving(false);
    }
  }, [idClass, closeRemoveDialog, loadStudents]);

  return (
    <div className="space-y-6">
      {/* Breadcrumb Navigation */}
      <div className="flex items-center gap-2 text-sm text-gray-600">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push('/classes')}
          className="p-0 h-auto font-normal text-gray-600 hover:text-blue-600"
        >
          <Home className="h-4 w-4 mr-1" />
          Quản lý Lớp học
        </Button>
        <ChevronRight className="h-4 w-4" />
        <span className="font-medium text-gray-900">
          {classInfo ? classInfo.name : `Lớp`}
        </span>
        <ChevronRight className="h-4 w-4" />
        <span>Danh sách học sinh</span>
      </div>

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => router.push('/classes')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Quay lại
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-800 flex items-center gap-2">
              <School className="h-8 w-8 text-blue-600" />
              {classInfo ? classInfo.name : `Lớp`}
            </h1>
            {classInfo && (
              <p className="text-gray-600 mt-1">
                Mã lớp: <span className="font-mono font-medium">{classInfo.code}</span>
              </p>
            )}
          </div>
        </div>
        <div>
          <Button onClick={openAddDialog} className="bg-blue-600 hover:bg-blue-700">
            <UserPlus className="h-4 w-4 mr-2" />
            Thêm sinh viên
          </Button>
        </div>
      </div>

      {/* Error and Success Messages */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-700">{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-700">{success}</AlertDescription>
        </Alert>
      )}

      {/* Student Filters Component */}
      <StudentFilters
        searchTerm={searchTerm}
        onSearchChange={handleSearchChange}
        onSearch={handleSearch}
        onClearFilters={clearSearch}
      />

      {/* Student Table Component */}
      <StudentTable
        students={students}
        isLoading={isLoading}
        totalCount={pagination.totalData}
        onRemoveStudent={openRemoveDialog}
      />

      {/* Student Pagination Component */}
      <StudentPagination
        pagination={pagination}
        onPageChange={handlePageChange}
      />

      {/* Add Students Dialog */}
      <AddStudentsDialog
        isOpen={isAddDialogOpen}
        onClose={closeAddDialog}
        classId={parseInt(idClass)}
        className={classInfo?.name || 'Lớp'}
        onSuccess={handleAddSuccess}
      />

      {/* Remove Student Dialog */}
      <RemoveStudentDialog
        isOpen={isRemoveDialogOpen}
        onClose={closeRemoveDialog}
        student={studentToRemove}
        className={classInfo?.name || 'Lớp'}
        onConfirm={handleRemoveStudent}
        isRemoving={isRemoving}
      />
    </div>
  );
};
