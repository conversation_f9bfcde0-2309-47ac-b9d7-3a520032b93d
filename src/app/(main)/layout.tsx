'use client';

import { AppSidebar } from "@/components/layout/AppSideBar";
import { SidebarProvider, SidebarInset, SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import { RootState } from "@/redux/store";
import { useSelector } from "react-redux";

interface SchoolLayoutProps {
  children: React.ReactNode;
}

export default function SchoolLayout({ children }: SchoolLayoutProps) {
  const user_info = useSelector((state: RootState) => state.user.currentUser);
  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-12 shrink-0 items-center gap-2 border-b px-4">
          <SidebarTrigger className="-ml-1" />
          <h1 className="text-lg font-semibold">Hệ thống Quản lý Camera AI {user_info?.name}</h1>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4">
          {children}
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}