"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Play,
  RefreshCw,
  Copy,
  CheckCircle,
  Video,
  Settings,
  Monitor,
  Smartphone,
  Globe
} from "lucide-react";
import { RTMPPlayer } from "@/components/camera/RTMPPlayer";
import { VideoJSPlayer } from "@/components/camera/VideoJSPlayer";
import { HLSPlayer } from "@/components/camera/HLSPlayer";

export default function StreamTestPage() {
  const [streamUrl, setStreamUrl] = useState("http://stream.topcam.ai.vn/viewcam/hls/live/topcam_5c_34_5b_fe_d8_3b.m3u8");
  const [isPlaying, setIsPlaying] = useState(false);
  const [activePlayer, setActivePlayer] = useState<"enhanced" | "videojs" | "hls">("hls");
  const [copiedText, setCopiedText] = useState<string | null>(null);
  const [streamInfo, setStreamInfo] = useState<any>(null);

  // Predefined test streams
  const testStreams = [
    {
      name: "TopCam HLS Stream",
      url: "http://stream.topcam.ai.vn/viewcam/hls/live/topcam_5c_34_5b_fe_d8_3b.m3u8",
      type: "HLS",
      description: "Camera stream từ TopCam server"
    },
    {
      name: "Big Buck Bunny (HLS)",
      url: "https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8",
      type: "HLS",
      description: "Test stream công khai"
    },
    {
      name: "Sample MP4 Stream",
      url: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
      type: "MP4",
      description: "Video MP4 trực tiếp"
    },
    {
      name: "RTMP Test (sẽ fail)",
      url: "rtmp://**************:1935/live/topcam_24_52_34_22_14_52",
      type: "RTMP",
      description: "Test RTMP để xem error handling"
    }
  ];

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedText(label);
      setTimeout(() => setCopiedText(null), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const handleStreamLoad = () => {
    setIsPlaying(true);
    setStreamInfo({
      loadTime: new Date().toLocaleTimeString(),
      player: activePlayer,
      url: streamUrl
    });
  };

  const handleStreamError = (error: string) => {
    setIsPlaying(false);
    console.error('Stream error:', error);
  };

  const getStreamType = (url: string) => {
    if (url.includes('.m3u8')) return 'HLS';
    if (url.includes('.flv')) return 'FLV';
    if (url.includes('.mp4')) return 'MP4';
    if (url.startsWith('rtmp://')) return 'RTMP';
    return 'Unknown';
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-800 flex items-center justify-center gap-2">
          <Video className="h-8 w-8 text-blue-600" />
          Stream Test Page
        </h1>
        <p className="text-gray-600">
          Test và debug các loại video streams
        </p>
      </div>

      {/* Stream URL Input */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Stream Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Stream URL</Label>
            <div className="flex gap-2">
              <Input
                value={streamUrl}
                onChange={(e) => setStreamUrl(e.target.value)}
                placeholder="Nhập URL stream..."
                className="flex-1"
              />
              <Button
                variant="outline"
                onClick={() => copyToClipboard(streamUrl, 'Stream URL')}
              >
                {copiedText === 'Stream URL' ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          {/* Stream Info */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
            <div className="text-center">
              <div className="text-sm text-gray-600">Stream Type</div>
              <div className="font-medium text-lg">{getStreamType(streamUrl)}</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-600">Player</div>
              <div className="font-medium text-lg capitalize">{activePlayer}</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-600">Status</div>
              <div className={`font-medium text-lg ${isPlaying ? 'text-green-600' : 'text-gray-400'}`}>
                {isPlaying ? 'Playing' : 'Stopped'}
              </div>
            </div>
          </div>

          {/* Quick Test Streams */}
          <div className="space-y-2">
            <Label>Quick Test Streams</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {testStreams.map((stream, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => setStreamUrl(stream.url)}
                  className="justify-start text-left h-auto p-3"
                >
                  <div>
                    <div className="font-medium">{stream.name}</div>
                    <div className="text-xs text-gray-500">{stream.description}</div>
                    <div className="text-xs text-blue-600">{stream.type}</div>
                  </div>
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Player Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Monitor className="h-5 w-5" />
            Player Selection
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 flex-wrap">
            <Button
              variant={activePlayer === "hls" ? "default" : "outline"}
              onClick={() => setActivePlayer("hls")}
              className="flex items-center gap-2"
              size="sm"
            >
              <Play className="h-4 w-4" />
              HLS Player
            </Button>
            <Button
              variant={activePlayer === "enhanced" ? "default" : "outline"}
              onClick={() => setActivePlayer("enhanced")}
              className="flex items-center gap-2"
              size="sm"
            >
              <Globe className="h-4 w-4" />
              Enhanced
            </Button>
            <Button
              variant={activePlayer === "videojs" ? "default" : "outline"}
              onClick={() => setActivePlayer("videojs")}
              className="flex items-center gap-2"
              size="sm"
            >
              <Smartphone className="h-4 w-4" />
              Video.js
            </Button>
          </div>
          <div className="text-sm text-gray-600 mt-2 space-y-1">
            <div><strong>HLS Player:</strong> Optimized cho .m3u8 streams với HLS.js</div>
            <div><strong>Enhanced:</strong> Multiple format attempts với fallbacks</div>
            <div><strong>Video.js:</strong> Professional player với plugin support</div>
          </div>
        </CardContent>
      </Card>

      {/* Video Player */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Play className="h-5 w-5" />
              Video Player
            </div>
            <div className="flex items-center gap-2">
              {streamInfo && (
                <div className="text-sm text-gray-600">
                  Loaded at {streamInfo.loadTime}
                </div>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.reload()}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Reset
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {streamUrl ? (
            <div className="space-y-4">
              {/* Current Stream Info */}
              <Alert>
                <Video className="h-4 w-4" />
                <AlertDescription>
                  <strong>Testing:</strong> {streamUrl}
                  <br />
                  <strong>Type:</strong> {getStreamType(streamUrl)} | <strong>Player:</strong> {activePlayer}
                </AlertDescription>
              </Alert>

              {/* Player Component */}
              {activePlayer === "hls" ? (
                <HLSPlayer
                  streamUrl={streamUrl}
                  className="w-full max-w-4xl mx-auto"
                  onLoad={handleStreamLoad}
                  onError={handleStreamError}
                />
              ) : activePlayer === "enhanced" ? (
                <RTMPPlayer
                  streamUrl={streamUrl}
                  className="w-full max-w-4xl mx-auto"
                  onLoad={handleStreamLoad}
                  onError={handleStreamError}
                />
              ) : (
                <VideoJSPlayer
                  streamUrl={streamUrl}
                  className="w-full max-w-4xl mx-auto"
                  onLoad={handleStreamLoad}
                  onError={handleStreamError}
                />
              )}
            </div>
          ) : (
            <div className="text-center py-12 text-gray-500">
              <Video className="h-16 w-16 mx-auto mb-4 text-gray-300" />
              <p>Nhập stream URL để bắt đầu test</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Debug Info */}
      {streamInfo && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Debug Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm">
              <div>Stream URL: {streamInfo.url}</div>
              <div>Player: {streamInfo.player}</div>
              <div>Load Time: {streamInfo.loadTime}</div>
              <div>Status: {isPlaying ? 'Playing' : 'Stopped'}</div>
              <div>Stream Type: {getStreamType(streamInfo.url)}</div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Hướng dẫn sử dụng</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm">
          <div>1. <strong>Chọn stream URL:</strong> Sử dụng URL có sẵn hoặc nhập URL tùy chỉnh</div>
          <div>2. <strong>Chọn player:</strong> Enhanced Player (custom) hoặc Video.js Player</div>
          <div>3. <strong>Test stream:</strong> Player sẽ tự động cố gắng phát stream</div>
          <div>4. <strong>Debug:</strong> Kiểm tra Console và Debug Information để troubleshoot</div>
          <div>5. <strong>HLS streams (.m3u8):</strong> Hoạt động tốt nhất trên browsers</div>
          <div>6. <strong>RTMP streams:</strong> Sẽ hiển thị error và hướng dẫn chuyển đổi</div>
        </CardContent>
      </Card>
    </div>
  );
}
