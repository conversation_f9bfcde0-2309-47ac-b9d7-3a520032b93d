"use client";

import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  DoorOpen, 
  Plus, 
  Loader2, 
  AlertTriangle, 
  CheckCircle
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  listRoomAPI, 
  detailRoomAPI, 
  addRoomAPI, 
  deleteRoomAPI,
  Room,
  RoomFormData,
  PaginationMeta,
  Class,
  FloorInfo,
  BuildingInfo
} from "@/api/classroom";
import { listBuildingAPI, Building } from "@/api/building";
import { listFloorAPI, Floor } from "@/api/floor";
import { getAuthToken } from "@/utils/getAuthToken";

// Import the new components
import { ClassroomFilters } from "@/components/classrooms/ClassroomFilters";
import { ClassroomTable } from "@/components/classrooms/ClassroomTable";
import { ClassroomPagination } from "@/components/classrooms/ClassroomPagination";
import { ClassroomFormDialog } from "@/components/classrooms/ClassroomFormDialog";
import { ClassroomDeleteDialog } from "@/components/classrooms/ClassroomDeleteDialog";

export default function ClassroomManagementPage() {
  // State management
  const [rooms, setRooms] = useState<Room[]>([]);
  const [buildings, setBuildings] = useState<Building[]>([]);
  const [floors, setFloors] = useState<Floor[]>([]);
  const [roomDetails, setRoomDetails] = useState<Record<number, { 
    floor_info: FloorInfo; 
    building_info: BuildingInfo; 
    classes: Class[] 
  }>>({});
  const [pagination, setPagination] = useState<PaginationMeta>({
    page: 1,
    limit: 10,
    totalData: 0,
    totalPage: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Search and filter state
  const [searchName, setSearchName] = useState("");
  const [selectedBuildingId, setSelectedBuildingId] = useState("all");
  const [selectedFloorId, setSelectedFloorId] = useState("all");
  
  // Modal state
  const [isAddEditModalOpen, setIsAddEditModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [editingRoom, setEditingRoom] = useState<Room | null>(null);
  const [deletingRoom, setDeletingRoom] = useState<Room | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load data on component mount
  useEffect(() => {
    fetchBuildings();
    fetchFloors();
    fetchRooms();
  }, [pagination.page, pagination.limit]);

  const fetchBuildings = async () => {
    try {
      const token = getAuthToken();
      if (!token) return;

      const response = await listBuildingAPI({ 
        token,
        limit: 1000 // Get all buildings for dropdown
      });
      
      if (response.code === 1 && response.data) {
        setBuildings(response.data);
      }
    } catch (err) {
      console.error("Error loading buildings:", err);
    }
  };

  const fetchFloors = async () => {
    try {
      const token = getAuthToken();
      if (!token) return;

      const response = await listFloorAPI({ 
        token,
        limit: 1000 // Get all floors for dropdown
      });
      
      if (response.code === 1 && response.data) {
        setFloors(response.data);
      }
    } catch (err) {
      console.error("Error loading floors:", err);
    }
  };

  const fetchRooms = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await listRoomAPI({
        token,
        name: searchName || undefined,
        floor_id: (selectedFloorId && selectedFloorId !== "all") ? parseInt(selectedFloorId) : undefined,
        building_id: (selectedBuildingId && selectedBuildingId !== "all") ? parseInt(selectedBuildingId) : undefined,
        page: pagination.page,
        limit: pagination.limit
      });
      
      // Note: API returns code: 1 for success
      if (response.code === 1 && response.data) {
        setRooms(response.data);
        if (response.meta) {
          setPagination(response.meta);
        }
      } else {
        throw new Error(response.mess || "Không thể lấy danh sách phòng học");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsLoading(false);
    }
  };

  // Load room details for expanded rooms
  const loadRoomDetails = async (roomId: number) => {
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await detailRoomAPI(token, roomId);
      
      if (response.code === 1 && response.data) {
        setRoomDetails(prev => ({
          ...prev,
          [roomId]: { 
            floor_info: response.data.room_info.floor_info,
            building_info: response.data.room_info.building_info,
            classes: response.data.classes 
          }
        }));
      }
    } catch (err) {
      console.error("Error loading room details:", err);
    }
  };

  // Search functionality
  const handleSearch = () => {
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchRooms();
  };

  const clearSearch = () => {
    setSearchName("");
    setSelectedBuildingId("all");
    setSelectedFloorId("all");
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchRooms();
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  const handleLimitChange = (limit: number) => {
    setPagination(prev => ({ ...prev, limit, page: 1 }));
  };

  // Search and filter handlers
  const handleSearchChange = (_field: 'name', value: string) => {
    setSearchName(value);
  };

  const handleBuildingChange = (buildingId: string) => {
    setSelectedBuildingId(buildingId);
    // Reset floor selection when building changes
    setSelectedFloorId("all");
  };

  const handleFloorChange = (floorId: string) => {
    setSelectedFloorId(floorId);
  };

  // Modal handlers
  const openAddModal = () => {
    setEditingRoom(null);
    setIsAddEditModalOpen(true);
  };

  const openEditModal = async (room: Room) => {
    try {
      setError(null);
      setEditingRoom(room);
      setIsAddEditModalOpen(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    }
  };

  const closeModal = () => {
    setIsAddEditModalOpen(false);
    setEditingRoom(null);
  };

  const openDeleteDialog = (room: Room) => {
    setDeletingRoom(room);
    setIsDeleteDialogOpen(true);
  };

  const closeDeleteDialog = () => {
    setIsDeleteDialogOpen(false);
    setDeletingRoom(null);
  };

  // Form submission handler for the dialog component
  const handleFormSubmit = async (formData: RoomFormData) => {
    try {
      setIsSubmitting(true);
      setError(null);
      setSuccess(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const submitData = {
        ...(editingRoom && { id: editingRoom.id }),
        name: formData.name,
        floor_id: formData.floor_id,
        school_id: formData.school_id,
      };

      const response = await addRoomAPI(token, submitData);

      // Note: API returns code: 1 for success
      if (response.code === 1 && response.data) {
        setSuccess(editingRoom ? "Cập nhật phòng học thành công!" : "Thêm phòng học thành công!");
        closeModal();
        fetchRooms(); // Refresh the list
        loadRoomDetails(response.data.id);
      } else {
        throw new Error(response.mess || "Không thể lưu phòng học");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async () => {
    if (!deletingRoom) return;

    try {
      setError(null);
      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await deleteRoomAPI(token, deletingRoom.id);

      // Note: API returns code: 1 for success
      if (response.code === 1) {
        setSuccess("Xóa phòng học thành công!");
        closeDeleteDialog();
        fetchRooms(); // Refresh the list
      } else {
        throw new Error(response.mess || "Không thể xóa phòng học");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
      closeDeleteDialog();
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-800 flex items-center gap-2">
            <DoorOpen className="h-8 w-8 text-purple-600" />
            Quản lý Phòng học
          </h1>
        </div>
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-purple-600" />
            <span className="ml-2 text-gray-600">Đang tải dữ liệu...</span>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-800 flex items-center gap-2">
          <DoorOpen className="h-8 w-8 text-purple-600" />
          Quản lý Phòng học
        </h1>
        <Button onClick={openAddModal} className="bg-purple-600 hover:bg-purple-700">
          <Plus className="h-4 w-4 mr-2" />
          Thêm phòng học
        </Button>
      </div>

      {/* Error/Success Messages */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-700">{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-700">{success}</AlertDescription>
        </Alert>
      )}

      {/* Classroom Filters Component */}
      <ClassroomFilters
        searchName={searchName}
        selectedBuildingId={selectedBuildingId}
        selectedFloorId={selectedFloorId}
        buildings={buildings}
        floors={floors}
        onSearchChange={handleSearchChange}
        onBuildingChange={handleBuildingChange}
        onFloorChange={handleFloorChange}
        onSearch={handleSearch}
        onClearFilters={clearSearch}
      />

      {/* Classroom Table Component */}
      <ClassroomTable
        rooms={rooms}
        roomDetails={roomDetails}
        onEdit={openEditModal}
        onDelete={openDeleteDialog}
        onLoadRoomDetails={loadRoomDetails}
        isLoading={false}
        totalCount={pagination.totalData}
      />

      {/* Classroom Pagination Component */}
      <ClassroomPagination
        pagination={pagination}
        onPageChange={handlePageChange}
        onLimitChange={handleLimitChange}
      />

      {/* Classroom Form Dialog Component */}
      <ClassroomFormDialog
        isOpen={isAddEditModalOpen}
        onClose={closeModal}
        editingRoom={editingRoom}
        buildings={buildings}
        floors={floors}
        onSubmit={handleFormSubmit}
        isSubmitting={isSubmitting}
      />

      {/* Classroom Delete Dialog Component */}
      <ClassroomDeleteDialog
        isOpen={isDeleteDialogOpen}
        onClose={closeDeleteDialog}
        room={deletingRoom}
        onConfirm={handleDelete}
        isDeleting={false}
      />
    </div>
  );
}
