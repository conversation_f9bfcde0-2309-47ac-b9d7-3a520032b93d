
"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Calendar,
  Search,
  Plus,
  Edit,
  Trash2,
  Loader2,
  AlertTriangle,
  CheckCircle,
  MoreHorizontal
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  listSemesterAPI,
  detailSemesterAPI,
  addSemesterAPI,
  deleteSemesterAPI,
  Semester,
  SemesterFormData
} from "@/api/semester";
import { getAuthToken } from "@/utils/getAuthToken";

export default function SemesterManagementPage() {
  // State management
  const [semesters, setSemesters] = useState<Semester[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Search and filter state
  const [searchName, setSearchName] = useState("");
  const [searchCode, setSearchCode] = useState("");

  // Modal state
  const [isAddEditModalOpen, setIsAddEditModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [editingSemester, setEditingSemester] = useState<Semester | null>(null);
  const [deletingSemester, setDeleteingSemester] = useState<Semester | null>(null);

  // Form state
  const [formData, setFormData] = useState<SemesterFormData>({
    name: "",
    code: "",
    start_date: "",
    end_date: "",
    id_parent: null,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Load semesters on component mount
  useEffect(() => {
    fetchSemesters();
  }, []);

  const fetchSemesters = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await listSemesterAPI({
        token,
        name: searchName || undefined,
        code: searchCode || undefined
      });

      if (response.code === 0 && response.data) {
        setSemesters(response.data);
      } else {
        throw new Error(response.mess || "Không thể lấy danh sách học kỳ");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsLoading(false);
    }
  };

  // Search functionality
  const handleSearch = () => {
    fetchSemesters();
  };

  const clearSearch = () => {
    setSearchName("");
    setSearchCode("");
    fetchSemesters();
  };

  // Form validation
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = "Tên học kỳ là bắt buộc";
    }

    if (!formData.code.trim()) {
      errors.code = "Mã học kỳ là bắt buộc";
    }

    if (!formData.start_date) {
      errors.start_date = "Ngày bắt đầu là bắt buộc";
    }

    if (!formData.end_date) {
      errors.end_date = "Ngày kết thúc là bắt buộc";
    }

    if (formData.start_date && formData.end_date) {
      const startDate = new Date(formData.start_date);
      const endDate = new Date(formData.end_date);

      if (endDate <= startDate) {
        errors.end_date = "Ngày kết thúc phải sau ngày bắt đầu";
      }
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Modal handlers
  const openAddModal = () => {
    setEditingSemester(null);
    setFormData({
      name: "",
      code: "",
      start_date: "",
      end_date: "",
      id_parent: null,
    });
    setValidationErrors({});
    setIsAddEditModalOpen(true);
  };

  const openEditModal = async (semester: Semester) => {
    try {
      setError(null);
      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      // Fetch detailed semester data
      const response = await detailSemesterAPI(token, semester.id);

      if (response.code === 0 && response.data) {
        setEditingSemester(response.data);
        setFormData({
          name: response.data.name,
          code: response.data.code,
          start_date: response.data.start_date,
          end_date: response.data.end_date,
          id_parent: response.data.id_parent,
        });
        setValidationErrors({});
        setIsAddEditModalOpen(true);
      } else {
        throw new Error(response.mess || "Không thể lấy chi tiết học kỳ");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    }
  };

  const closeModal = () => {
    setIsAddEditModalOpen(false);
    setEditingSemester(null);
    setFormData({
      name: "",
      code: "",
      start_date: "",
      end_date: "",
      id_parent: null,
    });
    setValidationErrors({});
  };

  const openDeleteDialog = (semester: Semester) => {
    setDeleteingSemester(semester);
    setIsDeleteDialogOpen(true);
  };

  const closeDeleteDialog = () => {
    setIsDeleteDialogOpen(false);
    setDeleteingSemester(null);
  };

  // Form handlers
  const handleInputChange = (field: keyof SemesterFormData, value: string | number | null) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }

    // Clear success message when user starts editing
    if (success) {
      setSuccess(null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);
      setSuccess(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const submitData = {
        ...(editingSemester && { id: editingSemester.id }),
        name: formData.name,
        code: formData.code,
        start_date: formData.start_date,
        end_date: formData.end_date,
        id_parent: formData.id_parent || 0, // Default to 0 if null
      };

      const response = await addSemesterAPI(token, submitData);

      if (response.code === 0 && response.data) {
        setSuccess(editingSemester ? "Cập nhật học kỳ thành công!" : "Thêm học kỳ thành công!");
        closeModal();
        fetchSemesters(); // Refresh the list
      } else {
        throw new Error(response.mess || "Không thể lưu học kỳ");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async () => {
    if (!deletingSemester) return;

    try {
      setError(null);
      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await deleteSemesterAPI(token, deletingSemester.id);

      if (response.code === 0) {
        setSuccess("Xóa học kỳ thành công!");
        closeDeleteDialog();
        fetchSemesters(); // Refresh the list
      } else {
        throw new Error(response.mess || "Không thể xóa học kỳ");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
      closeDeleteDialog();
    }
  };

  // Filter semesters based on search
  const filteredSemesters = semesters.filter(semester => {
    const nameMatch = !searchName || semester.name.toLowerCase().includes(searchName.toLowerCase());
    const codeMatch = !searchCode || semester.code.toLowerCase().includes(searchCode.toLowerCase());
    return nameMatch && codeMatch;
  });

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-800 flex items-center gap-2">
            <Calendar className="h-8 w-8 text-blue-600" />
            Quản lý Học kỳ
          </h1>
        </div>
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">Đang tải dữ liệu...</span>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-800 flex items-center gap-2">
          <Calendar className="h-8 w-8 text-blue-600" />
          Quản lý Học kỳ
        </h1>
        <Button onClick={openAddModal} className="bg-blue-600 hover:bg-blue-700">
          <Plus className="h-4 w-4 mr-2" />
          Thêm học kỳ
        </Button>
      </div>

      {/* Error/Success Messages */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-700">{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-700">{success}</AlertDescription>
        </Alert>
      )}

      {/* Search Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Tìm kiếm và lọc</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search-name">Tên học kỳ</Label>
              <Input
                id="search-name"
                placeholder="Nhập tên học kỳ..."
                value={searchName}
                onChange={(e) => setSearchName(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="search-code">Mã học kỳ</Label>
              <Input
                id="search-code"
                placeholder="Nhập mã học kỳ..."
                value={searchCode}
                onChange={(e) => setSearchCode(e.target.value)}
              />
            </div>
            <div className="flex items-end gap-2">
              <Button onClick={handleSearch} className="flex-1">
                <Search className="h-4 w-4 mr-2" />
                Tìm kiếm
              </Button>
              <Button onClick={clearSearch} variant="outline">
                Xóa bộ lọc
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data Table */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">
            Danh sách học kỳ ({filteredSemesters.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {filteredSemesters.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              Không có học kỳ nào được tìm thấy
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Tên học kỳ</TableHead>
                  <TableHead>Mã học kỳ</TableHead>
                  <TableHead>Ngày bắt đầu</TableHead>
                  <TableHead>Ngày kết thúc</TableHead>
                  <TableHead className="text-right">Thao tác</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredSemesters.map((semester) => (
                  <TableRow key={semester.id}>
                    <TableCell className="font-medium">{semester.name}</TableCell>
                    <TableCell>{semester.code}</TableCell>
                    <TableCell>
                      {new Date(semester.start_date).toLocaleDateString('vi-VN')}
                    </TableCell>
                    <TableCell>
                      {new Date(semester.end_date).toLocaleDateString('vi-VN')}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => openEditModal(semester)}
                        className="h-8 w-8 p-0"
                        title="Chỉnh sửa"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => openDeleteDialog(semester)}
                        className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                        title="Xóa"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Modal */}
      <Dialog open={isAddEditModalOpen} onOpenChange={setIsAddEditModalOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>
              {editingSemester ? "Chỉnh sửa học kỳ" : "Thêm học kỳ mới"}
            </DialogTitle>
            <DialogDescription>
              {editingSemester
                ? "Cập nhật thông tin học kỳ dưới đây."
                : "Nhập thông tin học kỳ mới dưới đây."
              }
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Tên học kỳ *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="Nhập tên học kỳ"
                  className={validationErrors.name ? "border-red-500" : ""}
                />
                {validationErrors.name && (
                  <p className="text-sm text-red-600">{validationErrors.name}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="code">Mã học kỳ *</Label>
                <Input
                  id="code"
                  value={formData.code}
                  onChange={(e) => handleInputChange("code", e.target.value)}
                  placeholder="Nhập mã học kỳ"
                  className={validationErrors.code ? "border-red-500" : ""}
                />
                {validationErrors.code && (
                  <p className="text-sm text-red-600">{validationErrors.code}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="start_date">Ngày bắt đầu *</Label>
                <Input
                  id="start_date"
                  type="date"
                  value={formData.start_date}
                  onChange={(e) => handleInputChange("start_date", e.target.value)}
                  className={validationErrors.start_date ? "border-red-500" : ""}
                />
                {validationErrors.start_date && (
                  <p className="text-sm text-red-600">{validationErrors.start_date}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="end_date">Ngày kết thúc *</Label>
                <Input
                  id="end_date"
                  type="date"
                  value={formData.end_date}
                  onChange={(e) => handleInputChange("end_date", e.target.value)}
                  className={validationErrors.end_date ? "border-red-500" : ""}
                />
                {validationErrors.end_date && (
                  <p className="text-sm text-red-600">{validationErrors.end_date}</p>
                )}
              </div>
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={closeModal}>
                Hủy
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Đang lưu...
                  </>
                ) : (
                  editingSemester ? "Cập nhật" : "Thêm mới"
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xác nhận xóa học kỳ</AlertDialogTitle>
            <AlertDialogDescription>
              Bạn có chắc chắn muốn xóa học kỳ `{deletingSemester?.name}` không?
              Hành động này không thể hoàn tác.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={closeDeleteDialog}>
              Hủy
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              Xóa
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
