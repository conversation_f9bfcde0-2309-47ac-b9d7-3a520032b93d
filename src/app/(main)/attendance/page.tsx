"use client";

import { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Users,
  UserPlus,
  AlertTriangle,
  CheckCircle,
  Download,
  RefreshCw,
  Calendar,
  Plus
} from "lucide-react";

// Import attendance components
import { ManualAttendanceDialog } from "@/components/attendance/ManualAttendanceDialog";
import { AttendanceFilters } from "@/components/attendance/AttendanceFilters";
import { AttendanceTable } from "@/components/attendance/AttendanceTable";
import { AttendancePagination } from "@/components/attendance/AttendancePagination";
import { AttendanceStats } from "@/components/attendance/AttendanceStats";
import { DeleteAttendanceDialog } from "@/components/attendance/DeleteAttendanceDialog";

// Import schedule management components
import { ScheduleFilters } from "@/components/attendance/ScheduleFilters";
import { ScheduleTable } from "@/components/attendance/ScheduleTable";
import { SchedulePagination } from "@/components/attendance/SchedulePagination";
import { ScheduleFormDialog } from "@/components/attendance/ScheduleFormDialog";
import { DeleteScheduleDialog } from "@/components/attendance/DeleteScheduleDialog";
import { ScheduleDetailDialog } from "@/components/attendance/ScheduleDetailDialog";

// Import API functions and types
import {
  listAttendedStudentsAPI,
  deleteAttendanceAPI,
  listCamerasAPI,
  AttendanceRecord,
  AttendanceFilters as FilterType,
  AttendancePaginationData,
  AttendanceStats as StatsType,
  Camera,
  // Schedule management APIs
  listAttendanceSchedulesAPI,
  deleteAttendanceScheduleAPI,
  AttendanceSchedule,
  AttendanceScheduleFilters,
  AttendanceSchedulePaginationData,
} from "@/api/attendance";
import { listClassAPI, Class } from "@/api/class";
import { getAuthToken } from "@/utils/getAuthToken";

export default function AttendanceManagement() {
  // State management
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [classes, setClasses] = useState<Class[]>([]);
  const [sessionSchedules, setSessionSchedules] = useState<AttendanceSchedule[]>([]);
  const [cameras, setCameras] = useState<Camera[]>([]);
  const [stats, setStats] = useState<StatsType>({
    total_present: 0,
    total_absent: 0,
    total_late: 0,
    attendance_rate: 0,
  });
  const [pagination, setPagination] = useState<AttendancePaginationData>({
    page: 1,
    limit: 20,
    totalData: 0,
    totalPage: 0,
  });

  // Filter state
  const [filters, setFilters] = useState<FilterType>({
    start_date: new Date().toISOString().split('T')[0], // Today
    end_date: new Date().toISOString().split('T')[0],   // Today
    page: 1,
    limit: 20,
  });

  // UI state
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Dialog states
  const [isManualDialogOpen, setIsManualDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [recordToDelete, setRecordToDelete] = useState<AttendanceRecord | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Schedule management state
  const [schedules, setSchedules] = useState<AttendanceSchedule[]>([]);
  const [schedulePagination, setSchedulePagination] = useState<AttendanceSchedulePaginationData>({
    page: 1,
    limit: 20,
    totalData: 0,
    totalPage: 0,
  });
  const [scheduleFilters, setScheduleFilters] = useState<AttendanceScheduleFilters>({
    page: 1,
    limit: 20,
  });

  // Schedule dialog states
  const [isScheduleFormOpen, setIsScheduleFormOpen] = useState(false);
  const [isScheduleDeleteOpen, setIsScheduleDeleteOpen] = useState(false);
  const [isScheduleDetailOpen, setIsScheduleDetailOpen] = useState(false);
  const [editingSchedule, setEditingSchedule] = useState<AttendanceSchedule | null>(null);
  const [scheduleToDelete, setScheduleToDelete] = useState<AttendanceSchedule | null>(null);
  const [scheduleToView, setScheduleToView] = useState<AttendanceSchedule | null>(null);
  const [isDeletingSchedule, setIsDeletingSchedule] = useState(false);
  const [isLoadingSchedules, setIsLoadingSchedules] = useState(false);

  // Tab state
  const [activeTab, setActiveTab] = useState("attendance");

  // Load initial data
  const loadInitialData = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      // Load classes, schedules, and cameras in parallel
      const [classResponse, scheduleResponse, cameraResponse] = await Promise.all([
        listClassAPI({ token }),
        listAttendanceSchedulesAPI(token, { limit: 100 }), // Load all active schedules
        listCamerasAPI(token)
      ]);

      if (classResponse.code === 1) setClasses(classResponse.data);
      if (scheduleResponse.code === 1) setSessionSchedules(scheduleResponse.data);
      if (cameraResponse.code === 1) setCameras(cameraResponse.data);

    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi khi tải dữ liệu ban đầu");
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Load attendance records
  const loadAttendanceRecords = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await listAttendedStudentsAPI(token, filters);

      if (response.code === 1) {
        setAttendanceRecords(response.data);
        setPagination(response.totalData);
        setStats(response.stats);
      } else {
        throw new Error(response.mess || "Lỗi khi tải danh sách điểm danh");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
      setAttendanceRecords([]);
    } finally {
      setIsLoading(false);
    }
  }, [filters]);

  // Load data on component mount
  useEffect(() => {
    loadInitialData();
  }, [loadInitialData]);

  // Load attendance records when filters change
  useEffect(() => {
    loadAttendanceRecords();
  }, [loadAttendanceRecords]);

  // Handle filter changes
  const handleFiltersChange = useCallback((newFilters: FilterType) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 }));
  }, []);

  // Handle search
  const handleSearch = useCallback(() => {
    setFilters(prev => ({ ...prev, page: 1 }));
    loadAttendanceRecords();
  }, [loadAttendanceRecords]);

  // Clear filters
  const clearFilters = useCallback(() => {
    const today = new Date().toISOString().split('T')[0];
    setFilters({
      start_date: today,
      end_date: today,
      page: 1,
      limit: 50,
    });
  }, []);

  // Handle pagination
  const handlePageChange = useCallback((page: number) => {
    setFilters(prev => ({ ...prev, page }));
  }, []);

  const handleLimitChange = useCallback((limit: number) => {
    setFilters(prev => ({ ...prev, limit, page: 1 }));
  }, []);

  // Dialog handlers
  const openManualDialog = useCallback(() => {
    setIsManualDialogOpen(true);
  }, []);

  const closeManualDialog = useCallback(() => {
    setIsManualDialogOpen(false);
  }, []);

  const openDeleteDialog = useCallback((record: AttendanceRecord) => {
    setRecordToDelete(record);
    setIsDeleteDialogOpen(true);
  }, []);

  const closeDeleteDialog = useCallback(() => {
    setIsDeleteDialogOpen(false);
    setRecordToDelete(null);
  }, []);

  // Handle manual attendance success
  const handleManualAttendanceSuccess = useCallback((record: AttendanceRecord) => {
    setSuccess(`Đã tạo điểm danh thủ công cho học sinh ${record.student_name} thành công!`);
    loadAttendanceRecords(); // Refresh the list

    // Clear success message after 3 seconds
    setTimeout(() => setSuccess(null), 3000);
  }, [loadAttendanceRecords]);

  // Handle delete attendance
  const handleDeleteAttendance = useCallback(async (record: AttendanceRecord) => {
    try {
      setIsDeleting(true);
      setError(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await deleteAttendanceAPI(token, record.id);

      if (response.code === 1) {
        setSuccess(`Đã xóa bản ghi điểm danh của học sinh ${record.student_name} thành công!`);
        closeDeleteDialog();
        loadAttendanceRecords(); // Refresh the list

        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(null), 3000);
      } else {
        throw new Error(response.mess || "Lỗi khi xóa bản ghi điểm danh");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsDeleting(false);
    }
  }, [closeDeleteDialog, loadAttendanceRecords]);

  // Schedule management functions
  const loadAttendanceSchedules = useCallback(async () => {
    try {
      setIsLoadingSchedules(true);
      setError(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await listAttendanceSchedulesAPI(token, scheduleFilters);

      if (response.code === 1) {
        setSchedules(response.data);
        setSchedulePagination(response.totalData);
      } else {
        throw new Error(response.mess || "Lỗi khi tải danh sách lịch điểm danh");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
      setSchedules([]);
    } finally {
      setIsLoadingSchedules(false);
    }
  }, [scheduleFilters]);

  // Load schedules when filters change
  useEffect(() => {
    if (activeTab === "schedules") {
      loadAttendanceSchedules();
    }
  }, [activeTab, loadAttendanceSchedules]);

  // Schedule filter handlers
  const handleScheduleFiltersChange = useCallback((newFilters: AttendanceScheduleFilters) => {
    setScheduleFilters(prev => ({ ...prev, ...newFilters, page: 1 }));
  }, []);

  const handleScheduleSearch = useCallback(() => {
    setScheduleFilters(prev => ({ ...prev, page: 1 }));
    loadAttendanceSchedules();
  }, [loadAttendanceSchedules]);

  const clearScheduleFilters = useCallback(() => {
    setScheduleFilters({
      page: 1,
      limit: 25,
    });
  }, []);

  // Schedule pagination handlers
  const handleSchedulePageChange = useCallback((page: number) => {
    setScheduleFilters(prev => ({ ...prev, page }));
  }, []);

  const handleScheduleLimitChange = useCallback((limit: number) => {
    setScheduleFilters(prev => ({ ...prev, limit, page: 1 }));
  }, []);

  // Schedule dialog handlers
  const openScheduleForm = useCallback((schedule?: AttendanceSchedule) => {
    setEditingSchedule(schedule || null);
    setIsScheduleFormOpen(true);
  }, []);

  const closeScheduleForm = useCallback(() => {
    setIsScheduleFormOpen(false);
    setEditingSchedule(null);
  }, []);

  const openScheduleDetail = useCallback((schedule: AttendanceSchedule) => {
    setScheduleToView(schedule);
    setIsScheduleDetailOpen(true);
  }, []);

  const closeScheduleDetail = useCallback(() => {
    setIsScheduleDetailOpen(false);
    setScheduleToView(null);
  }, []);

  const openScheduleDelete = useCallback((schedule: AttendanceSchedule) => {
    setScheduleToDelete(schedule);
    setIsScheduleDeleteOpen(true);
  }, []);

  const closeScheduleDelete = useCallback(() => {
    setIsScheduleDeleteOpen(false);
    setScheduleToDelete(null);
  }, []);

  // Handle schedule form success
  const handleScheduleFormSuccess = useCallback((schedule: AttendanceSchedule) => {
    setSuccess(`Đã ${editingSchedule ? 'cập nhật' : 'tạo'} lịch điểm danh "${schedule.schedule_name}" thành công!`);
    loadAttendanceSchedules(); // Refresh the list

    // Clear success message after 3 seconds
    setTimeout(() => setSuccess(null), 3000);
  }, [editingSchedule, loadAttendanceSchedules]);

  // Handle delete schedule
  const handleDeleteSchedule = useCallback(async (schedule: AttendanceSchedule) => {
    try {
      setIsDeletingSchedule(true);
      setError(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await deleteAttendanceScheduleAPI(token, schedule.id);

      if (response.code === 1) {
        setSuccess(`Đã xóa lịch điểm danh "${schedule.schedule_name}" thành công!`);
        closeScheduleDelete();
        loadAttendanceSchedules(); // Refresh the list

        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(null), 3000);
      } else {
        throw new Error(response.mess || "Lỗi khi xóa lịch điểm danh");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsDeletingSchedule(false);
    }
  }, [closeScheduleDelete, loadAttendanceSchedules]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-800 flex items-center gap-2">
            <Users className="h-8 w-8 text-blue-600" />
            Quản lý Điểm danh
          </h1>
          <p className="text-gray-600 mt-1">
            Quản lý và theo dõi điểm danh học sinh
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={activeTab === "attendance" ? loadAttendanceRecords : loadAttendanceSchedules}
            variant="outline"
            disabled={isLoading || isLoadingSchedules}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${(isLoading || isLoadingSchedules) ? 'animate-spin' : ''}`} />
            Làm mới
          </Button>
          {activeTab === "attendance" ? (
            <>
              <Button onClick={openManualDialog} className="bg-blue-600 hover:bg-blue-700">
                <UserPlus className="h-4 w-4 mr-2" />
                Điểm danh thủ công
              </Button>
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Xuất báo cáo
              </Button>
            </>
          ) : (
            <Button onClick={() => openScheduleForm()} className="bg-blue-600 hover:bg-blue-700">
              <Plus className="h-4 w-4 mr-2" />
              Tạo lịch điểm danh
            </Button>
          )}
        </div>
      </div>

      {/* Error and Success Messages */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-700">{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-700">{success}</AlertDescription>
        </Alert>
      )}

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="attendance" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Điểm danh
          </TabsTrigger>
          <TabsTrigger value="schedules" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Lịch điểm danh
          </TabsTrigger>
        </TabsList>

        {/* Attendance Tab */}
        <TabsContent value="attendance" className="space-y-6">
          {/* Statistics ẩn tính ănng thống kê điểm danh */}
          {/* <AttendanceStats stats={stats} isLoading={isLoading} /> */}

          {/* Filters */}
          <AttendanceFilters
            filters={filters}
            onFiltersChange={handleFiltersChange}
            onSearch={handleSearch}
            onClearFilters={clearFilters}
            classes={classes}
            schedules={sessionSchedules}
            cameras={cameras}
            isLoading={isLoading}
            isLoadingSchedules={false} // Session schedules are loaded with initial data
          />

          {/* Attendance Table */}
          <AttendanceTable
            records={attendanceRecords}
            isLoading={isLoading}
            totalCount={pagination.totalData}
            onDeleteRecord={openDeleteDialog}
          />

          {/* Pagination - Only show when totalData > 20 */}
          {pagination.totalData > 20 && (
            <AttendancePagination
              pagination={pagination}
              onPageChange={handlePageChange}
              onLimitChange={handleLimitChange}
            />
          )}
        </TabsContent>

        {/* Schedule Management Tab */}
        <TabsContent value="schedules" className="space-y-6">
          {/* Schedule Filters */}
          <ScheduleFilters
            filters={scheduleFilters}
            onFiltersChange={handleScheduleFiltersChange}
            onSearch={handleScheduleSearch}
            onClearFilters={clearScheduleFilters}
            classes={classes}
            isLoading={isLoadingSchedules}
          />

          {/* Schedule Table */}
          <ScheduleTable
            schedules={schedules}
            isLoading={isLoadingSchedules}
            totalCount={schedulePagination.totalData}
            onEditSchedule={openScheduleForm}
            onDeleteSchedule={openScheduleDelete}
            onViewSchedule={openScheduleDetail}
          />

          {/* Schedule Pagination - Only show when totalData > 20 */}
          {schedulePagination.totalData > 20 && (
            <SchedulePagination
              pagination={schedulePagination}
              onPageChange={handleSchedulePageChange}
              onLimitChange={handleScheduleLimitChange}
            />
          )}
        </TabsContent>
      </Tabs>

      {/* Manual Attendance Dialog */}
      <ManualAttendanceDialog
        isOpen={isManualDialogOpen}
        onClose={closeManualDialog}
        onSuccess={handleManualAttendanceSuccess}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteAttendanceDialog
        isOpen={isDeleteDialogOpen}
        onClose={closeDeleteDialog}
        record={recordToDelete}
        onConfirm={handleDeleteAttendance}
        isDeleting={isDeleting}
      />

      {/* Schedule Management Dialogs */}
      <ScheduleFormDialog
        isOpen={isScheduleFormOpen}
        onClose={closeScheduleForm}
        editingSchedule={editingSchedule}
        onSuccess={handleScheduleFormSuccess}
        classes={classes}
      />

      <ScheduleDetailDialog
        isOpen={isScheduleDetailOpen}
        onClose={closeScheduleDetail}
        schedule={scheduleToView}
        onEdit={openScheduleForm}
        onDelete={openScheduleDelete}
      />

      <DeleteScheduleDialog
        isOpen={isScheduleDeleteOpen}
        onClose={closeScheduleDelete}
        schedule={scheduleToDelete}
        onConfirm={handleDeleteSchedule}
        isDeleting={isDeletingSchedule}
      />
    </div>
  );
};
