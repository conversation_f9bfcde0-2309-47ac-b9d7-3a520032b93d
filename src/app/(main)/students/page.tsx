"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, AlertTriangle, CheckCircle, Users } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  listStudentAPI,
  addStudentAPI,
  deleteStudentAPI,
  Student,
  StudentFormData,
  PaginationMeta,
} from "@/api/student";
import { getAuthToken } from "@/utils/getAuthToken";

// Import the new components
import { StudentFilter } from "@/components/students/StudentFilter";
import { StudentTable } from "@/components/students/StudentTable";
import { StudentPagination } from "@/components/students/StudentPagination";
import { StudentFormDialog } from "@/components/students/StudentFormDialog";
import { StudentDeleteDialog } from "@/components/students/StudentDeleteDialog";

export default function StudentManagementPage() {
  // State management
  const [students, setStudents] = useState<Student[]>([]);
  const [pagination, setPagination] = useState<PaginationMeta>({
    page: 1,
    limit: 10,
    totalData: 0,
    totalPage: 0,
  });

  // Search and filter states
  const [searchName, setSearchName] = useState("");
  const [searchPhoneFather, setSearchPhoneFather] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [selectedGender, setSelectedGender] = useState("all");

  // Modal states
  const [isAddEditModalOpen, setIsAddEditModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [editingStudent, setEditingStudent] = useState<Student | null>(null);
  const [deletingStudent, setDeletingStudent] = useState<Student | null>(null);

  // Loading and error states
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Load students data
  const loadStudents = async (resetPage = false) => {
    try {
      setIsLoading(true);
      setError(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const currentPage = resetPage ? 1 : pagination.page;

      const params = {
        token,
        page: currentPage,
        limit: pagination.limit,
        ...(searchName && { search: searchName }),
        ...(searchPhoneFather && { phone_father: searchPhoneFather }),
        ...(selectedStatus !== "all" && { status: selectedStatus }),
        ...(selectedGender !== "all" && { gender: selectedGender }),
      };

      const response = await listStudentAPI(params);

      if (response.code === 1) {
        setStudents(response.data);
        setPagination(response.totalData);

        if (resetPage && response.totalData.page !== currentPage) {
          setPagination((prev) => ({ ...prev, page: currentPage }));
        }
      } else {
        throw new Error(response.mess || "Lỗi khi tải danh sách sinh viên");
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Lỗi không xác định";
      setError(errorMessage);
      console.error("Error loading students:", err);
    } finally {
      setIsLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    loadStudents();
  }, [pagination.page, pagination.limit]);

  // Search handlers
  const handleSearchChange = (
    field: "search" | "phone_father" | "status" | "gender",
    value: string
  ) => {
    switch (field) {
      case "search":
        setSearchName(value);
        break;
      case "phone_father":
        setSearchPhoneFather(value);
        break;
      case "status":
        setSelectedStatus(value);
        break;
      case "gender":
        setSelectedGender(value);
        break;
    }
  };

  const handleSearch = () => {
    loadStudents(true);
  };

  const clearSearch = () => {
    setSearchName("");
    setSearchPhoneFather("");
    setSelectedStatus("all");
    setSelectedGender("all");
    // Reset pagination to first page and reload
    setPagination((prev) => ({ ...prev, page: 1 }));
    setTimeout(() => loadStudents(true), 0);
  };

  // Pagination handlers
  const handlePageChange = (newPage: number) => {
    setPagination((prev) => ({ ...prev, page: newPage }));
  };

  const handleLimitChange = (newLimit: number) => {
    setPagination((prev) => ({ ...prev, limit: newLimit, page: 1 }));
  };

  // Modal handlers
  const openAddModal = () => {
    setEditingStudent(null);
    setIsAddEditModalOpen(true);
  };

  const openEditModal = (student: Student) => {
    setEditingStudent(student);
    setIsAddEditModalOpen(true);
  };

  const closeModal = () => {
    setIsAddEditModalOpen(false);
    setEditingStudent(null);
  };

  const openDeleteDialog = (student: Student) => {
    setDeletingStudent(student);
    setIsDeleteDialogOpen(true);
  };

  const closeDeleteDialog = () => {
    setIsDeleteDialogOpen(false);
    setDeletingStudent(null);
  };

  // Form submission handler
  const handleFormSubmit = async (formData: StudentFormData) => {
    try {
      setIsSubmitting(true);
      setError(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const studentData = {
        ...formData,
        ...(editingStudent && { id: editingStudent.id }),
      };

      const response = await addStudentAPI(token, studentData);

      if (response.code === 1) {
        setSuccess(
          editingStudent
            ? "Cập nhật sinh viên thành công!"
            : "Thêm sinh viên thành công!"
        );
        closeModal();
        loadStudents();

        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(null), 3000);
      } else {
        throw new Error(response.mess || "Lỗi khi lưu sinh viên");
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Lỗi không xác định";
      setError(errorMessage);
      console.error("Error saving student:", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Delete handler
  const handleDelete = async () => {
    if (!deletingStudent) return;

    try {
      setIsSubmitting(true);
      setError(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await deleteStudentAPI(token, deletingStudent.id);

      if (response.code === 1) {
        setSuccess("Xóa sinh viên thành công!");
        closeDeleteDialog();
        loadStudents();

        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(null), 3000);
      } else {
        throw new Error(response.mess || "Lỗi khi xóa sinh viên");
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Lỗi không xác định";
      setError(errorMessage);
      console.error("Error deleting student:", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Users className="h-8 w-8 text-blue-600" />
            Quản lý sinh viên
          </h1>
          <p className="text-gray-500">
            Quản lý thông tin sinh viên trong hệ thống
          </p>
        </div>
        <Button onClick={openAddModal} className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700">
          <Plus className="h-4 w-4" />
          Thêm sinh viên
        </Button>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-700">{error}</AlertDescription>
        </Alert>
      )}

      {/* Success Alert */}
      {success && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-700">
            {success}
          </AlertDescription>
        </Alert>
      )}

      {/* Student Filter Component */}
      <StudentFilter
        searchName={searchName}
        searchPhoneFather={searchPhoneFather}
        selectedStatus={selectedStatus}
        selectedGender={selectedGender}
        onSearchChange={handleSearchChange}
        onSearch={handleSearch}
        onClearFilters={clearSearch}
        onAddStudent={openAddModal}
      />

      {/* Student Table Component */}
      <StudentTable
        students={students}
        onEdit={openEditModal}
        onDelete={openDeleteDialog}
        isLoading={isLoading}
        totalCount={pagination.totalData}
      />

      {/* Student Pagination Component */}
      <StudentPagination
        pagination={pagination}
        onPageChange={handlePageChange}
        onLimitChange={handleLimitChange}
      />

      {/* Student Form Dialog Component */}
      <StudentFormDialog
        isOpen={isAddEditModalOpen}
        onClose={closeModal}
        editingStudent={editingStudent}
        onSubmit={handleFormSubmit}
        isSubmitting={isSubmitting}
      />

      {/* Student Delete Dialog Component */}
      <StudentDeleteDialog
        isOpen={isDeleteDialogOpen}
        onClose={closeDeleteDialog}
        student={deletingStudent}
        onConfirm={handleDelete}
        isDeleting={isSubmitting}
      />
    </div>
  );
}
