"use client";

import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Building2,
  Plus,
  Loader2,
  AlertTriangle,
  CheckCircle
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  listBuildingAPI,
  detailBuildingAPI,
  addBuildingAPI,
  deleteBuildingAPI,
  addFloorAPI,
  deleteFloorAPI,
  addRoomAPI,
  deleteRoomAPI,
  Building,
  BuildingFormData,
  FloorFormData,
  RoomFormData,
  PaginationMeta,
  Floor,
  Room
} from "@/api/building";
import { getAuthToken } from "@/utils/getAuthToken";

// Import the new components
import { BuildingFilters } from "@/components/buildings/BuildingFilters";
import { BuildingTable } from "@/components/buildings/BuildingTable";
import { BuildingPagination } from "@/components/buildings/BuildingPagination";
import { BuildingFormDialog } from "@/components/buildings/BuildingFormDialog";
import { BuildingDeleteDialog } from "@/components/buildings/BuildingDeleteDialog";
import { FloorFormDialog } from "@/components/buildings/FloorFormDialog";
import { FloorDeleteDialog } from "@/components/buildings/FloorDeleteDialog";
import { RoomFormDialog } from "@/components/buildings/RoomFormDialog";
import { RoomDeleteDialog } from "@/components/buildings/RoomDeleteDialog";

export default function BuildingManagementPage() {
  // State management
  const [buildings, setBuildings] = useState<Building[]>([]);
  const [buildingDetails, setBuildingDetails] = useState<Record<number, { floors: Floor[] }>>({});
  const [pagination, setPagination] = useState<PaginationMeta>({
    page: 1,
    limit: 10,
    totalData: 0,
    totalPage: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Search and filter state
  const [searchName, setSearchName] = useState("");

  // Modal state
  const [isAddEditModalOpen, setIsAddEditModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [editingBuilding, setEditingBuilding] = useState<Building | null>(null);
  const [deletingBuilding, setDeletingBuilding] = useState<Building | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Floor modal state
  const [isFloorFormOpen, setIsFloorFormOpen] = useState(false);
  const [isFloorDeleteOpen, setIsFloorDeleteOpen] = useState(false);
  const [editingFloor, setEditingFloor] = useState<Floor | null>(null);
  const [deletingFloor, setDeletingFloor] = useState<Floor | null>(null);
  const [floorBuildingId, setFloorBuildingId] = useState<number>(0);
  const [floorBuildingName, setFloorBuildingName] = useState<string>("");

  // Room modal state
  const [isRoomFormOpen, setIsRoomFormOpen] = useState(false);
  const [isRoomDeleteOpen, setIsRoomDeleteOpen] = useState(false);
  const [editingRoom, setEditingRoom] = useState<Room | null>(null);
  const [deletingRoom, setDeletingRoom] = useState<Room | null>(null);
  const [roomFloorId, setRoomFloorId] = useState<number>(0);
  const [roomFloorName, setRoomFloorName] = useState<string>("");
  const [roomBuildingName, setRoomBuildingName] = useState<string>("");

  // Load buildings on component mount
  useEffect(() => {
    fetchBuildings();
  }, [pagination.page, pagination.limit]);

  const fetchBuildings = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await listBuildingAPI({
        token,
        name: searchName || undefined,
        page: pagination.page,
        limit: pagination.limit
      });

      // Note: API returns code: 1 for success
      if (response.code === 1 && response.data) {
        setBuildings(response.data);
        if (response.totalData) {
          setPagination(response.totalData);
        }
      } else {
        throw new Error(response.mess || "Không thể lấy danh sách tòa nhà");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsLoading(false);
    }
  };

  // Load building details for expanded buildings
  const loadBuildingDetails = async (buildingId: number) => {
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await detailBuildingAPI(token, buildingId);

      if (response.code === 1 && response.data) {
        setBuildingDetails(prev => ({
          ...prev,
          [buildingId]: { floors: response.data.floors }
        }));
      }
    } catch (err) {
      console.error("Error loading building details:", err);
    }
  };

  // Search functionality
  const handleSearch = () => {
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchBuildings();
  };

  const clearSearch = () => {
    setSearchName("");
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchBuildings();
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  const handleLimitChange = (limit: number) => {
    setPagination(prev => ({ ...prev, limit, page: 1 }));
  };

  // Search and filter handlers
  const handleSearchChange = (_field: 'name', value: string) => {
    setSearchName(value);
  };

  // Modal handlers
  const openAddModal = () => {
    setEditingBuilding(null);
    setIsAddEditModalOpen(true);
  };

  const openEditModal = async (building: Building) => {
    try {
      setError(null);
      setEditingBuilding(building);
      setIsAddEditModalOpen(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    }
  };

  const closeModal = () => {
    setIsAddEditModalOpen(false);
    setEditingBuilding(null);
  };

  const openDeleteDialog = (building: Building) => {
    setDeletingBuilding(building);
    setIsDeleteDialogOpen(true);
  };

  const closeDeleteDialog = () => {
    setIsDeleteDialogOpen(false);
    setDeletingBuilding(null);
  };

  // Form submission handler for the dialog component
  const handleFormSubmit = async (formData: BuildingFormData) => {
    try {
      setIsSubmitting(true);
      setError(null);
      setSuccess(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const submitData = {
        ...(editingBuilding && { id: editingBuilding.id }),
        name: formData.name,
      };

      const response = await addBuildingAPI(token, submitData);

      // Note: API returns code: 1 for success
      if (response.code === 1 && response.data) {
        setSuccess(editingBuilding ? "Cập nhật tòa nhà thành công!" : "Thêm tòa nhà thành công!");
        closeModal();
        fetchBuildings(); // Refresh the list
      } else {
        throw new Error(response.mess || "Không thể lưu tòa nhà");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async () => {
    if (!deletingBuilding) return;

    try {
      setError(null);
      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await deleteBuildingAPI(token, deletingBuilding.id);

      // Note: API returns code: 1 for success
      if (response.code === 1) {
        setSuccess("Xóa tòa nhà thành công!");
        closeDeleteDialog();
        fetchBuildings(); // Refresh the list
      } else {
        throw new Error(response.mess || "Không thể xóa tòa nhà");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
      closeDeleteDialog();
    }
  };

  // Floor handlers
  const handleAddFloor = (buildingId: number, buildingName: string) => {
    setEditingFloor(null);
    setFloorBuildingId(buildingId);
    setFloorBuildingName(buildingName);
    setIsFloorFormOpen(true);
  };

  const handleEditFloor = (floor: Floor, buildingName: string) => {
    setEditingFloor(floor);
    setFloorBuildingId(floor.building_id);
    setFloorBuildingName(buildingName);
    setIsFloorFormOpen(true);
  };

  const handleDeleteFloor = (floor: Floor, buildingName: string) => {
    setDeletingFloor(floor);
    setFloorBuildingName(buildingName);
    setIsFloorDeleteOpen(true);
  };

  const handleFloorFormSubmit = async (formData: FloorFormData) => {
    try {
      setIsSubmitting(true);
      setError(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const submitData = {
        ...(editingFloor && { id: editingFloor.id }),
        name: formData.name,
        building_id: formData.building_id,
      };

      const response = await addFloorAPI(token, submitData);

      if (response.code === 1) {
        setSuccess(editingFloor ? "Cập nhật tầng thành công!" : "Thêm tầng thành công!");
        setIsFloorFormOpen(false);
        setEditingFloor(null);
        // Refresh building details
        loadBuildingDetails(formData.building_id);
      } else {
        throw new Error(response.mess || "Không thể lưu tầng");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFloorDelete = async () => {
    if (!deletingFloor) return;

    try {
      setError(null);
      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await deleteFloorAPI(token, deletingFloor.id);

      if (response.code === 1) {
        setSuccess("Xóa tầng thành công!");
        setIsFloorDeleteOpen(false);
        setDeletingFloor(null);
        // Refresh building details
        loadBuildingDetails(deletingFloor.building_id);
      } else {
        throw new Error(response.mess || "Không thể xóa tầng");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
      setIsFloorDeleteOpen(false);
      setDeletingFloor(null);
    }
  };

  // Room handlers
  const handleAddRoom = (floorId: number, floorName: string, buildingName: string) => {
    setEditingRoom(null);
    setRoomFloorId(floorId);
    setRoomFloorName(floorName);
    setRoomBuildingName(buildingName);
    setIsRoomFormOpen(true);
  };

  const handleEditRoom = (room: Room, floorName: string, floorId: number, buildingName: string) => {
    setEditingRoom(room);
    setRoomFloorId(floorId);
    setRoomFloorName(floorName);
    setRoomBuildingName(buildingName);
    setIsRoomFormOpen(true);
  };

  const handleDeleteRoom = (room: Room, floorName: string, buildingName: string) => {
    setDeletingRoom(room);
    setRoomFloorName(floorName);
    setRoomBuildingName(buildingName);
    setIsRoomDeleteOpen(true);
  };

  const handleRoomFormSubmit = async (formData: RoomFormData) => {
    try {
      setIsSubmitting(true);
      setError(null);

      console.log(formData);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const submitData = {
        ...(editingRoom && { id: editingRoom.id }),
        name: formData.name,
        status: formData.status,
        floor_id: formData.floor_id,
      };

      const response = await addRoomAPI(token, submitData);

      if (response.code === 1) {
        setSuccess(editingRoom ? "Cập nhật phòng học thành công!" : "Thêm phòng học thành công!");
        setIsRoomFormOpen(false);
        setEditingRoom(null);
        // Find building ID to refresh details
        const buildingId = Object.keys(buildingDetails).find(id =>
          buildingDetails[parseInt(id)].floors.some(floor => floor.id === formData.floor_id)
        );
        if (buildingId) {
          loadBuildingDetails(parseInt(buildingId));
        }
      } else {
        throw new Error(response.mess || "Không thể lưu phòng học");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRoomDelete = async () => {
    if (!deletingRoom) return;

    try {
      setError(null);
      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await deleteRoomAPI(token, deletingRoom.id);

      if (response.code === 1) {
        setSuccess("Xóa phòng học thành công!");
        setIsRoomDeleteOpen(false);
        setDeletingRoom(null);
        // Find building ID to refresh details
        const buildingId = Object.keys(buildingDetails).find(id =>
          buildingDetails[parseInt(id)].floors.some(floor =>
            floor.rooms?.some(room => room.id === deletingRoom.id)
          )
        );
        if (buildingId) {
          loadBuildingDetails(parseInt(buildingId));
        }
      } else {
        throw new Error(response.mess || "Không thể xóa phòng học");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
      setIsRoomDeleteOpen(false);
      setDeletingRoom(null);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-800 flex items-center gap-2">
            <Building2 className="h-8 w-8 text-blue-600" />
            Quản lý Tòa nhà
          </h1>
        </div>
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">Đang tải dữ liệu...</span>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-800 flex items-center gap-2">
          <Building2 className="h-8 w-8 text-blue-600" />
          Quản lý Sơ đồ trường
        </h1>
        <Button onClick={openAddModal} className="bg-blue-600 hover:bg-blue-700">
          <Plus className="h-4 w-4 mr-2" />
          Thêm tòa nhà
        </Button>
      </div>

      {/* Error/Success Messages */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-700">{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-700">{success}</AlertDescription>
        </Alert>
      )}

      {/* Building Filters Component */}
      <BuildingFilters
        searchName={searchName}
        onSearchChange={handleSearchChange}
        onSearch={handleSearch}
        onClearFilters={clearSearch}
      />

      {/* Building Table Component */}
      <BuildingTable
        buildings={buildings}
        buildingDetails={buildingDetails}
        onEdit={openEditModal}
        onDelete={openDeleteDialog}
        onLoadBuildingDetails={loadBuildingDetails}
        onAddFloor={handleAddFloor}
        onEditFloor={handleEditFloor}
        onDeleteFloor={handleDeleteFloor}
        onAddRoom={handleAddRoom}
        onEditRoom={handleEditRoom}
        onDeleteRoom={handleDeleteRoom}
        isLoading={false}
        totalCount={pagination.totalData}
      />

      {/* Building Pagination Component */}
      <BuildingPagination
        pagination={pagination}
        onPageChange={handlePageChange}
        onLimitChange={handleLimitChange}
      />

      {/* Building Form Dialog Component */}
      <BuildingFormDialog
        isOpen={isAddEditModalOpen}
        onClose={closeModal}
        editingBuilding={editingBuilding}
        onSubmit={handleFormSubmit}
        isSubmitting={isSubmitting}
      />

      {/* Building Delete Dialog Component */}
      <BuildingDeleteDialog
        isOpen={isDeleteDialogOpen}
        onClose={closeDeleteDialog}
        building={deletingBuilding}
        onConfirm={handleDelete}
        isDeleting={false}
      />

      {/* Floor Form Dialog Component */}
      <FloorFormDialog
        isOpen={isFloorFormOpen}
        onClose={() => setIsFloorFormOpen(false)}
        editingFloor={editingFloor}
        buildingId={floorBuildingId}
        buildingName={floorBuildingName}
        onSubmit={handleFloorFormSubmit}
        isSubmitting={isSubmitting}
      />

      {/* Floor Delete Dialog Component */}
      <FloorDeleteDialog
        isOpen={isFloorDeleteOpen}
        onClose={() => setIsFloorDeleteOpen(false)}
        floor={deletingFloor}
        buildingName={floorBuildingName}
        onConfirm={handleFloorDelete}
        isDeleting={false}
      />

      {/* Room Form Dialog Component */}
      <RoomFormDialog
        isOpen={isRoomFormOpen}
        onClose={() => setIsRoomFormOpen(false)}
        editingRoom={editingRoom}
        floorId={roomFloorId}
        floorName={roomFloorName}
        buildingName={roomBuildingName}
        onSubmit={handleRoomFormSubmit}
        isSubmitting={isSubmitting}
      />

      {/* Room Delete Dialog Component */}
      <RoomDeleteDialog
        isOpen={isRoomDeleteOpen}
        onClose={() => setIsRoomDeleteOpen(false)}
        room={deletingRoom}
        floorName={roomFloorName}
        buildingName={roomBuildingName}
        onConfirm={handleRoomDelete}
        isDeleting={false}
      />
    </div>
  );
}