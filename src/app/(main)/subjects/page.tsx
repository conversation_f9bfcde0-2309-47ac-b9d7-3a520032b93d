"use client";

import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  BookOpen,
  Plus,
  Loader2,
  AlertTriangle,
  CheckCircle
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  listProfessionalTeamAPI,
  detailProfessionalTeamAPI,
  addProfessionalTeamAPI,
  deleteProfessionalTeamAPI,
  addTeacherToProfessionalTeamAPI,
  removeTeacherFromProfessionalTeamAPI,
  ProfessionalTeam,
  ProfessionalTeamFormData,
  PaginationMeta,
  Teacher
} from "@/api/professionalTeam";
import { listTeacherAPI, Teacher as TeacherType } from "@/api/teacher";
import { getAuthToken } from "@/utils/getAuthToken";

// Import the new components
import { ProfessionalTeamFilters } from "@/components/professional-teams/ProfessionalTeamFilters";
import { ProfessionalTeamTable } from "@/components/professional-teams/ProfessionalTeamTable";
import { ProfessionalTeamPagination } from "@/components/professional-teams/ProfessionalTeamPagination";
import { ProfessionalTeamFormDialog } from "@/components/professional-teams/ProfessionalTeamFormDialog";
import { ProfessionalTeamDeleteDialog } from "@/components/professional-teams/ProfessionalTeamDeleteDialog";
import { TeacherAssignmentDialog } from "@/components/professional-teams/TeacherAssignmentDialog";
import { TeacherRemovalDialog } from "@/components/professional-teams/TeacherRemovalDialog";

export default function ProfessionalTeamManagementPage() {
  // State management
  const [teams, setTeams] = useState<ProfessionalTeam[]>([]);
  const [teamDetails, setTeamDetails] = useState<Record<number, Teacher[]>>({});
  const [allTeachers, setAllTeachers] = useState<TeacherType[]>([]);
  const [pagination, setPagination] = useState<PaginationMeta>({
    page: 1,
    limit: 10,
    totalData: 0,
    totalPage: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Search and filter state
  const [searchName, setSearchName] = useState("");
  const [searchId, setSearchId] = useState("");

  // Teacher management state
  const [isTeacherAssignmentDialogOpen, setIsTeacherAssignmentDialogOpen] = useState(false);
  const [isTeacherRemovalDialogOpen, setIsTeacherRemovalDialogOpen] = useState(false);
  const [selectedTeamForAssignment, setSelectedTeamForAssignment] = useState<ProfessionalTeam | null>(null);
  const [selectedTeacherForRemoval, setSelectedTeacherForRemoval] = useState<Teacher | null>(null);
  const [selectedTeamForRemoval, setSelectedTeamForRemoval] = useState<ProfessionalTeam | null>(null);
  const [isAssigningTeacher, setIsAssigningTeacher] = useState(false);
  const [isRemovingTeacher, setIsRemovingTeacher] = useState(false);

  // Modal state
  const [isAddEditModalOpen, setIsAddEditModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [editingTeam, setEditingTeam] = useState<ProfessionalTeam | null>(null);
  const [deletingTeam, setDeletingTeam] = useState<ProfessionalTeam | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load teams and teachers on component mount
  useEffect(() => {
    fetchTeams();
    fetchAllTeachers();
  }, [pagination.page, pagination.limit]);

  const fetchAllTeachers = async () => {
    try {
      const token = getAuthToken();
      if (!token) return;

      const response = await listTeacherAPI({
        token,
        limit: 1000 // Get all teachers for assignment
      });

      if (response.code === 0 && response.data) {
        setAllTeachers(response.data);
      }
    } catch (err) {
      console.error("Error loading teachers:", err);
    }
  };

  const fetchTeams = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await listProfessionalTeamAPI({
        token,
        name: searchName || undefined,
        id: searchId ? parseInt(searchId) : undefined,
        page: pagination.page,
        limit: pagination.limit
      });

      // Note: API returns code: 1 for success (not 0)
      if (response.code === 1 && response.data) {
        setTeams(response.data);
        if (response.totalData) {
          setPagination(response.totalData);
        }
      } else {
        throw new Error(response.mess || "Không thể lấy danh sách tổ chuyên môn");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsLoading(false);
    }
  };

  // Load team details for expanded teams
  const loadTeamDetails = async (teamId: number) => {
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await detailProfessionalTeamAPI(token, teamId);

      if (response.code === 1 && response.data) {
        setTeamDetails(prev => ({
          ...prev,
          [teamId]: response.data.teachers
        }));
      }
    } catch (err) {
      console.error("Error loading team details:", err);
    }
  };

  // Search functionality
  const handleSearch = () => {
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchTeams();
  };

  const clearSearch = () => {
    setSearchName("");
    setSearchId("");
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchTeams();
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  const handleLimitChange = (limit: number) => {
    setPagination(prev => ({ ...prev, limit, page: 1 }));
  };

  // Search and filter handlers
  const handleSearchChange = (field: 'name' | 'id', value: string) => {
    switch (field) {
      case 'name':
        setSearchName(value);
        break;
      case 'id':
        setSearchId(value);
        break;
    }
  };

  // Modal handlers
  const openAddModal = () => {
    setEditingTeam(null);
    setIsAddEditModalOpen(true);
  };

  const openEditModal = async (team: ProfessionalTeam) => {
    try {
      setError(null);
      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      // For professional teams, we can directly use the team data for editing
      setEditingTeam(team);
      setIsAddEditModalOpen(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    }
  };

  const closeModal = () => {
    setIsAddEditModalOpen(false);
    setEditingTeam(null);
  };

  const openDeleteDialog = (team: ProfessionalTeam) => {
    setDeletingTeam(team);
    setIsDeleteDialogOpen(true);
  };

  const closeDeleteDialog = () => {
    setIsDeleteDialogOpen(false);
    setDeletingTeam(null);
  };

  // Teacher management handlers
  const openTeacherAssignmentDialog = (team: ProfessionalTeam) => {
    setSelectedTeamForAssignment(team);
    setIsTeacherAssignmentDialogOpen(true);
  };

  const closeTeacherAssignmentDialog = () => {
    setIsTeacherAssignmentDialogOpen(false);
    setSelectedTeamForAssignment(null);
  };

  const openTeacherRemovalDialog = (teacher: Teacher, team: ProfessionalTeam) => {
    setSelectedTeacherForRemoval(teacher);
    setSelectedTeamForRemoval(team);
    setIsTeacherRemovalDialogOpen(true);
  };

  const closeTeacherRemovalDialog = () => {
    setIsTeacherRemovalDialogOpen(false);
    setSelectedTeacherForRemoval(null);
    setSelectedTeamForRemoval(null);
  };

  const handleAssignTeacher = async (teacherId: number) => {
    if (!selectedTeamForAssignment) return;

    try {
      setIsAssigningTeacher(true);
      setError(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await addTeacherToProfessionalTeamAPI({
        token,
        id_professional_team: selectedTeamForAssignment.id,
        id_teacher: teacherId
      });

      if (response.code === 1) {
        setSuccess("Thêm giáo viên vào tổ chuyên môn thành công!");
        closeTeacherAssignmentDialog();
        // Refresh team details to show the new teacher
        loadTeamDetails(selectedTeamForAssignment.id);
      } else {
        throw new Error(response.mess || "Không thể thêm giáo viên vào tổ chuyên môn");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsAssigningTeacher(false);
    }
  };

  const handleRemoveTeacher = async () => {
    if (!selectedTeacherForRemoval || !selectedTeamForRemoval) return;

    try {
      setIsRemovingTeacher(true);
      setError(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await removeTeacherFromProfessionalTeamAPI({
        token,
        id_teacher: selectedTeacherForRemoval.id
      });

      if (response.code === 1) {
        setSuccess("Xóa giáo viên khỏi tổ chuyên môn thành công!");
        closeTeacherRemovalDialog();
        // Refresh team details to remove the teacher
        loadTeamDetails(selectedTeamForRemoval.id);
      } else {
        throw new Error(response.mess || "Không thể xóa giáo viên khỏi tổ chuyên môn");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsRemovingTeacher(false);
    }
  };

  // Form submission handler for the dialog component
  const handleFormSubmit = async (formData: ProfessionalTeamFormData) => {
    try {
      setIsSubmitting(true);
      setError(null);
      setSuccess(null);

      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const submitData = {
        ...(editingTeam && { id: editingTeam.id }),
        name: formData.name,
      };

      const response = await addProfessionalTeamAPI(token, submitData);

      // Note: API returns code: 1 for success (not 0)
      if (response.code === 1 && response.data) {
        setSuccess(editingTeam ? "Cập nhật tổ chuyên môn thành công!" : "Thêm tổ chuyên môn thành công!");
        closeModal();
        fetchTeams(); // Refresh the list
      } else {
        throw new Error(response.mess || "Không thể lưu tổ chuyên môn");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async () => {
    if (!deletingTeam) return;

    try {
      setError(null);
      const token = getAuthToken();
      if (!token) {
        throw new Error("Không tìm thấy token xác thực");
      }

      const response = await deleteProfessionalTeamAPI(token, deletingTeam.id);

      // Note: API returns code: 1 for success (not 0)
      if (response.code === 1) {
        setSuccess("Xóa tổ chuyên môn thành công!");
        closeDeleteDialog();
        fetchTeams(); // Refresh the list
      } else {
        throw new Error(response.mess || "Không thể xóa tổ chuyên môn");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Lỗi không xác định");
      closeDeleteDialog();
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-800 flex items-center gap-2">
            <BookOpen className="h-8 w-8 text-blue-600" />
            Quản lý Tổ chuyên môn
          </h1>
        </div>
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">Đang tải dữ liệu...</span>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-800 flex items-center gap-2">
          <BookOpen className="h-8 w-8 text-blue-600" />
          Quản lý Tổ chuyên môn
        </h1>
        <Button onClick={openAddModal} className="bg-blue-600 hover:bg-blue-700">
          <Plus className="h-4 w-4 mr-2" />
          Thêm tổ chuyên môn
        </Button>
      </div>

      {/* Error/Success Messages */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-700">{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-700">{success}</AlertDescription>
        </Alert>
      )}

      {/* Professional Team Filters Component */}
      <ProfessionalTeamFilters
        searchName={searchName}
        searchId={searchId}
        onSearchChange={handleSearchChange}
        onSearch={handleSearch}
        onClearFilters={clearSearch}
      />

      {/* Professional Team Table Component */}
      <ProfessionalTeamTable
        teams={teams}
        teamDetails={teamDetails}
        onEdit={openEditModal}
        onDelete={openDeleteDialog}
        onLoadTeamDetails={loadTeamDetails}
        onAddTeacher={openTeacherAssignmentDialog}
        onRemoveTeacher={openTeacherRemovalDialog}
        isLoading={false}
        totalCount={pagination.totalData}
      />

      {/* Professional Team Pagination Component */}
      <ProfessionalTeamPagination
        pagination={pagination}
        onPageChange={handlePageChange}
        onLimitChange={handleLimitChange}
      />

      {/* Professional Team Form Dialog Component */}
      <ProfessionalTeamFormDialog
        isOpen={isAddEditModalOpen}
        onClose={closeModal}
        editingTeam={editingTeam}
        onSubmit={handleFormSubmit}
        isSubmitting={isSubmitting}
      />

      {/* Professional Team Delete Dialog Component */}
      <ProfessionalTeamDeleteDialog
        isOpen={isDeleteDialogOpen}
        onClose={closeDeleteDialog}
        team={deletingTeam}
        onConfirm={handleDelete}
        isDeleting={false}
      />

      {/* Teacher Assignment Dialog Component */}
      <TeacherAssignmentDialog
        isOpen={isTeacherAssignmentDialogOpen}
        onClose={closeTeacherAssignmentDialog}
        professionalTeam={selectedTeamForAssignment}
        availableTeachers={allTeachers}
        assignedTeacherIds={selectedTeamForAssignment ? (teamDetails[selectedTeamForAssignment.id]?.map(t => t.id) || []) : []}
        onAssignTeacher={handleAssignTeacher}
        isAssigning={isAssigningTeacher}
      />

      {/* Teacher Removal Dialog Component */}
      <TeacherRemovalDialog
        isOpen={isTeacherRemovalDialogOpen}
        onClose={closeTeacherRemovalDialog}
        teacher={selectedTeacherForRemoval}
        professionalTeamName={selectedTeamForRemoval?.name || ""}
        onConfirm={handleRemoveTeacher}
        isRemoving={isRemovingTeacher}
      />
    </div>
  );
};
