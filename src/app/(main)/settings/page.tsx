"use client";

import { Settings } from "lucide-react";
import { SchoolInfoForm } from "@/components/school/SchoolInfoForm";
import { AccountInfoForm } from "@/components/account/AccountInfoForm";
import { PasswordChangeForm } from "@/components/account/PasswordChangeForm";
import { ContractInfoCard } from "@/components/account/ContractInfoCard";

export default function SettingsPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-800 flex items-center gap-2">
          <Settings className="h-8 w-8 text-blue-600" />
          C<PERSON><PERSON> đặt hệ thống
        </h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* School Information Form - Full width */}
        <div className="lg:col-span-2">
          <SchoolInfoForm />
        </div>

        {/* Account Information Form */}
        <AccountInfoForm />

        {/* Password Change Form */}
        <PasswordChangeForm />

        {/* Contract Information Card - Full width */}
        <div className="lg:col-span-2">
          <ContractInfoCard />
        </div>
      </div>
    </div>
  );
}
