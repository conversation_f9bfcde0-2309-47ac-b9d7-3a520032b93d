'use client';

import LoginForm from "@/components/auth/LoginForm";

export default function HomePage() {
  return (
    <div className="min-h-screen bg-blue-100 flex flex-col">
      {/* Header */}
      <div className="bg-blue-500 text-white p-4 flex items-center shadow-md">
        
        <div className="ml-auto text-sm hidden sm:block">
          {new Date().toLocaleDateString('vi-VN', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto flex flex-grow items-center justify-center p-4">
        <div className="flex flex-col md:flex-row items-stretch bg-white rounded-xl shadow-2xl overflow-hidden max-w-4xl w-full">
          {/* Left side - School Image */}
          <div className="w-full md:w-1/2 hidden md:block">
            <img
              src="/images/login-image.jpg" // Ảnh trường nên đặt trong thư mục /public
              alt="Trường Phoenix"
              className="w-full h-full object-cover"
            />
          </div>

          {/* Right side - Login Form */}
          <div className="w-full md:w-1/2 p-8 sm:p-12 flex flex-col justify-center">
            <div className="mb-8 text-center">
                <img 
                    src="/images/logo_phoneix.png"
                    alt="Logo Phoenix" 
                    className="h-16 w-auto mx-auto mb-4 md:hidden" // Hiển thị logo nhỏ trên mobile
                />
              <h2 className="text-2xl sm:text-3xl font-bold text-gray-800">
                Đăng nhập 
              </h2>
              <p className="text-gray-600 mt-2">Chào mừng bạn trở lại!</p>
            </div>
            
            <LoginForm />
            
            
          </div>
        </div>
      </div>
       {/* Footer minimal */}
       <footer className="text-center p-4 text-sm text-gray-600 bg-blue-50">
        © {new Date().getFullYear()} Bảo lưu mọi quyền.
      </footer>
    </div>
  );
}